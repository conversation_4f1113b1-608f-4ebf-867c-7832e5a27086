#!/bin/bash

# Demo script showing how to use API keys with external agents
# This demonstrates the RBAC system in action

set -e

echo "🚀 API Key Usage Demo"
echo "===================="

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Check if server is running
if ! curl -s http://localhost:8011/health >/dev/null 2>&1; then
    echo -e "${RED}❌ Server is not running. Please start it first:${NC}"
    echo "PORT=8011 node src/index.js"
    exit 1
fi

# Check if API keys are configured
if [ ! -f "api_keys.txt" ]; then
    echo -e "${YELLOW}⚠️  API keys not found. Run setup first:${NC}"
    echo "./setup_api_keys.sh"
    exit 1
fi

# Extract API keys from file
ADMIN_KEY=$(grep "ADMIN_API_KEY=" api_keys.txt | cut -d'=' -f2)
USER_KEY=$(grep "USER_API_KEY=" api_keys.txt | cut -d'=' -f2)
GUEST_KEY=$(grep "GUEST_API_KEY=" api_keys.txt | cut -d'=' -f2)

echo -e "${BLUE}🔑 Using API keys from api_keys.txt${NC}"
echo

# Function to test API endpoint
test_api_call() {
    local role=$1
    local api_key=$2
    local action=$3
    local args=$4
    local expected_status=$5
    
    echo -e "${BLUE}Testing: ${role} -> ${action}${NC}"
    
    response=$(curl -s -w "\n%{http_code}" \
        -H "Authorization: Bearer ${api_key}" \
        -H "Content-Type: application/json" \
        -d "{\"tool_name\":\"${action}\",\"args\":${args}}" \
        http://localhost:8011/mcp/tools/execute)
    
    status_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | head -n -1)
    
    if [ "$status_code" = "$expected_status" ]; then
        echo -e "${GREEN}✅ PASS${NC} - HTTP $status_code"
        if [ "$status_code" = "200" ]; then
            echo -e "${GREEN}   Response: $(echo "$body" | jq -r '.result // .message // .' 2>/dev/null || echo "$body")${NC}"
        fi
    else
        echo -e "${RED}❌ FAIL${NC} - Expected $expected_status, got $status_code"
        echo -e "${RED}   Response: $body${NC}"
    fi
    echo
}

# Function to check RBAC status
check_rbac_status() {
    local role=$1
    local api_key=$2
    
    echo -e "${BLUE}📊 RBAC Status for ${role}:${NC}"
    
    response=$(curl -s -H "Authorization: Bearer ${api_key}" \
        http://localhost:8011/rbac/status)
    
    echo "$response" | jq '.' 2>/dev/null || echo "$response"
    echo
}

echo -e "${YELLOW}🔍 Step 1: Check RBAC Status for Each Role${NC}"
echo "============================================="

check_rbac_status "Admin" "$ADMIN_KEY"
check_rbac_status "User" "$USER_KEY"  
check_rbac_status "Guest" "$GUEST_KEY"

echo -e "${YELLOW}🧪 Step 2: Test Safe Operations${NC}"
echo "================================"

# Test safe operations that all roles should be able to do
test_api_call "Admin" "$ADMIN_KEY" "web_search" '{"query":"test"}' "200"
test_api_call "User" "$USER_KEY" "web_search" '{"query":"test"}' "200"
test_api_call "Guest" "$GUEST_KEY" "web_search" '{"query":"test"}' "200"

echo -e "${YELLOW}🔒 Step 3: Test File Operations${NC}"
echo "==============================="

# Test file reading (admin and user should succeed, guest should fail)
test_api_call "Admin" "$ADMIN_KEY" "read_file" '{"path":"package.json"}' "200"
test_api_call "User" "$USER_KEY" "read_file" '{"path":"package.json"}' "200"
test_api_call "Guest" "$GUEST_KEY" "read_file" '{"path":"package.json"}' "403"

echo -e "${YELLOW}⚠️  Step 4: Test Dangerous Operations${NC}"
echo "====================================="

# Test dangerous operations (only admin should succeed)
test_api_call "Admin" "$ADMIN_KEY" "execute_command" '{"command":"echo Hello from admin"}' "200"
test_api_call "User" "$USER_KEY" "execute_command" '{"command":"echo This should fail"}' "403"
test_api_call "Guest" "$GUEST_KEY" "execute_command" '{"command":"echo This should also fail"}' "403"

echo -e "${YELLOW}📝 Step 5: Test File Writing${NC}"
echo "============================"

# Test file writing (only admin should succeed)
test_api_call "Admin" "$ADMIN_KEY" "write_file" '{"path":"test_admin.txt","content":"Admin was here"}' "200"
test_api_call "User" "$USER_KEY" "write_file" '{"path":"test_user.txt","content":"User should not write"}' "403"
test_api_call "Guest" "$GUEST_KEY" "write_file" '{"path":"test_guest.txt","content":"Guest should not write"}' "403"

echo -e "${YELLOW}🌐 Step 6: Test OpenWebUI Tools${NC}"
echo "==============================="

# Test OpenWebUI tools
test_api_call "Admin" "$ADMIN_KEY" "openwebui_get_models" '{}' "200"
test_api_call "User" "$USER_KEY" "openwebui_get_models" '{}' "200"
test_api_call "Guest" "$GUEST_KEY" "openwebui_get_models" '{}' "200"

# Test OpenWebUI dangerous operations
test_api_call "Admin" "$ADMIN_KEY" "openwebui_send_message" '{"message":"test"}' "200"
test_api_call "User" "$USER_KEY" "openwebui_send_message" '{"message":"test"}' "403"
test_api_call "Guest" "$GUEST_KEY" "openwebui_send_message" '{"message":"test"}' "403"

echo -e "${GREEN}🎉 Demo Complete!${NC}"
echo "================="
echo
echo -e "${BLUE}📋 Summary:${NC}"
echo -e "  • ${GREEN}Admin${NC}: Can execute ALL commands including dangerous ones"
echo -e "  • ${YELLOW}User${NC}: Can read files and search, but NOT execute commands or write files"
echo -e "  • ${RED}Guest${NC}: Can only search web and get models"
echo
echo -e "${BLUE}🔧 External Agent Configuration:${NC}"
echo
echo -e "${YELLOW}VS Code REST Client (.http file):${NC}"
cat << EOF
### Get RBAC Status
GET http://localhost:8011/rbac/status
Authorization: Bearer ${USER_KEY}

### Read File (User can do this)
POST http://localhost:8011/mcp/tools/execute
Authorization: Bearer ${USER_KEY}
Content-Type: application/json

{
  "tool_name": "read_file",
  "args": {"path": "package.json"}
}

### Execute Command (User CANNOT do this - will get 403)
POST http://localhost:8011/mcp/tools/execute
Authorization: Bearer ${USER_KEY}
Content-Type: application/json

{
  "tool_name": "execute_command",
  "args": {"command": "ls -la"}
}
EOF

echo
echo -e "${YELLOW}Cline Configuration:${NC}"
cat << EOF
{
  "apiKey": "${USER_KEY}",
  "baseUrl": "http://localhost:8011",
  "model": "gemini-pro"
}
EOF

echo
echo -e "${YELLOW}Continue Extension config.json:${NC}"
cat << EOF
{
  "models": [
    {
      "title": "Gemini CLI Wrapper",
      "provider": "openai", 
      "model": "gemini-pro",
      "apiKey": "${USER_KEY}",
      "apiBase": "http://localhost:8011/v1"
    }
  ]
}
EOF

echo
echo -e "${BLUE}🔒 Security Notes:${NC}"
echo "  • User and Guest roles CANNOT execute dangerous commands"
echo "  • Only Admin role can run execute_command, write_file, etc."
echo "  • External clients (VS Code, Cline) are automatically assigned User role"
echo "  • API keys override User-Agent detection for precise control"
echo
echo -e "${GREEN}Your VPS is now secure! 🛡️${NC}"

# Clean up test file if it was created
if [ -f "test_admin.txt" ]; then
    rm -f test_admin.txt
    echo -e "${BLUE}🧹 Cleaned up test files${NC}"
fi