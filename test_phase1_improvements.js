#!/usr/bin/env node

/**
 * Test script for Phase 1 improvements from gemini-cli-openai analysis
 * Tests Real Thinking, Content Safety, and Enhanced Token Management
 */

import axios from 'axios';

const BASE_URL = 'http://localhost:8010';
const API_KEY = 'test-key';

// Headers with VS Code User-Agent for user role
const headers = {
  'Authorization': `Bearer ${API_KEY}`,
  'User-Agent': 'Visual Studio Code',
  'Content-Type': 'application/json'
};

async function testPhase1Improvements() {
  console.log('🚀 Testing Phase 1 Improvements from gemini-cli-openai analysis...\n');

  // Test 1: Real Thinking Support
  console.log('1️⃣ Testing Real Thinking Support...');
  try {
    const response = await axios.post(`${BASE_URL}/v1/chat/completions`, {
      model: 'gemini-2.5-pro',
      messages: [
        {
          role: 'user',
          content: 'Solve this step by step: What is 15% of 240? Show your reasoning process.'
        }
      ],
      include_reasoning: true,
      thinking_budget: 1024,
      reasoning_effort: 'medium',
      stream: false
    }, { headers });
    
    console.log('✅ Real Thinking request successful!');
    console.log(`   Model: ${response.data.model}`);
    console.log(`   Has reasoning: ${!!response.data.reasoning}`);
    if (response.data.reasoning) {
      console.log(`   Reasoning length: ${response.data.reasoning.length} chars`);
      console.log(`   Reasoning preview: ${response.data.reasoning.substring(0, 100)}...`);
    }
    console.log(`   Content preview: ${response.data.choices[0].message.content.substring(0, 100)}...`);
  } catch (error) {
    console.log('❌ Real Thinking test failed:', error.response?.data?.error || error.message);
  }

  // Test 2: Content Safety Settings
  console.log('\n2️⃣ Testing Content Safety Settings...');
  try {
    const response = await axios.post(`${BASE_URL}/v1/chat/completions`, {
      model: 'gemini-2.5-flash',
      messages: [
        {
          role: 'user',
          content: 'Tell me about online safety for children.'
        }
      ],
      harassment_threshold: 'BLOCK_SOME',
      hate_speech_threshold: 'BLOCK_SOME',
      sexually_explicit_threshold: 'BLOCK_ONLY_HIGH',
      dangerous_content_threshold: 'BLOCK_ONLY_HIGH',
      stream: false
    }, { headers });
    
    console.log('✅ Content Safety request successful!');
    console.log(`   Model: ${response.data.model}`);
    console.log(`   Content length: ${response.data.choices[0].message.content.length} chars`);
    console.log(`   Safety ratings: ${response.data.safety_ratings ? 'Present' : 'Not present'}`);
  } catch (error) {
    console.log('❌ Content Safety test failed:', error.response?.data?.error || error.message);
  }

  // Test 3: Enhanced Token Management
  console.log('\n3️⃣ Testing Enhanced Token Management...');
  try {
    const response = await axios.get(`${BASE_URL}/v1/debug/cache`, { headers });
    
    console.log('✅ Token cache status retrieved!');
    console.log(`   Cache status: ${response.data.status || 'Available'}`);
    console.log(`   Total tokens: ${response.data.totalTokens || 0}`);
    if (response.data.stats) {
      console.log(`   Hit rate: ${response.data.stats.hitRate || 'N/A'}`);
      console.log(`   Total requests: ${response.data.stats.totalRequests || 0}`);
      console.log(`   Refreshes: ${response.data.stats.refreshes || 0}`);
      console.log(`   Errors: ${response.data.stats.errors || 0}`);
    }
  } catch (error) {
    console.log('❌ Token Management test failed:', error.response?.data?.error || error.message);
  }

  // Test 4: Streaming with Real Thinking
  console.log('\n4️⃣ Testing Streaming with Real Thinking...');
  try {
    const response = await axios.post(`${BASE_URL}/v1/chat/completions`, {
      model: 'gemini-2.5-flash',
      messages: [
        {
          role: 'user',
          content: 'Explain quantum computing in simple terms.'
        }
      ],
      include_reasoning: true,
      thinking_budget: 512,
      reasoning_effort: 'low',
      stream: true
    }, { 
      headers,
      responseType: 'stream'
    });
    
    console.log('✅ Streaming with Real Thinking started!');
    
    let chunkCount = 0;
    let hasThinking = false;
    let hasContent = false;
    
    response.data.on('data', (chunk) => {
      const lines = chunk.toString().split('\n');
      for (const line of lines) {
        if (line.startsWith('data: ') && line !== 'data: [DONE]') {
          try {
            const data = JSON.parse(line.substring(6));
            chunkCount++;
            
            if (data.choices[0]?.delta?.reasoning) {
              hasThinking = true;
            }
            if (data.choices[0]?.delta?.content) {
              hasContent = true;
            }
          } catch (e) {
            // Skip invalid JSON
          }
        }
      }
    });
    
    response.data.on('end', () => {
      console.log(`   Received ${chunkCount} chunks`);
      console.log(`   Has thinking content: ${hasThinking}`);
      console.log(`   Has regular content: ${hasContent}`);
    });
    
    // Wait for stream to complete
    await new Promise((resolve) => {
      response.data.on('end', resolve);
      setTimeout(resolve, 10000); // 10 second timeout
    });
    
  } catch (error) {
    console.log('❌ Streaming test failed:', error.response?.data?.error || error.message);
  }

  // Test 5: Model Capabilities Check
  console.log('\n5️⃣ Testing Enhanced Model Capabilities...');
  try {
    const response = await axios.get(`${BASE_URL}/v1/models`, { headers });
    
    console.log('✅ Models retrieved successfully!');
    const models = response.data.data || [];
    console.log(`   Total models: ${models.length}`);
    
    const thinkingModels = models.filter(m => m.capabilities?.thinking || m.realThinking);
    console.log(`   Thinking-capable models: ${thinkingModels.length}`);
    
    if (thinkingModels.length > 0) {
      console.log(`   Thinking models: ${thinkingModels.map(m => m.id).join(', ')}`);
    }
    
    const visionModels = models.filter(m => m.capabilities?.vision);
    console.log(`   Vision-capable models: ${visionModels.length}`);
    
  } catch (error) {
    console.log('❌ Model capabilities test failed:', error.response?.data?.error || error.message);
  }

  // Test 6: Safety Configuration
  console.log('\n6️⃣ Testing Safety Configuration...');
  try {
    // This would be a custom endpoint for safety config
    const response = await axios.get(`${BASE_URL}/v1/debug/safety`, { headers });
    
    console.log('✅ Safety configuration retrieved!');
    console.log(`   Safety enabled: ${response.data.enableSafety}`);
    console.log(`   Strict mode: ${response.data.strictMode}`);
    console.log(`   Available thresholds: ${response.data.availableThresholds?.join(', ')}`);
  } catch (error) {
    console.log('ℹ️ Safety configuration endpoint not available (expected for now)');
  }

  console.log('\n🎉 Phase 1 Improvements testing completed!');
  console.log('\n📊 Summary:');
  console.log('   ✅ Real Thinking Support - Native Gemini reasoning');
  console.log('   ✅ Content Safety Settings - Configurable moderation');
  console.log('   ✅ Enhanced Token Management - Better caching & error handling');
  console.log('   ✅ Streaming Support - Real thinking in streaming mode');
  console.log('   ✅ Model Capabilities - Enhanced model information');
}

// Run the tests
testPhase1Improvements().catch(console.error);