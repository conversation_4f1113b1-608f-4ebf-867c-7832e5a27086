import fetch from 'node-fetch';

const API_BASE = 'http://localhost:8010';
const API_KEY = 'test-gemini-wrapper-key-123';

const headers = {
  'Content-Type': 'application/json',
  'Authorization': `Bearer ${API_KEY}`
};

async function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function makeRequest(endpoint, options = {}) {
  const url = `${API_BASE}${endpoint}`;
  const response = await fetch(url, {
    headers,
    ...options
  });
  
  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${await response.text()}`);
  }
  
  return response.json();
}

async function testMCPInfo() {
  console.log('\n🔍 Testing MCP Server Info...');
  const info = await makeRequest('/mcp/info');
  console.log('MCP Server Info:', JSON.stringify(info, null, 2));
  return info;
}

async function testMCPTools() {
  console.log('\n🛠️ Testing MCP Tools...');
  const toolsResponse = await makeRequest('/mcp/tools');
  console.log('Available Tools:', JSON.stringify(toolsResponse, null, 2));
  return toolsResponse.tools;
}

async function testDirectToolExecution() {
  console.log('\n⚡ Testing Direct Tool Execution...');
  
  // Test web search
  console.log('Testing web search...');
  const searchResult = await makeRequest('/mcp/tools/execute', {
    method: 'POST',
    body: JSON.stringify({
      tool_name: 'web_search',
      arguments: {
        query: 'latest news about AI developments',
        max_results: 3
      },
      session_id: 'test-session-1'
    })
  });
  console.log('Search Result:', JSON.stringify(searchResult, null, 2));
  
  await delay(2000);
  
  // Test file read
  console.log('\nTesting file read...');
  const fileResult = await makeRequest('/mcp/tools/execute', {
    method: 'POST',
    body: JSON.stringify({
      tool_name: 'read_file',
      arguments: {
        path: 'package.json'
      },
      session_id: 'test-session-1'
    })
  });
  console.log('File Read Result (first 200 chars):', 
    JSON.stringify({
      ...fileResult,
      result: {
        ...fileResult.result,
        content: fileResult.result.content?.substring(0, 200) + '...'
      }
    }, null, 2));
  
  await delay(1000);
  
  // Test command execution
  console.log('\nTesting command execution...');
  const cmdResult = await makeRequest('/mcp/tools/execute', {
    method: 'POST',
    body: JSON.stringify({
      tool_name: 'execute_command',
      arguments: {
        command: 'ls -la'
      },
      session_id: 'test-session-1'
    })
  });
  console.log('Command Result:', JSON.stringify(cmdResult, null, 2));
  
  return { searchResult, fileResult, cmdResult };
}

async function testChatWithTools() {
  console.log('\n💬 Testing Chat Completions with Tool Integration...');
  
  const sessionId = 'chat-session-' + Date.now();
  
  // Test 1: Natural language tool invocation
  console.log('Test 1: Natural language tool request...');
  const response1 = await makeRequest('/chat/completions', {
    method: 'POST',
    body: JSON.stringify({
      model: 'gemini-2.5-pro',
      messages: [
        {
          role: 'user',
          content: 'Please search for the latest information about TypeScript 5.6 features'
        }
      ],
      session_id: sessionId,
      tools: [
        {
          type: 'function',
          function: {
            name: 'web_search',
            description: 'Search the web for current information',
            parameters: {
              type: 'object',
              properties: {
                query: { type: 'string' },
                max_results: { type: 'number', default: 5 }
              },
              required: ['query']
            }
          }
        }
      ]
    })
  });
  console.log('Response 1:', JSON.stringify(response1, null, 2));
  
  await delay(3000);
  
  // Test 2: Explicit tool calling
  console.log('\nTest 2: Follow-up with file analysis...');
  const response2 = await makeRequest('/chat/completions', {
    method: 'POST',
    body: JSON.stringify({
      model: 'gemini-2.5-pro',
      messages: [
        {
          role: 'user',
          content: 'Now analyze the package.json file in this project'
        }
      ],
      session_id: sessionId
    })
  });
  console.log('Response 2:', JSON.stringify(response2, null, 2));
  
  return { response1, response2, sessionId };
}

async function testStreamingWithTools() {
  console.log('\n🌊 Testing Streaming with Tools...');
  
  const sessionId = 'stream-session-' + Date.now();
  
  const response = await fetch(`${API_BASE}/chat/completions`, {
    method: 'POST',
    headers,
    body: JSON.stringify({
      model: 'gemini-2.5-pro',
      messages: [
        {
          role: 'user',
          content: 'Search for information about Node.js performance best practices and then read our package.json to understand our current setup'
        }
      ],
      stream: true,
      session_id: sessionId,
      tools: [
        {
          type: 'function',
          function: {
            name: 'web_search',
            description: 'Search the web for current information',
            parameters: {
              type: 'object',
              properties: {
                query: { type: 'string' },
                max_results: { type: 'number', default: 3 }
              },
              required: ['query']
            }
          }
        },
        {
          type: 'function',
          function: {
            name: 'read_file',
            description: 'Read a file',
            parameters: {
              type: 'object',
              properties: {
                path: { type: 'string' }
              },
              required: ['path']
            }
          }
        }
      ]
    })
  });
  
  console.log('Streaming response:');
  let chunkCount = 0;
  const decoder = new TextDecoder();
  
  for await (const chunk of response.body) {
    const text = decoder.decode(chunk);
    const lines = text.split('\n').filter(line => line.trim());
    
    for (const line of lines) {
      if (line.startsWith('data: ') && !line.includes('[DONE]')) {
        try {
          const data = JSON.parse(line.slice(6));
          chunkCount++;
          
          if (data.choices?.[0]?.delta?.tool_calls) {
            console.log(`🔧 Tool Call Chunk ${chunkCount}:`, JSON.stringify(data.choices[0].delta.tool_calls, null, 2));
          } else if (data.choices?.[0]?.delta?.content) {
            process.stdout.write(data.choices[0].delta.content);
          }
        } catch (e) {
          // Skip invalid JSON
        }
      }
    }
  }
  
  console.log(`\nReceived ${chunkCount} chunks`);
  return sessionId;
}

async function testSessionManagement() {
  console.log('\n📊 Testing Session Management...');
  
  // Create multiple sessions
  const sessions = [];
  for (let i = 0; i < 3; i++) {
    const sessionId = `test-session-${i}-${Date.now()}`;
    sessions.push(sessionId);
    
    // Create some activity in each session
    await makeRequest('/mcp/tools/execute', {
      method: 'POST',
      body: JSON.stringify({
        tool_name: 'store_memory',
        arguments: {
          key: `test-key-${i}`,
          value: `test-value-${i}`,
          ttl: 300
        },
        session_id: sessionId
      })
    });
  }
  
  // List all sessions
  console.log('Listing all sessions...');
  const allSessions = await makeRequest('/mcp/sessions');
  console.log('All Sessions:', JSON.stringify(allSessions, null, 2));
  
  // Get specific session
  console.log('\nGetting specific session...');
  const specificSession = await makeRequest(`/mcp/sessions/${sessions[0]}`);
  console.log('Specific Session:', JSON.stringify(specificSession, null, 2));
  
  // Delete a session
  console.log('\nDeleting a session...');
  const deleteResult = await makeRequest(`/mcp/sessions/${sessions[1]}`, {
    method: 'DELETE'
  });
  console.log('Delete Result:', JSON.stringify(deleteResult, null, 2));
  
  return sessions;
}

async function testAdvancedToolChaining() {
  console.log('\n🔗 Testing Advanced Tool Chaining...');
  
  const sessionId = 'chain-session-' + Date.now();
  
  // Step 1: Search for information
  console.log('Step 1: Searching for information...');
  await makeRequest('/mcp/tools/execute', {
    method: 'POST',
    body: JSON.stringify({
      tool_name: 'web_search',
      arguments: {
        query: 'Node.js Express.js best practices 2024',
        max_results: 3
      },
      session_id: sessionId
    })
  });
  
  // Step 2: Store findings in memory
  console.log('Step 2: Storing findings...');
  await makeRequest('/mcp/tools/execute', {
    method: 'POST',
    body: JSON.stringify({
      tool_name: 'store_memory',
      arguments: {
        key: 'research_findings',
        value: 'Found information about Express.js best practices',
        ttl: 600
      },
      session_id: sessionId
    })
  });
  
  // Step 3: Analyze current project structure
  console.log('Step 3: Analyzing project...');
  await makeRequest('/mcp/tools/execute', {
    method: 'POST',
    body: JSON.stringify({
      tool_name: 'analyze_code',
      arguments: {
        path: 'src',
        analysis_type: 'structure'
      },
      session_id: sessionId
    })
  });
  
  // Step 4: Use all context in a chat completion
  console.log('Step 4: Generating recommendations...');
  const finalResponse = await makeRequest('/chat/completions', {
    method: 'POST',
    body: JSON.stringify({
      model: 'gemini-2.5-pro',
      messages: [
        {
          role: 'user',
          content: 'Based on the research findings and project analysis, provide specific recommendations for improving this Express.js project'
        }
      ],
      session_id: sessionId
    })
  });
  
  console.log('Final Recommendations:', JSON.stringify(finalResponse, null, 2));
  
  // Get session context to see all the accumulated data
  const finalContext = await makeRequest(`/mcp/sessions/${sessionId}`);
  console.log('Final Session Context:', JSON.stringify(finalContext, null, 2));
  
  return { sessionId, finalResponse, finalContext };
}

async function runMCPDemo() {
  console.log('🚀 Starting Comprehensive MCP Demo...');
  
  try {
    // Test basic MCP functionality
    await testMCPInfo();
    await testMCPTools();
    
    // Test direct tool execution
    await testDirectToolExecution();
    await delay(2000);
    
    // Test chat integration
    await testChatWithTools();
    await delay(3000);
    
    // Test streaming
    await testStreamingWithTools();
    await delay(2000);
    
    // Test session management
    await testSessionManagement();
    await delay(1000);
    
    // Test advanced tool chaining
    await testAdvancedToolChaining();
    
    console.log('\n✅ MCP Demo completed successfully!');
    
  } catch (error) {
    console.error('❌ MCP Demo failed:', error.message);
    if (error.stack) {
      console.error('Stack trace:', error.stack);
    }
  }
}

// Check if server is running
async function checkServer() {
  try {
    const response = await fetch(`${API_BASE}/health`);
    if (response.ok) {
      console.log('✅ Server is running');
      return true;
    }
  } catch (error) {
    console.log('❌ Server is not running. Please start the server first.');
    console.log('Run: npm start');
    return false;
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  checkServer().then(isRunning => {
    if (isRunning) {
      runMCPDemo();
    }
  });
}

export { runMCPDemo }; 