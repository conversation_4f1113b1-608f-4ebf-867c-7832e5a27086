import axios from 'axios';
import fs from 'fs';
import path from 'path';

// Configuration
const API_BASE_URL = 'http://localhost:8010';
const API_KEY = process.env.API_KEY || 'your-api-key-here';

const client = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Authorization': `Bearer ${API_KEY}`,
    'Content-Type': 'application/json'
  }
});

/**
 * Demo vision capabilities with base64 image
 */
async function testVisionWithBase64() {
  console.log('🖼️  Testing Vision with Base64 Image...\n');
  
  try {
    // Create a simple test image (you can replace this with actual image file)
    const testImagePath = path.join(process.cwd(), 'test-image.jpg');
    
    // Check if test image exists
    if (!fs.existsSync(testImagePath)) {
      console.log('⚠️  No test image found. Creating a placeholder...');
      console.log('   Please add a test-image.jpg file to the project root for real testing.\n');
      
      // Use a data URL for a simple red square as demo
      const response = await client.post('/v1/chat/completions', {
        model: 'gemini-2.5-flash',
        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: 'What do you see in this image?'
              },
              {
                type: 'image_url',
                image_url: {
                  url: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=='
                }
              }
            ]
          }
        ]
      });
      
      console.log('✅ Vision Response:');
      console.log(response.data.choices[0].message.content);
      return;
    }
    
    // Read and encode actual image
    const imageBuffer = fs.readFileSync(testImagePath);
    const base64Image = imageBuffer.toString('base64');
    const mimeType = 'image/jpeg'; // Adjust based on your image type
    
    const response = await client.post('/v1/chat/completions', {
      model: 'gemini-2.5-pro',
      messages: [
        {
          role: 'user',
          content: [
            {
              type: 'text',
              text: 'Please describe this image in detail. What objects, people, or scenes do you see?'
            },
            {
              type: 'image_url',
              image_url: {
                url: `data:${mimeType};base64,${base64Image}`
              }
            }
          ]
        }
      ]
    });
    
    console.log('✅ Vision Analysis:');
    console.log(response.data.choices[0].message.content);
    console.log('\n📊 Usage:', response.data.usage);
    
  } catch (error) {
    console.error('❌ Vision test failed:', error.response?.data || error.message);
  }
}

/**
 * Demo vision capabilities with image URL
 */
async function testVisionWithURL() {
  console.log('\n🌐 Testing Vision with Image URL...\n');
  
  try {
    const response = await client.post('/v1/chat/completions', {
      model: 'gemini-2.5-flash',
      messages: [
        {
          role: 'user',
          content: [
            {
              type: 'text',
              text: 'What do you see in this image? Please be specific about colors, objects, and composition.'
            },
            {
              type: 'image_url',
              image_url: {
                url: 'https://upload.wikimedia.org/wikipedia/commons/thumb/d/dd/Gfp-wisconsin-madison-the-nature-boardwalk.jpg/2560px-Gfp-wisconsin-madison-the-nature-boardwalk.jpg',
                detail: 'high'
              }
            }
          ]
        }
      ]
    });
    
    console.log('✅ Vision Analysis:');
    console.log(response.data.choices[0].message.content);
    console.log('\n📊 Usage:', response.data.usage);
    
  } catch (error) {
    console.error('❌ Vision URL test failed:', error.response?.data || error.message);
  }
}

/**
 * Demo multiple images in one conversation
 */
async function testMultipleImages() {
  console.log('\n🖼️🖼️ Testing Multiple Images...\n');
  
  try {
    const response = await client.post('/v1/chat/completions', {
      model: 'gemini-2.5-pro',
      messages: [
        {
          role: 'user',
          content: [
            {
              type: 'text',
              text: 'Compare these two images. What are the similarities and differences?'
            },
            {
              type: 'image_url',
              image_url: {
                url: 'https://upload.wikimedia.org/wikipedia/commons/thumb/5/50/Vd-Orig.png/256px-Vd-Orig.png'
              }
            },
            {
              type: 'image_url',
              image_url: {
                url: 'https://upload.wikimedia.org/wikipedia/commons/thumb/a/a7/React-icon.svg/256px-React-icon.svg.png'
              }
            }
          ]
        }
      ]
    });
    
    console.log('✅ Multiple Images Analysis:');
    console.log(response.data.choices[0].message.content);
    console.log('\n📊 Usage:', response.data.usage);
    
  } catch (error) {
    console.error('❌ Multiple images test failed:', error.response?.data || error.message);
  }
}

/**
 * Test vision endpoint directly
 */
async function testVisionEndpoint() {
  console.log('\n🔧 Testing Vision Endpoint...\n');
  
  try {
    const response = await client.post('/v1/vision/test', {
      image_url: 'https://upload.wikimedia.org/wikipedia/commons/thumb/6/6a/JavaScript-logo.png/256px-JavaScript-logo.png',
      model: 'gemini-2.5-flash'
    });
    
    console.log('✅ Vision Endpoint Response:');
    console.log(response.data.choices[0].message.content);
    
  } catch (error) {
    console.error('❌ Vision endpoint test failed:', error.response?.data || error.message);
  }
}

/**
 * Check which models support vision
 */
async function checkVisionModels() {
  console.log('\n🔍 Checking Vision-Capable Models...\n');
  
  try {
    const response = await client.get('/v1/models');
    const visionModels = response.data.data.filter(model => model.capabilities?.vision);
    
    console.log('✅ Vision-Capable Models:');
    visionModels.forEach(model => {
      console.log(`  • ${model.id} - ${model.capabilities.max_tokens} tokens`);
    });
    
  } catch (error) {
    console.error('❌ Failed to check models:', error.response?.data || error.message);
  }
}

// Run all vision demos
async function runVisionDemo() {
  console.log('🚀 Gemini CLI Wrapper - Vision Demo\n');
  console.log('=====================================\n');
  
  await checkVisionModels();
  await testVisionWithBase64();
  await testVisionWithURL();
  await testMultipleImages();
  await testVisionEndpoint();
  
  console.log('\n✨ Vision demo completed!');
  console.log('\n💡 Tips:');
  console.log('  • Use base64 encoding for reliable image processing');
  console.log('  • Supported formats: JPEG, PNG, GIF, WebP');
  console.log('  • Maximum image size: 20MB');
  console.log('  • Use gemini-2.5-pro for best vision analysis');
}

// Run the demo
if (import.meta.url === `file://${process.argv[1]}`) {
  runVisionDemo().catch(console.error);
}

export { runVisionDemo };