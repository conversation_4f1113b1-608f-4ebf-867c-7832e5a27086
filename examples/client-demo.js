import axios from 'axios';

class GeminiCLIClient {
  constructor(baseURL = 'http://localhost:8010', apiKey = null) {
    this.baseURL = baseURL;
    this.apiKey = apiKey || process.env.API_KEY;
    
    if (!this.apiKey) {
      throw new Error('API key is required. Set API_KEY environment variable or pass it to constructor.');
    }
    
    this.client = axios.create({
      baseURL: this.baseURL,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json'
      },
      timeout: 30000
    });
  }

  async healthCheck() {
    try {
      const response = await axios.get(`${this.baseURL}/health`);
      return response.data;
    } catch (error) {
      throw new Error(`Health check failed: ${error.message}`);
    }
  }

  async getModels() {
    try {
      const response = await this.client.get('/models');
      return response.data;
    } catch (error) {
      throw new Error(`Failed to get models: ${error.message}`);
    }
  }

  async chatCompletion(messages, options = {}) {
    try {
      const requestData = {
        messages,
        model: options.model || 'gemini-1.5-pro',
        ...options
      };
      
      const response = await this.client.post('/chat/completions', requestData);
      return response.data;
    } catch (error) {
      throw new Error(`Chat completion failed: ${error.message}`);
    }
  }

  async streamChatCompletion(messages, options = {}, onChunk) {
    try {
      const requestData = {
        messages,
        model: options.model || 'gemini-1.5-pro',
        stream: true,
        ...options
      };
      
      const response = await this.client.post('/chat/completions', requestData, {
        responseType: 'stream'
      });
      
      return new Promise((resolve, reject) => {
        let fullResponse = '';
        
        response.data.on('data', (chunk) => {
          const lines = chunk.toString().split('\n');
          
          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.substring(6);
              
              if (data === '[DONE]') {
                resolve(fullResponse);
                return;
              }
              
              try {
                const parsed = JSON.parse(data);
                const content = parsed.choices[0]?.delta?.content || '';
                
                if (content) {
                  fullResponse += content;
                  if (onChunk) {
                    onChunk(content, parsed);
                  }
                }
              } catch (e) {
                // Ignore parsing errors for non-JSON lines
              }
            }
          }
        });
        
        response.data.on('error', reject);
      });
    } catch (error) {
      throw new Error(`Streaming chat completion failed: ${error.message}`);
    }
  }
}

// Demo functions
async function runDemo() {
  console.log('🤖 Gemini CLI Wrapper Demo\n');
  
  try {
    const client = new GeminiCLIClient();
    
    // Health check
    console.log('1. Testing health check...');
    const health = await client.healthCheck();
    console.log('✅ Health:', health);
    console.log();
    
    // Get models
    console.log('2. Getting available models...');
    const models = await client.getModels();
    console.log('✅ Available models:');
    models.models.forEach(model => {
      console.log(`   - ${model.id}: ${model.description}`);
    });
    console.log();
    
    // Simple chat completion
    console.log('3. Testing chat completion...');
    const completion = await client.chatCompletion([
      { role: 'user', content: 'Hello! Can you tell me what you are?' }
    ]);
    console.log('✅ Response:', completion.choices[0].message.content);
    console.log();
    
    // Streaming chat completion
    console.log('4. Testing streaming chat completion...');
    console.log('✅ Streaming response: ');
    process.stdout.write('   ');
    
    await client.streamChatCompletion([
      { role: 'user', content: 'Count from 1 to 5 slowly, explaining each number.' }
    ], {}, (chunk) => {
      process.stdout.write(chunk);
    });
    
    console.log('\n\n✅ Demo completed successfully!');
    
  } catch (error) {
    console.error('❌ Demo failed:', error.message);
    process.exit(1);
  }
}

// Example usage functions
function exampleBasicChat() {
  return `
// Basic chat example
const client = new GeminiCLIClient();

const response = await client.chatCompletion([
  { role: 'user', content: 'Explain quantum computing in simple terms' }
]);

console.log(response.choices[0].message.content);
`;
}

function exampleStreamingChat() {
  return `
// Streaming chat example
const client = new GeminiCLIClient();

await client.streamChatCompletion([
  { role: 'user', content: 'Write a short story' }
], {}, (chunk) => {
  process.stdout.write(chunk); // Print each chunk as it arrives
});
`;
}

function exampleConversation() {
  return `
// Multi-turn conversation
const client = new GeminiCLIClient();

const messages = [
  { role: 'user', content: 'What is the capital of France?' },
  { role: 'assistant', content: 'The capital of France is Paris.' },
  { role: 'user', content: 'What is its population?' }
];

const response = await client.chatCompletion(messages);
console.log(response.choices[0].message.content);
`;
}

// Export for use as a module
export { GeminiCLIClient };

// Run demo if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  console.log('Usage examples:');
  console.log('1. Basic chat:', exampleBasicChat());
  console.log('2. Streaming chat:', exampleStreamingChat());
  console.log('3. Conversation:', exampleConversation());
  console.log('\nRunning demo...\n');
  
  runDemo().catch(console.error);
} 