import fetch from 'node-fetch';

const API_BASE = 'http://localhost:8010';
const API_KEY = 'test-gemini-wrapper-key-123';

const headers = {
  'Content-Type': 'application/json',
  'Authorization': `Bearer ${API_KEY}`
};

async function makeRequest(endpoint, options = {}) {
  const url = `${API_BASE}${endpoint}`;
  const response = await fetch(url, {
    headers,
    ...options
  });
  
  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${await response.text()}`);
  }
  
  return response.json();
}

async function testMCPFeatures() {
  console.log('🧪 Testing MCP Features...\n');
  
  try {
    // 1. Test MCP Info
    console.log('1️⃣ Testing MCP Server Info...');
    const info = await makeRequest('/mcp/info');
    console.log(`✅ MCP Server: ${info.name} v${info.version}`);
    console.log(`📦 Available Tools: ${info.tools.length}`);
    console.log(`🔧 Capabilities: ${Object.keys(info.capabilities).join(', ')}`);
    console.log(`📊 Active Sessions: ${info.activeSessions}\n`);
    
    // 2. Test Tool List
    console.log('2️⃣ Testing Tool Registry...');
    const toolsResponse = await makeRequest('/mcp/tools');
    console.log(`✅ Tools available: ${toolsResponse.tools.length}`);
    toolsResponse.tools.forEach(tool => {
      console.log(`   🛠️ ${tool.name} (${tool.category}): ${tool.description}`);
    });
    console.log();
    
    // 3. Test Direct Tool Execution - File Read
    console.log('3️⃣ Testing Direct Tool Execution - File Read...');
    const fileResult = await makeRequest('/mcp/tools/execute', {
      method: 'POST',
      body: JSON.stringify({
        tool_name: 'read_file',
        arguments: {
          path: 'package.json'
        },
        session_id: 'demo-session'
      })
    });
    console.log(`✅ File read successful: ${fileResult.result.path}`);
    console.log(`📏 File size: ${fileResult.result.size} bytes`);
    console.log(`📅 Modified: ${fileResult.result.modified}\n`);
    
    // 4. Test Memory Storage
    console.log('4️⃣ Testing Memory Storage...');
    const memoryResult = await makeRequest('/mcp/tools/execute', {
      method: 'POST',
      body: JSON.stringify({
        tool_name: 'store_memory',
        arguments: {
          key: 'demo_test',
          value: 'This is a test memory value',
          ttl: 300
        },
        session_id: 'demo-session'
      })
    });
    console.log(`✅ Memory stored: ${memoryResult.result.key}`);
    console.log(`⏰ Expires at: ${memoryResult.result.expiresAt}\n`);
    
    // 5. Test Command Execution
    console.log('5️⃣ Testing Safe Command Execution...');
    const cmdResult = await makeRequest('/mcp/tools/execute', {
      method: 'POST',
      body: JSON.stringify({
        tool_name: 'execute_command',
        arguments: {
          command: 'pwd'
        },
        session_id: 'demo-session'
      })
    });
    console.log(`✅ Command executed: ${cmdResult.result.command}`);
    console.log(`📤 Output: ${cmdResult.result.stdout}`);
    console.log(`✅ Exit code: ${cmdResult.result.exitCode}\n`);
    
    // 6. Test Code Analysis
    console.log('6️⃣ Testing Code Analysis...');
    const analysisResult = await makeRequest('/mcp/tools/execute', {
      method: 'POST',
      body: JSON.stringify({
        tool_name: 'analyze_code',
        arguments: {
          path: 'package.json',
          analysis_type: 'dependencies'
        },
        session_id: 'demo-session'
      })
    });
    console.log(`✅ Code analysis completed for: ${analysisResult.result.path}`);
    console.log(`📦 Dependencies found: ${Object.keys(analysisResult.result.dependencies || {}).length}`);
    console.log(`🔧 Dev Dependencies: ${Object.keys(analysisResult.result.devDependencies || {}).length}\n`);
    
    // 7. Test Session Management
    console.log('7️⃣ Testing Session Management...');
    const sessions = await makeRequest('/mcp/sessions');
    console.log(`✅ Total sessions: ${sessions.sessions.length}`);
    sessions.sessions.forEach(session => {
      console.log(`   📊 Session ${session.id}: ${session.toolCallsCount} tool calls`);
    });
    console.log();
    
    // 8. Test Chat with Simple Tools
    console.log('8️⃣ Testing Chat Completion with Tools...');
    const chatResult = await makeRequest('/chat/completions', {
      method: 'POST',
      body: JSON.stringify({
        model: 'gemini-2.5-pro',
        messages: [
          {
            role: 'user',
            content: 'Please read our package.json file and tell me about our project dependencies'
          }
        ],
        session_id: 'chat-demo-session',
        tools: [
          {
            type: 'function',
            function: {
              name: 'read_file',
              description: 'Read a file',
              parameters: {
                type: 'object',
                properties: {
                  path: { type: 'string' }
                },
                required: ['path']
              }
            }
          }
        ]
      })
    });
    console.log(`✅ Chat completion successful`);
    console.log(`🔧 Tool calls made: ${chatResult.tool_calls ? chatResult.tool_calls.length : 0}`);
    console.log(`💬 Response length: ${chatResult.choices[0].message.content.length} characters`);
    console.log(`📋 Content preview: ${chatResult.choices[0].message.content.substring(0, 150)}...\n`);
    
    // 9. Test System Status
    console.log('9️⃣ Testing System Status...');
    const status = await makeRequest('/status');
    console.log(`✅ System initialized: ${status.initialized}`);
    console.log(`🤖 Default model: ${status.defaultModel}`);
    console.log(`🔐 Auth method: ${status.authMethod}`);
    console.log(`🔧 MCP enabled: ${status.features.mcp}`);
    console.log(`⚡ Tool calling: ${status.features.toolCalling}\n`);
    
    console.log('🎉 All MCP tests completed successfully!');
    console.log('\n📊 Summary:');
    console.log('- ✅ MCP Server Info');
    console.log('- ✅ Tool Registry');
    console.log('- ✅ File Operations');
    console.log('- ✅ Memory Storage');
    console.log('- ✅ Command Execution');
    console.log('- ✅ Code Analysis');
    console.log('- ✅ Session Management');
    console.log('- ✅ Chat Integration');
    console.log('- ✅ System Status');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Check if server is running
async function checkServer() {
  try {
    const response = await fetch(`${API_BASE}/health`);
    if (response.ok) {
      console.log('✅ Server is running\n');
      return true;
    }
  } catch (error) {
    console.log('❌ Server is not running. Please start the server first.');
    console.log('Run: npm start\n');
    return false;
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  checkServer().then(isRunning => {
    if (isRunning) {
      testMCPFeatures();
    }
  });
} 