import axios from 'axios';

// Configuration
const API_BASE_URL = 'http://localhost:8010';
const API_KEY = process.env.API_KEY || 'your-api-key-here';

const client = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Authorization': `Bearer ${API_KEY}`,
    'Content-Type': 'application/json'
  }
});

/**
 * Demo R1 style thinking với <thinking> tags
 */
async function testR1StyleThinking() {
  console.log('🧠 Testing R1 Style Thinking (DeepSeek R1 Style)...\n');
  
  try {
    const response = await client.post('/v1/chat/completions', {
      model: 'gemini-2.5-pro',
      messages: [
        {
          role: 'user',
          content: 'Solve this math problem step by step: A rectangular garden has a length that is 3 meters more than twice its width. If the perimeter is 36 meters, what are the dimensions of the garden?'
        }
      ],
      stream: true
    }, {
      responseType: 'stream'
    });
    
    console.log('✅ R1 Style Response with <thinking> tags:\n');
    
    let buffer = '';
    response.data.on('data', (chunk) => {
      buffer += chunk.toString();
      const lines = buffer.split('\n');
      buffer = lines.pop(); // Keep incomplete line in buffer
      
      for (const line of lines) {
        if (line.startsWith('data: ') && line !== 'data: [DONE]') {
          try {
            const data = JSON.parse(line.substring(6));
            const content = data.choices[0]?.delta?.content;
            
            if (content) {
              process.stdout.write(content);
            }
          } catch (e) {
            // Ignore JSON parse errors
          }
        }
      }
    });
    
    response.data.on('end', () => {
      console.log('\n\n✨ R1 Style streaming completed!');
    });
    
    // Wait for stream to complete
    await new Promise((resolve) => {
      response.data.on('end', resolve);
    });
    
  } catch (error) {
    console.error('❌ R1 Style test failed:', error.response?.data || error.message);
  }
}

/**
 * Demo R1 style với complex reasoning
 */
async function testComplexR1Reasoning() {
  console.log('\n🔬 Testing Complex R1 Style Reasoning...\n');
  
  try {
    const response = await client.post('/v1/chat/completions', {
      model: 'gemini-2.5-pro',
      messages: [
        {
          role: 'user',
          content: `You are given the following logic puzzle:

There are 5 houses in a row, each painted a different color (red, blue, green, yellow, white).
In each house lives a person of different nationality (British, Swedish, Danish, Norwegian, German).
Each person drinks a different beverage (tea, coffee, milk, beer, water).
Each person smokes a different brand of cigarettes (Pall Mall, Dunhill, Blend, Blue Master, Prince).
Each person keeps a different pet (dog, bird, cat, horse, fish).

Clues:
1. The British person lives in the red house
2. The Swedish person keeps dogs as pets
3. The Danish person drinks tea
4. The green house is on the left of the white house
5. The green house owner drinks coffee
6. The person who smokes Pall Mall rears birds
7. The owner of the yellow house smokes Dunhill
8. The person living in the center house drinks milk
9. The Norwegian lives in the first house
10. The person who smokes Blend lives next to the one who keeps cats
11. The person who keeps horses lives next to the person who smokes Dunhill
12. The person who smokes Blue Master drinks beer
13. The German smokes Prince
14. The Norwegian lives next to the blue house
15. The person who smokes Blend has a neighbor who drinks water

Who owns the fish?`
        }
      ],
      stream: true
    }, {
      responseType: 'stream'
    });
    
    console.log('✅ Complex R1 Style Reasoning:\n');
    
    let buffer = '';
    response.data.on('data', (chunk) => {
      buffer += chunk.toString();
      const lines = buffer.split('\n');
      buffer = lines.pop();
      
      for (const line of lines) {
        if (line.startsWith('data: ') && line !== 'data: [DONE]') {
          try {
            const data = JSON.parse(line.substring(6));
            const content = data.choices[0]?.delta?.content;
            
            if (content) {
              process.stdout.write(content);
            }
          } catch (e) {
            // Ignore JSON parse errors
          }
        }
      }
    });
    
    response.data.on('end', () => {
      console.log('\n\n✨ Complex reasoning completed!');
    });
    
    await new Promise((resolve) => {
      response.data.on('end', resolve);
    });
    
  } catch (error) {
    console.error('❌ Complex reasoning test failed:', error.response?.data || error.message);
  }
}

/**
 * Demo R1 style với coding problem
 */
async function testR1StyleCoding() {
  console.log('\n💻 Testing R1 Style with Coding Problem...\n');
  
  try {
    const response = await client.post('/v1/chat/completions', {
      model: 'gemini-2.5-flash',
      messages: [
        {
          role: 'user',
          content: `Write a Python function that finds the longest common subsequence (LCS) between two strings. 

Requirements:
- Use dynamic programming approach
- Include time and space complexity analysis
- Provide example usage
- Explain the algorithm step by step

Example: LCS of "ABCDGH" and "AEDFHR" should be "ADH"`
        }
      ],
      stream: true
    }, {
      responseType: 'stream'
    });
    
    console.log('✅ R1 Style Coding Solution:\n');
    
    let buffer = '';
    response.data.on('data', (chunk) => {
      buffer += chunk.toString();
      const lines = buffer.split('\n');
      buffer = lines.pop();
      
      for (const line of lines) {
        if (line.startsWith('data: ') && line !== 'data: [DONE]') {
          try {
            const data = JSON.parse(line.substring(6));
            const content = data.choices[0]?.delta?.content;
            
            if (content) {
              process.stdout.write(content);
            }
          } catch (e) {
            // Ignore JSON parse errors
          }
        }
      }
    });
    
    response.data.on('end', () => {
      console.log('\n\n✨ Coding solution completed!');
    });
    
    await new Promise((resolve) => {
      response.data.on('end', resolve);
    });
    
  } catch (error) {
    console.error('❌ Coding test failed:', error.response?.data || error.message);
  }
}

/**
 * Test non-streaming R1 style
 */
async function testR1StyleNonStreaming() {
  console.log('\n📄 Testing R1 Style (Non-streaming)...\n');
  
  try {
    const response = await client.post('/v1/chat/completions', {
      model: 'gemini-2.5-pro',
      messages: [
        {
          role: 'user',
          content: 'Explain the concept of quantum entanglement and its implications for quantum computing. Think through this systematically.'
        }
      ],
      stream: false
    });
    
    console.log('✅ R1 Style Non-streaming Response:');
    console.log(response.data.choices[0].message.content);
    
    if (response.data.reasoning) {
      console.log('\n🤔 Reasoning (if available):');
      console.log(response.data.reasoning);
    }
    
  } catch (error) {
    console.error('❌ Non-streaming test failed:', error.response?.data || error.message);
  }
}

/**
 * Check R1 configuration
 */
async function checkR1Configuration() {
  console.log('\n🔍 Checking R1 Style Configuration...\n');
  
  try {
    const response = await client.get('/v1/models');
    const thinkingModels = response.data.data.filter(model => model.capabilities?.thinking);
    
    console.log('✅ R1 Style Thinking Models:');
    thinkingModels.forEach(model => {
      console.log(`  • ${model.id} - Thinking: ${model.capabilities.thinking}`);
    });
    
    if (thinkingModels.length === 0) {
      console.log('⚠️  No thinking models available.');
      console.log('   Make sure ENABLE_FAKE_THINKING=true in .env');
    } else {
      console.log('\n💡 R1 Style Features:');
      console.log('  • <thinking> tags for reasoning process');
      console.log('  • Step-by-step problem solving');
      console.log('  • Transparent thought process');
      console.log('  • Compatible with LiteLLM and other tools');
    }
    
  } catch (error) {
    console.error('❌ Failed to check configuration:', error.response?.data || error.message);
  }
}

// Run all R1 style demos
async function runR1StyleDemo() {
  console.log('🚀 Gemini CLI Wrapper - R1 Style Thinking Demo\n');
  console.log('==============================================\n');
  
  await checkR1Configuration();
  await testR1StyleThinking();
  await testComplexR1Reasoning();
  await testR1StyleCoding();
  await testR1StyleNonStreaming();
  
  console.log('\n✨ R1 Style demo completed!');
  console.log('\n💡 R1 Style Benefits:');
  console.log('  • Transparent reasoning with <thinking> tags');
  console.log('  • Better problem-solving visibility');
  console.log('  • Compatible with existing OpenAI tools');
  console.log('  • Improved debugging and understanding');
  console.log('  • Works great with LiteLLM integration');
  
  console.log('\n🔧 Configuration:');
  console.log('  • ENABLE_FAKE_THINKING=true');
  console.log('  • STREAM_THINKING_AS_CONTENT=true');
  console.log('  • Use gemini-2.5-pro or gemini-2.5-flash models');
}

// Run the demo
if (import.meta.url === `file://${process.argv[1]}`) {
  runR1StyleDemo().catch(console.error);
}

export { runR1StyleDemo };