import axios from 'axios';

const API_BASE = 'http://localhost:8010';
const API_KEY = process.env.API_KEY || 'test-gemini-wrapper-key-123';

const client = axios.create({
  baseURL: API_BASE,
  headers: {
    'Authorization': `Bearer ${API_KEY}`,
    'Content-Type': 'application/json'
  }
});

async function testBasicTools() {
  console.log('🔧 Testing Basic Tool Capabilities\n');

  // Test 1: Web Search
  console.log('1. Testing Web Search...');
  try {
    const response = await client.post('/chat/completions', {
      model: 'gemini-2.5-pro',
      messages: [
        {role: 'user', content: 'What is the current Bitcoin price? Use web search to find this.'}
      ]
    });
    console.log('✅ Web Search Result:', response.data.choices[0].message.content.substring(0, 100) + '...\n');
  } catch (error) {
    console.error('❌ Web Search Error:', error.message);
  }

  // Test 2: Code Analysis
  console.log('2. Testing Code Analysis...');
  try {
    const response = await client.post('/chat/completions', {
      model: 'gemini-2.5-pro',
      messages: [
        {role: 'user', content: 'Analyze the package.json file in this directory and tell me about the project.'}
      ]
    });
    console.log('✅ Code Analysis Result:', response.data.choices[0].message.content.substring(0, 100) + '...\n');
  } catch (error) {
    console.error('❌ Code Analysis Error:', error.message);
  }

  // Test 3: Mathematical Reasoning
  console.log('3. Testing Mathematical Reasoning...');
  try {
    const response = await client.post('/chat/completions', {
      model: 'gemini-2.5-pro',
      messages: [
        {role: 'user', content: 'Solve this step by step: If a train travels 120km in 2 hours, and then 180km in 3 hours, what is the average speed for the entire journey?'}
      ]
    });
    console.log('✅ Math Result:', response.data.choices[0].message.content.substring(0, 150) + '...\n');
  } catch (error) {
    console.error('❌ Math Error:', error.message);
  }

  // Test 4: OpenAI-style Function Calling (experimental)
  console.log('4. Testing OpenAI-style Function Calling...');
  try {
    const response = await client.post('/chat/completions', {
      model: 'gemini-2.5-pro',
      messages: [
        {role: 'user', content: 'Search for the latest news about AI developments.'}
      ],
      tools: [
        {
          type: 'function',
          function: {
            name: 'web_search',
            description: 'Search the web for current information',
            parameters: {
              type: 'object',
              properties: {
                query: {type: 'string', description: 'Search query'}
              },
              required: ['query']
            }
          }
        }
      ]
    });
    console.log('✅ Function Calling Result:', response.data.choices[0].message.content.substring(0, 100) + '...\n');
  } catch (error) {
    console.error('❌ Function Calling Error:', error.message);
  }
}

async function testModelComparison() {
  console.log('🔍 Testing Different Models\n');

  const models = ['gemini-2.5-pro', 'gemini-1.5-pro', 'gemini-1.5-flash'];
  const prompt = 'Explain quantum computing in exactly one sentence.';

  for (const model of models) {
    console.log(`Testing ${model}...`);
    try {
      const start = Date.now();
      const response = await client.post('/chat/completions', {
        model,
        messages: [{role: 'user', content: prompt}]
      });
      const duration = Date.now() - start;
      console.log(`✅ ${model} (${duration}ms):`, response.data.choices[0].message.content);
      console.log();
    } catch (error) {
      console.error(`❌ ${model} Error:`, error.message);
    }
  }
}

async function testStreaming() {
  console.log('📡 Testing Streaming Response\n');
  
  try {
    const response = await client.post('/chat/completions', {
      model: 'gemini-2.5-pro', 
      messages: [
        {role: 'user', content: 'Count from 1 to 5 and explain each number briefly.'}
      ],
      stream: true
    }, {
      responseType: 'stream'
    });

    console.log('Streaming response:');
    return new Promise((resolve) => {
      response.data.on('data', (chunk) => {
        const lines = chunk.toString().split('\n');
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.substring(6);
            if (data === '[DONE]') {
              console.log('\n✅ Streaming completed!');
              resolve();
              return;
            }
            try {
              const parsed = JSON.parse(data);
              const content = parsed.choices[0]?.delta?.content || '';
              if (content) process.stdout.write(content);
            } catch (e) {
              // Ignore parsing errors
            }
          }
        }
      });
    });
  } catch (error) {
    console.error('❌ Streaming Error:', error.message);
  }
}

async function main() {
  console.log('🤖 Gemini CLI Wrapper - Tools & Capabilities Demo\n');
  
  try {
    // Check status first
    const status = await client.get('/status');
    console.log('📊 Current Status:');
    console.log('- Model:', status.data.defaultModel);
    console.log('- Auth:', status.data.authMethod);
    console.log('- Tools:', status.data.features.tools);
    console.log('- Function Calling:', status.data.features.toolCalling);
    console.log('- MCP Support:', status.data.features.mcp);
    console.log();

    await testBasicTools();
    await testModelComparison();
    await testStreaming();
    
    console.log('\n🎉 Demo completed successfully!');
  } catch (error) {
    console.error('❌ Demo failed:', error.message);
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { testBasicTools, testModelComparison, testStreaming }; 