import axios from 'axios';

// Configuration
const API_BASE_URL = 'http://localhost:8010';
const API_KEY = process.env.API_KEY || 'your-api-key-here';

const client = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Authorization': `Bear<PERSON> ${API_KEY}`,
    'Content-Type': 'application/json'
  }
});

/**
 * Demo thinking models with non-streaming response
 */
async function testThinkingCompletion() {
  console.log('🧠 Testing Thinking Model (Non-streaming)...\n');
  
  try {
    const response = await client.post('/v1/chat/completions', {
      model: 'gemini-2.5-pro',
      messages: [
        {
          role: 'user',
          content: 'Solve this step by step: If a train travels 120 km in 2 hours, and then 180 km in the next 3 hours, what is the average speed for the entire journey?'
        }
      ]
    });
    
    console.log('✅ Thinking Response:');
    if (response.data.reasoning) {
      console.log('🤔 Reasoning Process:');
      console.log(response.data.reasoning);
      console.log('\n💡 Final Answer:');
    }
    console.log(response.data.choices[0].message.content);
    console.log('\n📊 Usage:', response.data.usage);
    
  } catch (error) {
    console.error('❌ Thinking test failed:', error.response?.data || error.message);
  }
}

/**
 * Demo thinking models with streaming response
 */
async function testThinkingStreaming() {
  console.log('\n🌊 Testing Thinking Model (Streaming)...\n');
  
  try {
    const response = await client.post('/v1/chat/completions', {
      model: 'gemini-2.5-flash',
      messages: [
        {
          role: 'user',
          content: 'Analyze the pros and cons of renewable energy vs fossil fuels. Think through this systematically.'
        }
      ],
      stream: true
    }, {
      responseType: 'stream'
    });
    
    console.log('✅ Streaming Thinking Response:');
    console.log('🤔 Reasoning + Content:\n');
    
    let buffer = '';
    response.data.on('data', (chunk) => {
      buffer += chunk.toString();
      const lines = buffer.split('\n');
      buffer = lines.pop(); // Keep incomplete line in buffer
      
      for (const line of lines) {
        if (line.startsWith('data: ') && line !== 'data: [DONE]') {
          try {
            const data = JSON.parse(line.substring(6));
            const delta = data.choices[0]?.delta;
            
            if (delta?.reasoning) {
              process.stdout.write(delta.reasoning);
            }
            if (delta?.content) {
              process.stdout.write(delta.content);
            }
          } catch (e) {
            // Ignore JSON parse errors
          }
        }
      }
    });
    
    response.data.on('end', () => {
      console.log('\n\n✨ Streaming completed!');
    });
    
    // Wait for stream to complete
    await new Promise((resolve) => {
      response.data.on('end', resolve);
    });
    
  } catch (error) {
    console.error('❌ Thinking streaming test failed:', error.response?.data || error.message);
  }
}

/**
 * Demo DeepSeek R1 style thinking with <thinking> tags
 */
async function testDeepSeekStyleThinking() {
  console.log('\n🏷️  Testing DeepSeek R1 Style Thinking...\n');
  
  // Note: This requires STREAM_THINKING_AS_CONTENT=true in environment
  try {
    const response = await client.post('/v1/chat/completions', {
      model: 'gemini-2.5-pro',
      messages: [
        {
          role: 'user',
          content: 'Write a Python function to find the longest palindromic substring in a given string. Explain your approach.'
        }
      ],
      stream: true
    }, {
      responseType: 'stream'
    });
    
    console.log('✅ DeepSeek R1 Style Response:');
    console.log('(Look for <thinking> tags in the output)\n');
    
    let buffer = '';
    response.data.on('data', (chunk) => {
      buffer += chunk.toString();
      const lines = buffer.split('\n');
      buffer = lines.pop();
      
      for (const line of lines) {
        if (line.startsWith('data: ') && line !== 'data: [DONE]') {
          try {
            const data = JSON.parse(line.substring(6));
            const content = data.choices[0]?.delta?.content;
            
            if (content) {
              process.stdout.write(content);
            }
          } catch (e) {
            // Ignore JSON parse errors
          }
        }
      }
    });
    
    response.data.on('end', () => {
      console.log('\n\n✨ DeepSeek style streaming completed!');
    });
    
    await new Promise((resolve) => {
      response.data.on('end', resolve);
    });
    
  } catch (error) {
    console.error('❌ DeepSeek style test failed:', error.response?.data || error.message);
  }
}

/**
 * Test thinking endpoint directly
 */
async function testThinkingEndpoint() {
  console.log('\n🔧 Testing Thinking Endpoint...\n');
  
  try {
    const response = await client.post('/v1/thinking/test', {
      message: 'What are the key factors to consider when designing a scalable web application?',
      model: 'gemini-2.5-pro',
      stream: false
    });
    
    console.log('✅ Thinking Endpoint Response:');
    if (response.data.reasoning) {
      console.log('🤔 Reasoning:');
      console.log(response.data.reasoning);
      console.log('\n💡 Answer:');
    }
    console.log(response.data.choices[0].message.content);
    
  } catch (error) {
    console.error('❌ Thinking endpoint test failed:', error.response?.data || error.message);
  }
}

/**
 * Check which models support thinking
 */
async function checkThinkingModels() {
  console.log('\n🔍 Checking Thinking-Capable Models...\n');
  
  try {
    const response = await client.get('/v1/models');
    const thinkingModels = response.data.data.filter(model => model.capabilities?.thinking);
    
    console.log('✅ Thinking-Capable Models:');
    thinkingModels.forEach(model => {
      console.log(`  • ${model.id} - Max tokens: ${model.capabilities.max_tokens}`);
    });
    
    if (thinkingModels.length === 0) {
      console.log('⚠️  No thinking models available. Set ENABLE_FAKE_THINKING=true in .env');
    }
    
  } catch (error) {
    console.error('❌ Failed to check models:', error.response?.data || error.message);
  }
}

/**
 * Demo complex reasoning task
 */
async function testComplexReasoning() {
  console.log('\n🧮 Testing Complex Reasoning Task...\n');
  
  try {
    const response = await client.post('/v1/chat/completions', {
      model: 'gemini-2.5-pro',
      messages: [
        {
          role: 'user',
          content: `You have 3 boxes: Box A contains 2 red balls and 3 blue balls, Box B contains 4 red balls and 1 blue ball, and Box C contains 1 red ball and 4 blue balls. 

You randomly select a box and then randomly draw a ball from it. The ball turns out to be red. 

What is the probability that it came from Box B? Show your reasoning step by step using Bayes' theorem.`
        }
      ]
    });
    
    console.log('✅ Complex Reasoning Response:');
    if (response.data.reasoning) {
      console.log('🤔 Step-by-step Reasoning:');
      console.log(response.data.reasoning);
      console.log('\n💡 Final Answer:');
    }
    console.log(response.data.choices[0].message.content);
    
  } catch (error) {
    console.error('❌ Complex reasoning test failed:', error.response?.data || error.message);
  }
}

// Run all thinking demos
async function runThinkingDemo() {
  console.log('🚀 Gemini CLI Wrapper - Thinking Demo\n');
  console.log('======================================\n');
  
  await checkThinkingModels();
  await testThinkingCompletion();
  await testThinkingStreaming();
  await testDeepSeekStyleThinking();
  await testThinkingEndpoint();
  await testComplexReasoning();
  
  console.log('\n✨ Thinking demo completed!');
  console.log('\n💡 Tips:');
  console.log('  • Set ENABLE_FAKE_THINKING=true to enable thinking models');
  console.log('  • Set STREAM_THINKING_AS_CONTENT=true for DeepSeek R1 style');
  console.log('  • Use gemini-2.5-pro for best reasoning capabilities');
  console.log('  • Thinking models show their reasoning process before answering');
}

// Run the demo
if (import.meta.url === `file://${process.argv[1]}`) {
  runThinkingDemo().catch(console.error);
}

export { runThinkingDemo };