#!/bin/bash

echo "🚀 Starting Cursor Proxy Server..."
echo "This will create a local proxy to route Cursor requests to your OpenRouter endpoint"
echo ""

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install express cors node-fetch
fi

# Start the proxy server
echo "🔧 Starting proxy server on port 3001..."
echo "Press Ctrl+C to stop"
echo ""

node cursor_proxy_server.cjs