# Hướng dẫn sử dụng Gemini CLI API từ bên ngoài

## Thông tin cơ bản

- **Base URL**: `http://**********:8010` (thay thế IP này bằng IP public của server nếu cần)
- **Port**: 8010
- **Authentication**: Bearer token (nếu được cấu hình trong .env)

## C<PERSON><PERSON> endpoints chính

### 1. Health Check

```
GET /health
```

Kiểm tra trạng thái hoạt động của server.

### 2. Models

```
GET /models
```

<PERSON><PERSON><PERSON> danh sách các models có sẵn.

```
DELETE /models
```

Xóa tất cả models (reset về mặc định).

```
POST /models
```

Thêm model tùy chỉnh.

Body:
```json
{
  "id": "custom-model-id",
  "name": "Custom Model Name",
  "description": "Model description",
  "maxTokens": 1000000
}
```

```
DELETE /models/:modelId
```

<PERSON><PERSON>a một model cụ thể.

### 3. Chat Completions

```
POST /chat/completions
```

Tạo chat completion.

Body:
```json
{
  "model": "gemini-1.5-pro",
  "messages": [
    {"role": "user", "content": "Hello, how are you?"}
  ],
  "stream": false,
  "session_id": "optional-session-id"
}
```

Nếu muốn stream response:
```json
{
  "model": "gemini-1.5-pro",
  "messages": [
    {"role": "user", "content": "Hello, how are you?"}
  ],
  "stream": true,
  "session_id": "optional-session-id"
}
```

### 4. API Keys

```
GET /api-keys
```

Lấy danh sách API keys.

```
POST /api-keys
```

Thêm API key mới.

Body:
```json
{
  "key": "your-api-key",
  "name": "Key Name"
}
```

```
DELETE /api-keys/:keyId
```

Xóa một API key.

### 5. MCP (Model Context Protocol)

```
GET /mcp/info
```

Lấy thông tin về server.

```
GET /mcp/tools
```

Lấy danh sách tools có sẵn.

```
GET /mcp/sessions/:sessionId
```

Lấy thông tin về một session cụ thể.

```
DELETE /mcp/sessions/:sessionId
```

Xóa một session.

```
POST /mcp/tools/execute
```

Thực thi một tool.

Body:
```json
{
  "tool_name": "tool_name",
  "arguments": {},
  "session_id": "optional-session-id"
}
```

## OpenAI-compatible Endpoints

API này cũng cung cấp các endpoints tương thích với OpenAI để dễ dàng tích hợp với các công cụ như Open WebUI.

### 1. List Models

```
GET /v1/models
```

Trả về danh sách models theo định dạng OpenAI.

### 2. Chat Completions

```
POST /v1/chat/completions
```

Tạo chat completion theo định dạng OpenAI.

Body (cơ bản):
```json
{
  "model": "gemini-1.5-pro",
  "messages": [
    {"role": "user", "content": "Hello, how are you?"}
  ],
  "stream": false
}
```

Body (bật Native Real Thinking – theo `gemini-cli-openai`):

```json
{
  "model": "gemini-2.5-pro",
  "messages": [
    {"role": "user", "content": "Solve this math problem step by step: What is 15% of 240?"}
  ],
  "include_reasoning": true,
  "thinking_budget": 1024,
  "reasoning_effort": "medium",
  "stream": false
}
```

Streaming (SSE):

```bash
curl -X POST http://<HOST>:8010/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gemini-2.5-pro",
    "messages": [{"role":"user","content":"Think step by step: 15% of 240?"}],
    "include_reasoning": true,
    "thinking_budget": 1024,
    "reasoning_effort": "medium",
    "stream": true
  }'
```

## Kết nối với Open WebUI

Để kết nối Gemini CLI Wrapper với Open WebUI:

1. Mở Open WebUI và đăng nhập
2. Vào Settings > Connections
3. Click "Add Connection"
4. Cấu hình như sau:
   - Connection Type: Local
   - URL: `http://**********:8010` (thay thế bằng IP của server)
   - Key: (để trống vì chúng ta đã tắt xác thực cho các endpoints OpenAI)
   - Prefix ID: (để trống)
   - Model IDs: (để trống để lấy tất cả models từ endpoint `/v1/models`)
5. Click "Save"

### Khắc phục lỗi "OpenAI: Network Problem"

Nếu vẫn gặp lỗi "OpenAI: Network Problem", hãy thử các cách sau:

1. **Kiểm tra kết nối mạng**:
   - Đảm bảo máy chạy Open WebUI có thể kết nối được với máy chủ Gemini CLI Wrapper
   - Thử ping đến địa chỉ IP của server: `ping **********`
   - Kiểm tra port có mở không: `telnet ********** 8010`

2. **Kiểm tra API**:
   - Truy cập trang test: `http://**********:8010/test.html`
   - Thử các nút để kiểm tra các endpoints khác nhau

3. **Cấu hình Open WebUI**:
   - Thử sử dụng Connection Type: "Custom OpenAI" thay vì "Local"
   - URL: `http://**********:8010/v1`
   - Đảm bảo không có dấu `/` ở cuối URL

4. **Kiểm tra logs**:
   - Xem logs của server: `pm2 logs`
   - Xem logs của Open WebUI (nếu có thể)

5. **Kiểm tra firewall**:
   - Đảm bảo port 8010 được mở trên server: `sudo ufw status`
   - Nếu firewall đang bật, mở port: `sudo ufw allow 8010`

6. **Thử với curl từ máy client**:
   ```bash
   curl http://**********:8010/v1/models
   ```

## Cách sử dụng từ các ngôn ngữ khác nhau

### JavaScript/Node.js

```javascript
const response = await fetch('http://**********:8010/chat/completions', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer YOUR_API_KEY' // Nếu cần
  },
  body: JSON.stringify({
    model: 'gemini-1.5-pro',
    messages: [
      {role: 'user', content: 'Hello, how are you?'}
    ]
  })
});

const data = await response.json();
console.log(data);
```

### Python

```python
import requests

response = requests.post(
    'http://**********:8010/chat/completions',
    headers={
        'Content-Type': 'application/json',
        'Authorization': 'Bearer YOUR_API_KEY'  # Nếu cần
    },
    json={
        'model': 'gemini-1.5-pro',
        'messages': [
            {'role': 'user', 'content': 'Hello, how are you?'}
        ]
    }
)

print(response.json())
```

### cURL

```bash
curl -X POST http://**********:8010/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "model": "gemini-1.5-pro",
    "messages": [
      {"role": "user", "content": "Hello, how are you?"}
    ]
  }'
```

## Cấu hình .env cho API Key

Nếu bạn muốn bảo vệ API bằng API key, hãy tạo file `.env` trong thư mục gốc của project với nội dung:

```
API_KEY=your_secret_api_key
PORT=8010
```

Sau đó khởi động lại server:

```bash
pm2 restart all
```

## Truy cập từ bên ngoài

Nếu bạn đang chạy server trên một máy chủ cloud, hãy đảm bảo:

1. Mở port 8010 trong firewall
2. Sử dụng địa chỉ IP public của server thay vì localhost
3. Cấu hình security groups/firewall rules để cho phép truy cập

## Lưu ý về bảo mật

- Luôn sử dụng API key trong môi trường production
- Cân nhắc sử dụng HTTPS thay vì HTTP trong môi trường production
- Giới hạn truy cập IP nếu có thể 