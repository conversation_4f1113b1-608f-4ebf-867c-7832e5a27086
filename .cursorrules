# Cursor Rules for Gemini CLI Wrapper

## Project Patterns

### File Structure
- `src/index.js`: Entry point của application
- `src/providers/`: Chứa các providers cho model (hiện tại chỉ có Gemini)
- `src/middleware/`: Chứa các middleware như authentication và error handling
- `src/utils/`: Utilities như logging
- `src/mcp/`: Model Context Protocol handler
- `src/tools/`: Tool registry và các tools
- `memory-bank/`: Chứa các file documentation và context

### Coding Patterns
- Sử dụng ES modules (import/export) thay vì CommonJS (require)
- Async/await cho asynchronous operations
- Error handling với try/catch blocks
- Structured logging với nhiều levels
- Middleware pattern cho Express.js
- Factory pattern cho Tool Registry

### Naming Conventions
- camelCase cho variables và functions
- PascalCase cho classes
- snake_case cho file names
- ALL_CAPS cho constants

## User Preferences
- Ưu tiên sử dụng ES modules
- Code comments bằng tiếng Anh
- Documentation bằng tiếng Việt
- Sử dụng async/await thay vì callbacks hoặc promises
- Logging chi tiết cho debugging

## Known Challenges
- Node.js v20+ là bắt buộc cho Gemini CLI
- Streaming có thể không ổn định trong một số trường hợp
- Gemini CLI có thể không hoạt động trong một số môi trường
- Memory usage có thể cao khi có nhiều sessions

## Implementation Paths
- Luôn kiểm tra Node.js version trước khi sử dụng Gemini CLI
- Sử dụng mock responses khi Gemini CLI gặp lỗi
- Expose API endpoints với `app.listen(PORT, '0.0.0.0', ...)`
- Sử dụng environment variables cho cấu hình
- Sử dụng structured logging với timestamp và log levels

## Tool Usage Patterns
- Sử dụng `curl` để test API endpoints
- Sử dụng `lsof -ti:PORT | xargs kill -9` để kill processes trên port
- Sử dụng `npm start` để start server
- Sử dụng `./test_api.sh` để test tất cả các endpoints

## Project Evolution
- Ban đầu chỉ hỗ trợ basic API endpoints
- Thêm streaming responses và tool calling
- Thêm session management và error handling
- Thêm external access và mock responses
- Đang cải thiện documentation và testing 