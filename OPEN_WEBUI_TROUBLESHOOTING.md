# Open WebUI Connection Troubleshooting Guide

## Current Status ✅
The Gemini CLI Wrapper is running correctly and CORS is properly configured. All API endpoints are accessible and responding correctly.

## Verified Working Endpoints
- ✅ Health check: `http://localhost:8010/health`
- ✅ Models list: `http://localhost:8010/v1/models`
- ✅ CORS preflight requests working
- ✅ CORS headers properly set for `http://localhost:3000`

## Open WebUI Configuration Steps

### 1. Correct URL Format
Make sure you're using the correct URL in Open WebUI:

**For local access (same server):**
```
http://localhost:8010/v1
```

**For external access (from external IP):**
```
http://**************:8010/v1
```

**For internal VPS access:**
```
http://**********:8010/v1
```

**NOT:**
- `http://localhost:8010` (missing /v1)
- `http://**************:8010` (missing /v1)
- `http://**********:8010` (missing /v1)

### 2. Open WebUI Settings
1. Open Open WebUI in your browser
2. Go to Settings → Connections
3. Add a new connection with:
   - **Name**: Gemini CLI Wrapper
   - **Base URL**:
     - For local access: `http://localhost:8010/v1`
     - For external access: `http://**************:8010/v1`
     - For internal VPS access: `http://**********:8010/v1`
   - **API Key**: (leave empty or use any value - auth is disabled for testing)

### 3. Browser Console Debugging
If you still get "Network Problem":
1. Open browser Developer Tools (F12)
2. Go to Console tab
3. Try to connect in Open WebUI
4. Look for error messages in the console
5. Check the Network tab for failed requests

### 4. Common Issues and Solutions

#### Issue: "Network Problem" Error
**Possible Causes:**
1. **Browser Security Policy**: Some browsers block localhost-to-localhost requests
2. **Open WebUI Configuration**: Wrong URL format
3. **Firewall/Network**: Local firewall blocking connections

**Solutions:**
1. Try using `127.0.0.1` instead of `localhost`:
   ```
   http://127.0.0.1:8010/v1
   ```

2. If using Chrome, try starting it with disabled security (for testing only):
   ```bash
   google-chrome --disable-web-security --user-data-dir=/tmp/chrome_dev_test
   ```

3. Try a different browser (Firefox, Edge, etc.)

#### Issue: CORS Errors in Browser Console
**Solution**: The CORS configuration has been updated and should work. If you still see CORS errors, restart both services:
```bash
# Restart Gemini CLI Wrapper
pm2 restart gemini-cli-wrapper

# Restart Open WebUI (if using Docker)
docker restart open-webui
```

### 5. Manual Testing
You can verify the API is working by testing these URLs directly in your browser:

**Local access:**
1. Health check: http://localhost:8010/health
2. Models list: http://localhost:8010/v1/models

**External IP access:**
1. Health check: http://**************:8010/health
2. Models list: http://**************:8010/v1/models

**Internal VPS IP access:**
1. Health check: http://**********:8010/health
2. Models list: http://**********:8010/v1/models

All URLs should return JSON responses.

### 6. Alternative: Use External IP
If localhost doesn't work, try using the server's IP address:
```
http://**********:8010/v1
```

### 7. Debug Information
Current server configuration:
- **Port**: 8010
- **Binding**: 0.0.0.0 (all interfaces)
- **CORS**: Enabled for localhost:3000 and other common ports
- **Authentication**: Disabled for /v1/ endpoints

## Testing Commands
Run these commands to verify the API is working:

**Local testing:**
```bash
# Test health endpoint
curl http://localhost:8010/health

# Test models endpoint
curl http://localhost:8010/v1/models

# Test with CORS headers (simulating browser request)
curl -H "Origin: http://localhost:3000" http://localhost:8010/v1/models

# Test preflight request
curl -X OPTIONS -H "Origin: http://localhost:3000" -H "Access-Control-Request-Method: GET" http://localhost:8010/v1/models
```

**External IP testing:**
```bash
# Test health endpoint
curl http://**************:8010/health

# Test models endpoint
curl http://**************:8010/v1/models

# Test with CORS headers (simulating browser request from external IP)
curl -H "Origin: http://**************:3000" http://**************:8010/v1/models

# Test preflight request
curl -X OPTIONS -H "Origin: http://**************:3000" -H "Access-Control-Request-Method: GET" http://**************:8010/v1/models
```

**Internal VPS IP testing:**
```bash
# Test health endpoint
curl http://**********:8010/health

# Test models endpoint
curl http://**********:8010/v1/models

# Test with CORS headers (simulating browser request from internal VPS IP)
curl -H "Origin: http://**********:3000" http://**********:8010/v1/models

# Test preflight request
curl -X OPTIONS -H "Origin: http://**********:3000" -H "Access-Control-Request-Method: GET" http://**********:8010/v1/models
```

All commands should return successful responses.

## Next Steps
1. **For local access**: Try the URL `http://localhost:8010/v1` in Open WebUI
2. **For external access**: Try the URL `http://**************:8010/v1` in Open WebUI
3. **For internal VPS access**: Try the URL `http://**********:8010/v1` in Open WebUI
4. If localhost doesn't work, try `http://127.0.0.1:8010/v1`
5. Check browser console for specific error messages
6. If still having issues, try a different browser

## ✅ Confirmed Working
- **Local access**: `http://localhost:8010/v1` ✅
- **External IP access**: `http://**************:8010/v1` ✅
- **Internal VPS IP access**: `http://**********:8010/v1` ✅
- **CORS**: Properly configured for all access methods ✅

The server is working correctly with local, external IP, and internal VPS IP access - the issue is likely in the Open WebUI configuration or browser security policies.