#!/usr/bin/env node

const express = require('express');
const cors = require('cors');
const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

const app = express();
const PORT = process.env.PORT || 3001;

// Your OpenRouter endpoint config
const OPENROUTER_ENDPOINT = 'https://kilocode.ai/api/openrouter';
const OPENROUTER_TOKEN = process.env.JWT_TOKEN || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiNjc3ZjU5YzNkNzJkNzEwMDAxNzJkNzJkIiwiaWF0IjoxNzM2NDI0OTYzLCJleHAiOjE3Mzc2MzQ1NjN9.CZTrJ3fFe8xGOmhJKlMnOpQrStUvWxYz2AbCdEfGhIk';

// Simple API key for Cursor (instead of long JWT)
const CURSOR_API_KEY = 'kilo-claude-2025';

// API key validation middleware
function validateApiKey(req, res, next) {
    const authHeader = req.headers.authorization;
    
    if (!authHeader) {
        return res.status(401).json({
            error: {
                message: 'Missing Authorization header',
                type: 'authentication_error',
                code: 'missing_api_key'
            }
        });
    }
    
    const token = authHeader.replace('Bearer ', '');
    
    // Accept either the simple API key or the full JWT token
    if (token === CURSOR_API_KEY || token === OPENROUTER_TOKEN) {
        console.log(`🔑 Valid API key: ${token.substring(0, 10)}...`);
        next();
    } else {
        console.log(`❌ Invalid API key: ${token.substring(0, 10)}...`);
        return res.status(401).json({
            error: {
                message: 'Invalid API key',
                type: 'authentication_error',
                code: 'invalid_api_key'
            }
        });
    }
}

// Middleware
app.use(cors());
app.use(express.json({ limit: '50mb' }));

// Model name mapping for Cursor compatibility
const MODEL_MAPPING = {
    // Cursor's model names -> OpenRouter model names
    'claude-4-sonnet': 'anthropic/claude-sonnet-4',
    'claude-sonnet-4': 'anthropic/claude-sonnet-4',
    'claude-sonnet-4-20250514': 'anthropic/claude-sonnet-4',
    'claude-3.5-sonnet': 'anthropic/claude-3.5-sonnet',
    'claude-3-5-sonnet': 'anthropic/claude-3.5-sonnet',
    'claude-haiku': 'anthropic/claude-3.5-haiku',
    'claude-3.5-haiku': 'anthropic/claude-3.5-haiku',
    'claude-opus': 'anthropic/claude-3-opus',
    'cursordev': 'anthropic/claude-sonnet-4', // Special alias for Cursor
    'please-enable-some-models': 'anthropic/claude-sonnet-4', // Fallback
    // Add more mappings as needed
};

function mapModelName(modelName) {
    // If it's already in OpenRouter format, return as-is
    if (modelName && modelName.includes('/')) {
        return modelName;
    }
    
    // Check direct mapping
    if (MODEL_MAPPING[modelName]) {
        console.log(`🔄 Mapped model: ${modelName} -> ${MODEL_MAPPING[modelName]}`);
        return MODEL_MAPPING[modelName];
    }
    
    // Default fallback to claude-sonnet-4 for unknown models
    console.log(`⚠️  Unknown model ${modelName}, using fallback: anthropic/claude-sonnet-4`);
    return 'anthropic/claude-sonnet-4';
}

// Log requests
app.use((req, res, next) => {
    console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
    next();
});

// Health check
app.get('/health', (req, res) => {
    res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Get models endpoint (OpenAI compatible)
app.get('/v1/models', validateApiKey, async (req, res) => {
    try {
        console.log('📋 Fetching models from OpenRouter...');
        
        const response = await fetch(`${OPENROUTER_ENDPOINT}/models`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${OPENROUTER_TOKEN}`,
                'Content-Type': 'application/json',
                'HTTP-Referer': 'https://kilocode.ai',
                'X-Title': 'Gemini CLI Wrapper'
            }
        });

        if (!response.ok) {
            throw new Error(`OpenRouter API error: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        
        // Convert to OpenAI format with tool support
        const openaiModels = data.data.map(model => ({
            id: model.id,
            object: "model",
            created: Math.floor(Date.now() / 1000),
            owned_by: model.id.split('/')[0] || 'openrouter',
            permission: [],
            root: model.id,
            parent: null,
            // Add tool support for Claude and GPT models
            capabilities: {
                function_calling: model.id.includes('claude') || model.id.includes('gpt') || model.id.includes('gemini'),
                tools: model.id.includes('claude') || model.id.includes('gpt') || model.id.includes('gemini')
            }
        }));

        // Add custom cursordev model with full tool support
        openaiModels.push({
            id: 'cursordev',
            object: "model",
            created: Math.floor(Date.now() / 1000),
            owned_by: 'kilocode',
            permission: [],
            root: 'cursordev',
            parent: null,
            capabilities: {
                function_calling: true,
                tools: true,
                vision: true
            }
        });

        const openaiFormat = {
            object: "list",
            data: openaiModels
        };

        console.log(`✅ Returned ${openaiFormat.data.length} models`);
        res.json(openaiFormat);
    } catch (error) {
        console.error('❌ Error fetching models:', error.message);
        res.status(500).json({
            error: {
                message: error.message,
                type: 'api_error',
                code: 'internal_error'
            }
        });
    }
});

// Chat completions endpoint (OpenAI compatible)
app.post('/v1/chat/completions', validateApiKey, async (req, res) => {
    try {
        console.log('💬 Processing chat completion...');
        console.log(`🤖 Original Model: ${req.body.model}`);
        console.log(`📝 Messages: ${req.body.messages?.length} messages`);
        
        // Map the model name
        const mappedModel = mapModelName(req.body.model);
        
        // Create request body with mapped model
        const requestBody = {
            ...req.body,
            model: mappedModel
        };
        
        console.log(`🎯 Using OpenRouter Model: ${mappedModel}`);
        
        // Forward request to OpenRouter
        const response = await fetch(`${OPENROUTER_ENDPOINT}/chat/completions`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${OPENROUTER_TOKEN}`,
                'Content-Type': 'application/json',
                'HTTP-Referer': 'https://kilocode.ai',
                'X-Title': 'Gemini CLI Wrapper'
            },
            body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error('❌ OpenRouter API error:', errorText);
            throw new Error(`OpenRouter API error: ${response.status} ${response.statusText}`);
        }

        // Handle streaming
        if (req.body.stream) {
            console.log('🌊 Streaming response...');
            res.setHeader('Content-Type', 'text/event-stream');
            res.setHeader('Cache-Control', 'no-cache');
            res.setHeader('Connection', 'keep-alive');
            
            response.body.pipe(res);
        } else {
            const data = await response.json();
            console.log('✅ Chat completion successful');
            res.json(data);
        }
    } catch (error) {
        console.error('❌ Error in chat completion:', error.message);
        res.status(500).json({
            error: {
                message: error.message,
                type: 'api_error',
                code: 'internal_error'
            }
        });
    }
});

// Completions endpoint (legacy OpenAI format)
app.post('/v1/completions', validateApiKey, async (req, res) => {
    try {
        console.log('📝 Processing legacy completion...');
        console.log(`🤖 Original Model: ${req.body.model}`);
        
        // Map the model name
        const mappedModel = mapModelName(req.body.model);
        console.log(`🎯 Using OpenRouter Model: ${mappedModel}`);
        
        // Convert legacy format to chat format
        const chatRequest = {
            model: mappedModel,
            messages: [
                {
                    role: 'user',
                    content: req.body.prompt
                }
            ],
            max_tokens: req.body.max_tokens,
            temperature: req.body.temperature,
            stream: req.body.stream
        };

        // Forward to chat completions
        const response = await fetch(`${OPENROUTER_ENDPOINT}/chat/completions`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${OPENROUTER_TOKEN}`,
                'Content-Type': 'application/json',
                'HTTP-Referer': 'https://kilocode.ai',
                'X-Title': 'Gemini CLI Wrapper'
            },
            body: JSON.stringify(chatRequest)
        });

        if (!response.ok) {
            throw new Error(`OpenRouter API error: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        
        // Convert back to legacy format
        const legacyFormat = {
            id: data.id,
            object: "text_completion",
            created: data.created,
            model: data.model,
            choices: data.choices.map(choice => ({
                text: choice.message.content,
                index: choice.index,
                logprobs: null,
                finish_reason: choice.finish_reason
            })),
            usage: data.usage
        };

        console.log('✅ Legacy completion successful');
        res.json(legacyFormat);
    } catch (error) {
        console.error('❌ Error in legacy completion:', error.message);
        res.status(500).json({
            error: {
                message: error.message,
                type: 'api_error',
                code: 'internal_error'
            }
        });
    }
});

// Start server
app.listen(PORT, () => {
    console.log('🚀 Cursor Proxy Server started!');
    console.log('='.repeat(50));
    console.log(`📡 Server running on: http://localhost:${PORT}`);
    console.log(`🔗 OpenAI API Base URL: http://localhost:${PORT}/v1`);
    console.log(`🎯 Target OpenRouter: ${OPENROUTER_ENDPOINT}`);
    console.log('='.repeat(50));
    console.log('');
    console.log('📋 Available endpoints:');
    console.log(`  GET  /health - Health check`);
    console.log(`  GET  /v1/models - List models`);
    console.log(`  POST /v1/chat/completions - Chat completions`);
    console.log(`  POST /v1/completions - Legacy completions`);
    console.log('');
    console.log('🔧 Cursor Configuration:');
    console.log(`  API Base URL: http://localhost:${PORT}/v1`);
    console.log(`  API Key: ${CURSOR_API_KEY}`);
    console.log(`  Model: anthropic/claude-sonnet-4`);
    console.log('');
});

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down proxy server...');
    process.exit(0);
});