# Quick API Reference - External Agents

## 🔑 Your Generated API Keys

After running `./setup_api_keys.sh`, check `api_keys.txt` for your specific keys.

## 🚀 Quick Setup for Popular Agents

### VS Code REST Client

Create a `.http` file:
```http
### Check your role
GET http://localhost:8011/rbac/status
Authorization: Bearer YOUR_USER_API_KEY

### Read a file (User can do this)
POST http://localhost:8011/mcp/tools/execute
Authorization: Bearer YOUR_USER_API_KEY
Content-Type: application/json

{
  "tool_name": "read_file",
  "args": {"path": "package.json"}
}

### Execute command (Only Admin can do this)
POST http://localhost:8011/mcp/tools/execute
Authorization: Bearer YOUR_ADMIN_API_KEY
Content-Type: application/json

{
  "tool_name": "execute_command",
  "args": {"command": "ls -la"}
}
```

### Cline Assistant

Configure in Cline settings:
```json
{
  "apiKey": "YOUR_USER_API_KEY",
  "baseUrl": "http://localhost:8011",
  "model": "gemini-pro"
}
```

### Continue Extension

Add to `~/.continue/config.json`:
```json
{
  "models": [
    {
      "title": "Gemini CLI Wrapper",
      "provider": "openai",
      "model": "gemini-pro", 
      "apiKey": "YOUR_USER_API_KEY",
      "apiBase": "http://localhost:8011/v1"
    }
  ]
}
```

### Aider

Command line usage:
```bash
aider --openai-api-key YOUR_USER_API_KEY \
      --openai-api-base http://localhost:8011/v1 \
      --model gemini-pro
```

### Cursor IDE

Add to Cursor settings:
```json
{
  "gemini-cli-wrapper.apiKey": "YOUR_USER_API_KEY",
  "gemini-cli-wrapper.baseUrl": "http://localhost:8011"
}
```

## 🔒 Role Permissions

### Admin Role (ADMIN_API_KEY)
- ✅ **All operations** including dangerous ones
- ✅ `execute_command` - Run system commands
- ✅ `write_file` - Modify files
- ✅ `read_file` - Read files
- ✅ `web_search` - Search web
- ✅ All OpenWebUI tools
- ✅ Browser automation

### User Role (USER_API_KEY) - **Recommended for most agents**
- ✅ `read_file` - Read files
- ✅ `web_search` - Search web
- ✅ `analyze_code` - Code analysis
- ✅ Safe OpenWebUI tools (get_models, web_search, etc.)
- ❌ `execute_command` - **BLOCKED**
- ❌ `write_file` - **BLOCKED**
- ❌ Dangerous OpenWebUI tools

### Guest Role (GUEST_API_KEY)
- ✅ `web_search` - Search web only
- ✅ `openwebui_get_models` - Get available models
- ❌ File operations - **BLOCKED**
- ❌ All dangerous operations - **BLOCKED**

## 🧪 Testing Your Setup

### Quick Test Commands

```bash
# Test Admin role
curl -H "Authorization: Bearer YOUR_ADMIN_API_KEY" \
     http://localhost:8011/rbac/status

# Test User role  
curl -H "Authorization: Bearer YOUR_USER_API_KEY" \
     http://localhost:8011/rbac/status

# Test permission for specific action
curl -H "Authorization: Bearer YOUR_USER_API_KEY" \
     -H "Content-Type: application/json" \
     -d '{"action":"read_file"}' \
     http://localhost:8011/rbac/test-permission
```

### Run Full Demo
```bash
./demo_api_key_usage.sh
```

## 🔄 Authentication Methods (Priority Order)

1. **API Key Mapping** (Highest Priority)
   - `Authorization: Bearer YOUR_API_KEY`
   - Overrides all other methods

2. **Custom Headers**
   - `X-User-Role: admin`
   - `X-OpenWebUI-Role: user`

3. **JWT Token**
   - Extracts role from JWT claims

4. **User-Agent Detection**
   - VS Code → user role
   - Cline → user role
   - Unknown → guest role

## 🚨 Security Notes

- **User role is recommended** for most external agents
- **Admin role should be used sparingly** and only when you need to execute system commands
- **Never commit API keys** to version control
- **Rotate keys regularly** for production use
- **Monitor logs** for unauthorized access attempts

## 🔧 Troubleshooting

### Common Issues

1. **403 Access Denied**
   - Check your role: `curl -H "Authorization: Bearer YOUR_KEY" http://localhost:8011/rbac/status`
   - Verify you're using the right key for the operation

2. **API Key Not Working**
   - Restart server after changing `.env`
   - Check `API_KEY_ROLE_MAPPINGS` in `.env`

3. **External Client Not Detected**
   - Use API key override instead of relying on User-Agent
   - Check `EXTERNAL_CLIENT_ROLE` setting

### Debug Commands

```bash
# Check server logs
tail -f logs/access.log | grep RBAC

# Test specific permission
curl -H "Authorization: Bearer YOUR_KEY" \
     -H "Content-Type: application/json" \
     -d '{"action":"execute_command"}' \
     http://localhost:8011/rbac/test-permission
```

---

**🛡️ Your VPS is now secure! Only admin API keys can execute dangerous commands.**