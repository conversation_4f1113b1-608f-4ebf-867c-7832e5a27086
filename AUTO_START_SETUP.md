# Hướng Dẫn Thiết Lập Auto-Start cho Gemini CLI Wrapper

## Tổng Quan
Dự án đã được thiết lập để tự động khởi động khi server boot thông qua PM2 và systemd.

## Các Thành Phần Đã Thiết Lập

### 1. PM2 Startup Service
- ✅ **PM2 startup script**: Đã được cấu hình với systemd
- ✅ **Service name**: `pm2-ubuntu`
- ✅ **Status**: Enabled và sẵn sàng chạy khi boot
- ✅ **Process list**: Đã được lưu với `pm2 save`

### 2. Auto Start Script
- ✅ **File**: `auto_start.sh`
- ✅ **Chức năng**: Kiểm tra và khởi động dự án nếu cần
- ✅ **Permissions**: Executable

## Cách Hoạt Động

### Khi Server Boot:
1. Systemd khởi động service `pm2-ubuntu`
2. PM2 tự động khôi phục các process đã lưu (`pm2 resurrect`)
3. Gemini CLI Wrapper tự động chạy trên port 8010

### Kiểm Tra Manual:
```bash
# Kiểm tra status systemd service
sudo systemctl status pm2-ubuntu

# Kiểm tra PM2 processes
pm2 status

# Chạy auto start script
./auto_start.sh
```

## Lệnh Quản Lý

### Khởi Động
```bash
./start.sh          # Khởi động thông thường
./auto_start.sh     # Khởi động với kiểm tra tự động
```

### Kiểm Tra
```bash
pm2 status          # Xem trạng thái processes
pm2 logs            # Xem logs
./test_api.sh       # Test API hoạt động
```

### Dừng
```bash
pm2 stop all        # Dừng tất cả processes
pm2 delete all      # Xóa tất cả processes
```

### Vô Hiệu Hóa Auto-Start
```bash
pm2 unstartup systemd    # Vô hiệu hóa auto-start
sudo systemctl disable pm2-ubuntu
```

## Thông Tin Truy Cập

- **Local**: http://localhost:8010
- **External**: http://**********:8010

## Troubleshooting

### Nếu Service Không Khởi Động:
```bash
# Kiểm tra systemd service
sudo systemctl status pm2-ubuntu
sudo journalctl -u pm2-ubuntu

# Khởi động manual
sudo systemctl start pm2-ubuntu
```

### Nếu PM2 Không Khôi Phục Process:
```bash
# Kiểm tra dump file
ls -la ~/.pm2/dump.pm2

# Khôi phục manual
pm2 resurrect

# Hoặc khởi động từ ecosystem config
pm2 start ecosystem.config.cjs
pm2 save
```

### Nếu Port Bị Chiếm:
```bash
# Kiểm tra process đang sử dụng port 8010
sudo lsof -i :8010

# Kill process nếu cần
sudo lsof -ti:8010 | xargs kill -9
```

## Logs và Monitoring

- **PM2 Logs**: `pm2 logs`
- **System Logs**: `sudo journalctl -u pm2-ubuntu`
- **Application Logs**: `logs/` directory trong project

## Kết Luận

✅ **Auto-start đã được thiết lập hoàn chỉnh**
- Dự án sẽ tự động khởi động khi server boot
- PM2 quản lý process với auto-restart
- Systemd đảm bảo PM2 luôn chạy
- Script auto_start.sh để kiểm tra và khởi động manual khi cần

**Dự án giờ đây sẽ tự động chạy mỗi khi server khởi động lại!**