# API Key Setup Guide for External Agents

## 🔑 Overview

This guide explains how to configure API keys for external agents (VS Code, <PERSON><PERSON>, <PERSON>, etc.) to access the Gemini CLI Wrapper with proper RBAC permissions.

## 📋 Quick Setup Steps

### 1. Create Environment File

Copy the example environment file:
```bash
cp .env.example .env
```

### 2. Generate Secure API Keys

Generate strong API keys for different roles:
```bash
# Generate random API keys (Linux/Mac)
echo "Admin API Key: $(openssl rand -hex 32)"
echo "User API Key: $(openssl rand -hex 32)"
echo "Guest API Key: $(openssl rand -hex 32)"
```

### 3. Configure .env File

Edit your `.env` file with your generated keys:
```bash
# RBAC API Keys - Replace with your generated keys
ADMIN_API_KEY=a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456
USER_API_KEY=b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef1234567a
GUEST_API_KEY=c3d4e5f6789012345678901234567890abcdef1234567890abcdef1234567ab2

# External Client Default Roles
EXTERNAL_CLIENT_ROLE=user
VSCODE_CLIENT_ROLE=user
CLINE_CLIENT_ROLE=user

# API Key to Role Mappings (JSON format)
API_KEY_ROLE_MAPPINGS={"a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456":"admin","b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef1234567a":"user","c3d4e5f6789012345678901234567890abcdef1234567890abcdef1234567ab2":"guest"}
```

## 🛠️ Agent Configuration

### VS Code / Cursor

#### Method 1: Using REST Client Extension
Create a `.http` file:
```http
### Test with User API Key
GET http://localhost:8011/rbac/status
Authorization: Bearer b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef1234567a
User-Agent: vscode-restclient

### Execute safe command
POST http://localhost:8011/mcp/tools/execute
Authorization: Bearer b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef1234567a
Content-Type: application/json

{
  "name": "read_file",
  "args": {"path": "package.json"}
}
```

#### Method 2: Using VS Code Settings
Add to your VS Code `settings.json`:
```json
{
  "gemini-cli-wrapper.apiKey": "b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef1234567a",
  "gemini-cli-wrapper.baseUrl": "http://localhost:8011"
}
```

### Cline Assistant

Configure Cline with your API key:
```json
{
  "apiKey": "b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef1234567a",
  "baseUrl": "http://localhost:8011",
  "model": "gemini-pro"
}
```

### Claude Dev Extension

Add to Claude Dev settings:
```json
{
  "geminiCliWrapper": {
    "apiKey": "b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef1234567a",
    "endpoint": "http://localhost:8011"
  }
}
```

### Continue Extension

Configure in Continue's `config.json`:
```json
{
  "models": [
    {
      "title": "Gemini CLI Wrapper",
      "provider": "openai",
      "model": "gemini-pro",
      "apiKey": "b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef1234567a",
      "apiBase": "http://localhost:8011/v1"
    }
  ]
}
```

### Aider

Use with command line:
```bash
aider --openai-api-key b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef1234567a \
      --openai-api-base http://localhost:8011/v1 \
      --model gemini-pro
```

## 🔒 Role-Based Access

### Admin Role (Full Access)
```bash
# API Key: ADMIN_API_KEY
# Permissions: ALL (including dangerous commands)
curl -H "Authorization: Bearer a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456" \
     -H "Content-Type: application/json" \
     -d '{"name":"execute_command","args":{"command":"ls -la"}}' \
     http://localhost:8011/mcp/tools/execute
```

### User Role (Safe Operations)
```bash
# API Key: USER_API_KEY  
# Permissions: read_file, web_search, analyze_code, openwebui_*
curl -H "Authorization: Bearer b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef1234567a" \
     -H "Content-Type: application/json" \
     -d '{"name":"read_file","args":{"path":"package.json"}}' \
     http://localhost:8011/mcp/tools/execute
```

### Guest Role (Read-Only)
```bash
# API Key: GUEST_API_KEY
# Permissions: web_search, openwebui_web_search, openwebui_get_models
curl -H "Authorization: Bearer c3d4e5f6789012345678901234567890abcdef1234567890abcdef1234567ab2" \
     -H "Content-Type: application/json" \
     -d '{"name":"web_search","args":{"query":"test"}}' \
     http://localhost:8011/mcp/tools/execute
```

## 🧪 Testing Your Setup

### Test API Key Authentication
```bash
# Test user role
curl -H "Authorization: Bearer YOUR_USER_API_KEY" \
     http://localhost:8011/rbac/status

# Test permission
curl -H "Authorization: Bearer YOUR_USER_API_KEY" \
     -H "Content-Type: application/json" \
     -d '{"action":"read_file"}' \
     http://localhost:8011/rbac/test-permission
```

### Run Automated Tests
```bash
# Set your API keys in environment
export ADMIN_API_KEY=your-admin-key
export USER_API_KEY=your-user-key
export GUEST_API_KEY=your-guest-key

# Run tests
./test_rbac_security.sh
./test_external_clients.sh
```

## 🔄 Authentication Priority

The system checks authentication in this order:

1. **API Key Mapping** (highest priority)
   - Checks `Authorization: Bearer <key>` against `API_KEY_ROLE_MAPPINGS`
   - Overrides all other methods

2. **Custom Headers**
   - `X-User-Role` or `X-OpenWebUI-Role`

3. **JWT Token**
   - Extracts role from JWT claims

4. **User-Agent Detection**
   - Automatically assigns roles to known clients (VS Code, Cline, etc.)

5. **Default Guest** (lowest priority)
   - Unknown clients get 'guest' role

## 🚨 Security Best Practices

### API Key Security
- **Never commit API keys** to version control
- **Use different keys** for different environments
- **Rotate keys regularly**
- **Use strong, random keys** (32+ characters)

### Environment Setup
```bash
# Production example
ADMIN_API_KEY=$(openssl rand -hex 32)
USER_API_KEY=$(openssl rand -hex 32)
GUEST_API_KEY=$(openssl rand -hex 32)
NODE_ENV=production
```

### Monitoring
Check logs for authentication attempts:
```bash
# Monitor access logs
tail -f logs/access.log | grep "RBAC"

# Check for failed authentication
tail -f logs/error.log | grep "Access denied"
```

## 🔧 Troubleshooting

### Common Issues

#### 1. 403 Access Denied
```bash
# Check your role
curl -H "Authorization: Bearer YOUR_API_KEY" \
     http://localhost:8011/rbac/status

# Check permission for specific action
curl -H "Authorization: Bearer YOUR_API_KEY" \
     -H "Content-Type: application/json" \
     -d '{"action":"your_action"}' \
     http://localhost:8011/rbac/test-permission
```

#### 2. API Key Not Working
- Verify key is in `.env` file
- Check `API_KEY_ROLE_MAPPINGS` JSON format
- Restart server after changing `.env`

#### 3. External Client Not Detected
- Check User-Agent header
- Verify `EXTERNAL_CLIENT_ROLE` setting
- Use API key override if needed

### Debug Mode
```bash
# Enable debug logging
LOG_LEVEL=debug node src/index.js

# Check RBAC status
curl http://localhost:8011/rbac/status
```

## 📞 Support

If you encounter issues:
1. Check the logs for error messages
2. Verify your `.env` configuration
3. Test with the provided test scripts
4. Review the RBAC Security Guide for detailed troubleshooting

---

**🔒 Remember: Only admin API keys can execute dangerous commands like `execute_command` and `write_file`. User and guest roles are restricted for security.**