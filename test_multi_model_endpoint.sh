#!/bin/bash

# Multi-Model Endpoint Test Script
# Tests both <PERSON> and <PERSON> through the same /v1/chat/completions endpoint

echo "🚀 Testing Multi-Model Endpoint Support"
echo "========================================"

# Configuration
BASE_URL="http://localhost:8010"
API_KEY="2ae26d0e5603dd813b3c073dff3ec1d77047e90fddbf83b96b2e871f9191fe0c"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to test API call
test_model() {
    local model=$1
    local provider=$2
    local question="What is 2+2? Answer briefly."
    
    echo -e "\n${BLUE}Testing ${provider} Model: ${model}${NC}"
    echo "Question: $question"
    echo "----------------------------------------"
    
    response=$(curl -s -X POST "${BASE_URL}/v1/chat/completions" \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer ${API_KEY}" \
        -d "{
            \"model\": \"${model}\",
            \"messages\": [
                {
                    \"role\": \"user\",
                    \"content\": \"${question}\"
                }
            ]
        }")
    
    if echo "$response" | grep -q "error"; then
        echo -e "${RED}❌ Error:${NC}"
        echo "$response" | jq -r '.error // .message // .'
    else
        echo -e "${GREEN}✅ Success:${NC}"
        echo "$response" | jq -r '.choices[0].message.content // .content // .'
    fi
}

# Function to check available models
check_models() {
    echo -e "\n${YELLOW}📋 Checking Available Models${NC}"
    echo "==============================="
    
    response=$(curl -s -X GET "${BASE_URL}/v1/models" \
        -H "Authorization: Bearer ${API_KEY}")
    
    if echo "$response" | grep -q "error"; then
        echo -e "${RED}❌ Error getting models:${NC}"
        echo "$response" | jq -r '.error // .message // .'
    else
        echo -e "${GREEN}✅ Available Models:${NC}"
        echo "$response" | jq -r '.data[] | "- \(.id) (\(.owned_by))"'
    fi
}

# Function to test server health
test_health() {
    echo -e "\n${YELLOW}🏥 Testing Server Health${NC}"
    echo "========================="
    
    response=$(curl -s -X GET "${BASE_URL}/health")
    
    if echo "$response" | grep -q "healthy"; then
        echo -e "${GREEN}✅ Server is healthy${NC}"
        echo "$response" | jq '.'
    else
        echo -e "${RED}❌ Server health check failed${NC}"
        echo "$response"
    fi
}

# Function to test streaming
test_streaming() {
    local model=$1
    local provider=$2
    
    echo -e "\n${BLUE}Testing ${provider} Streaming: ${model}${NC}"
    echo "Question: Count from 1 to 3"
    echo "----------------------------------------"
    
    curl -s -X POST "${BASE_URL}/v1/chat/completions" \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer ${API_KEY}" \
        -d "{
            \"model\": \"${model}\",
            \"messages\": [
                {
                    \"role\": \"user\",
                    \"content\": \"Count from 1 to 3, one number per line\"
                }
            ],
            \"stream\": true
        }" | while IFS= read -r line; do
            if [[ $line == data:* ]]; then
                data=${line#data: }
                if [[ $data != "[DONE]" ]]; then
                    content=$(echo "$data" | jq -r '.choices[0].delta.content // empty' 2>/dev/null)
                    if [[ -n $content && $content != "null" ]]; then
                        echo -n "$content"
                    fi
                fi
            fi
        done
    echo -e "\n${GREEN}✅ Streaming test completed${NC}"
}

# Main test execution
main() {
    echo "Starting multi-model endpoint tests..."
    
    # Test server health first
    test_health
    
    # Check available models
    check_models
    
    # Test Gemini models
    echo -e "\n${YELLOW}🤖 Testing Gemini Models${NC}"
    echo "=========================="
    test_model "gemini-2.5-flash" "Gemini"
    test_model "gemini-2.5-pro" "Gemini"
    
    # Test Claude models
    echo -e "\n${YELLOW}🧠 Testing Claude Models${NC}"
    echo "========================="
    test_model "claude-3-5-sonnet-20241022" "Claude"
    test_model "claude-3-5-haiku-20241022" "Claude"
    
    # Test streaming (optional)
    echo -e "\n${YELLOW}🌊 Testing Streaming Support${NC}"
    echo "============================="
    test_streaming "gemini-2.5-flash" "Gemini"
    
    echo -e "\n${GREEN}🎉 Multi-model endpoint testing completed!${NC}"
    echo ""
    echo "Summary:"
    echo "- Same endpoint: /v1/chat/completions"
    echo "- Same API key authentication"
    echo "- Different models via 'model' parameter"
    echo "- Both Gemini and Claude supported"
    echo ""
    echo "Usage in Open WebUI:"
    echo "1. Base URL: http://localhost:8010/v1"
    echo "2. API Key: ${API_KEY}"
    echo "3. Model: Choose from available models above"
}

# Check if jq is installed
if ! command -v jq &> /dev/null; then
    echo -e "${RED}❌ jq is required but not installed. Installing...${NC}"
    sudo apt-get update && sudo apt-get install -y jq
fi

# Run main function
main