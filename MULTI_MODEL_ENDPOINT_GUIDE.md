# Multi-Model Endpoint Guide

## 🎯 Tổng quan

Hệ thống **Gemini CLI Wrapper** đã hỗ trợ **c<PERSON> Gemini và Claude** thông qua **cùng 1 endpoint** `/v1/chat/completions`. Bạn chỉ cần thay đổi parameter `model` trong request để chọn AI provider khác nhau.

## 🚀 C<PERSON>ch sử dụng

### **Endpoint duy nhất:**
```
POST http://localhost:8010/v1/chat/completions
```

### **Chọn Model thông qua parameter:**

#### **1. Sử dụng Gemini Models:**
```json
{
  "model": "gemini-2.5-flash",
  "messages": [
    {
      "role": "user", 
      "content": "Hello from <PERSON>!"
    }
  ]
}
```

#### **2. Sử dụng Claude Models:**
```json
{
  "model": "claude-3-5-sonnet-20241022",
  "messages": [
    {
      "role": "user",
      "content": "Hello from <PERSON>!"
    }
  ]
}
```

## 📋 Available Models

### **Gemini Models:**
- `gemini-2.5-flash` (Fast, efficient)
- `gemini-2.5-pro` (Advanced reasoning)
- `gemini-1.5-flash` (Legacy)
- `gemini-1.5-pro` (Legacy)

### **Claude Models:**
- `claude-3-5-sonnet-20241022` (Latest Sonnet)
- `claude-3-5-haiku-20241022` (Fast, efficient)
- `claude-3-opus-20240229` (Most capable)

## 🔧 Cấu hình Open WebUI

### **Cho Gemini:**
```
OpenAI API Base URL: http://localhost:8010/v1
Model: gemini-2.5-flash
API Key: 2ae26d0e5603dd813b3c073dff3ec1d77047e90fddbf83b96b2e871f9191fe0c
```

### **Cho Claude:**
```
OpenAI API Base URL: http://localhost:8010/v1
Model: claude-3-5-sonnet-20241022
API Key: 2ae26d0e5603dd813b3c073dff3ec1d77047e90fddbf83b96b2e871f9191fe0c
```

## 💡 Ví dụ cURL

### **Test Gemini:**
```bash
curl -X POST http://localhost:8010/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer 2ae26d0e5603dd813b3c073dff3ec1d77047e90fddbf83b96b2e871f9191fe0c" \
  -d '{
    "model": "gemini-2.5-flash",
    "messages": [
      {
        "role": "user",
        "content": "What is the capital of Vietnam?"
      }
    ]
  }'
```

### **Test Claude:**
```bash
curl -X POST http://localhost:8010/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer 2ae26d0e5603dd813b3c073dff3ec1d77047e90fddbf83b96b2e871f9191fe0c" \
  -d '{
    "model": "claude-3-5-sonnet-20241022",
    "messages": [
      {
        "role": "user",
        "content": "What is the capital of Vietnam?"
      }
    ]
  }'
```

## 🔍 Kiểm tra Available Models

```bash
curl -X GET http://localhost:8010/v1/models \
  -H "Authorization: Bearer 2ae26d0e5603dd813b3c073dff3ec1d77047e90fddbf83b96b2e871f9191fe0c"
```

## 🛡️ RBAC Security

Cả Gemini và Claude đều tuân theo **cùng hệ thống RBAC**:
- **Admin**: Full access to all tools
- **User**: Limited access to safe tools
- **Guest**: Read-only access

## 🎛️ Advanced Features

### **Streaming Response:**
```json
{
  "model": "gemini-2.5-flash",
  "messages": [...],
  "stream": true
}
```

### **Tool Usage:**
```json
{
  "model": "claude-3-5-sonnet-20241022",
  "messages": [...],
  "tools": [
    {
      "type": "function",
      "function": {
        "name": "execute_command",
        "description": "Execute shell command"
      }
    }
  ]
}
```

## 🔄 Provider Manager

Hệ thống sử dụng `providerManager` để:
- **Auto-detect** provider dựa trên model name
- **Route requests** đến đúng AI service
- **Handle authentication** cho từng provider
- **Manage rate limiting** và error handling

## 📝 Lưu ý quan trọng

1. **Không cần multiple servers** - Chỉ cần 1 server duy nhất
2. **Không cần multiple ports** - Chỉ cần port 8010
3. **Không cần multiple endpoints** - Chỉ cần `/v1/chat/completions`
4. **Chỉ cần thay đổi `model` parameter** để switch giữa Gemini và Claude

## 🚀 Kết luận

Hệ thống đã được thiết kế **chuẩn OpenAI API** với khả năng hỗ trợ multiple AI providers thông qua cùng 1 endpoint. Điều này giúp:
- **Dễ dàng tích hợp** với Open WebUI
- **Tương thích** với các client OpenAI
- **Linh hoạt** trong việc chọn AI provider
- **Đơn giản** trong deployment và maintenance