#!/bin/bash

# Set JWT Token
export JWT_TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJlbnYiOiJwcm9kdWN0aW9uIiwia2lsb1VzZXJJZCI6Im9hdXRoL2dvb2dsZToxMDQ1NDQ0ODE2OTcwNjE3NjYzMjMiLCJhcGlUb2tlblBlcHBlciI6bnVsbCwidmVyc2lvbiI6MywiaWF0IjoxNzUyMjA2MTg2LCJleHAiOjE5MDk5OTQxODZ9.pg8hxhyIpsi1XBJy7d7H1opzaQOyds8q8mPSNZBUEhg"

echo "🔑 JWT Token set (length: ${#JWT_TOKEN})"
echo "🚀 Starting Cursor Proxy Server..."

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install express node-fetch
fi

# Start the proxy server
node cursor_proxy_server.cjs