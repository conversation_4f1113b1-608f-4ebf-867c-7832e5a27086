# Open WebUI API Key Guide

## 🤔 Câu Hỏi: Open WebUI Có Cần API Key Không?

**Tr<PERSON> lời**: B<PERSON><PERSON> giờ bạn có thể **TỰ CHỌN** có yêu cầu API key hay không!

## 🔧 Tùy Chọn Bảo Mật Mới

### Chế độ 1: **KHÔNG YÊU CẦU API KEY** (Mặc định)

**Cấu hình:**
```bash
# Trong .env (hoặc không set)
REQUIRE_OPENAI_AUTH=false
```

**Setup trong Open WebUI:**
```
OpenAI API Base URL: http://localhost:8010/v1
OpenAI API Key: [Để trống hoặc bất kỳ giá trị nào]
```

**✅ Ưu điểm:**
- Dễ dàng thiết lập
- Không cần quản lý API keys
- Tương thích tốt với Open WebUI

**⚠️ Nhược điểm:**
- <PERSON>t bảo mật hơn
- B<PERSON>t kỳ ai có thể truy cập `/v1/*` endpoints

---

### Chế độ 2: **YÊU CẦU API KEY** (<PERSON><PERSON><PERSON> mật cao)

**Cấu hình:**
```bash
# Trong .env
REQUIRE_OPENAI_AUTH=true
ADMIN_API_KEY=your-secure-admin-key
USER_API_KEY=your-secure-user-key
```

**Setup trong Open WebUI:**
```
OpenAI API Base URL: http://localhost:8010/v1
OpenAI API Key: your-secure-user-key
```

**✅ Ưu điểm:**
- Bảo mật cao
- Kiểm soát truy cập với RBAC
- Audit logs đầy đủ
- Phân quyền theo role (admin/user/guest)

**⚠️ Nhược điểm:**
- Cần quản lý API keys
- Thiết lập phức tạp hơn

## 🚀 Hướng Dẫn Thiết Lập Bảo Mật

### Bước 1: Tạo API Keys
```bash
./setup_api_keys.sh
```

### Bước 2: Bật Authentication
```bash
echo "REQUIRE_OPENAI_AUTH=true" >> .env
```

### Bước 3: Restart Server
```bash
npm start
```

### Bước 4: Cấu hình Open WebUI
1. Vào **Settings** → **Connections** → **OpenAI API**
2. Nhập API key từ file `api_keys.txt`

### Bước 5: Test Cấu Hình
```bash
./test_openwebui_security.sh
```

## 🔍 Giải Thích Kỹ Thuật

### Authentication Logic
```javascript
// Trong src/index.js
const modifiedAuthMiddleware = (req, res, next) => {
  if (req.path === '/health') {
    return next(); // Health check luôn public
  }
  
  const requireOpenAIAuth = process.env.REQUIRE_OPENAI_AUTH === 'true';
  
  if (req.path.startsWith('/v1/') && !requireOpenAIAuth) {
    return next(); // Bypass auth nếu không yêu cầu
  }
  
  authMiddleware(req, res, next); // Yêu cầu auth
};
```

### RBAC Integration
Khi bật `REQUIRE_OPENAI_AUTH=true`, Open WebUI sẽ được áp dụng RBAC:

- **Admin role**: Truy cập tất cả tools và endpoints
- **User role**: Truy cập hạn chế, không có dangerous actions  
- **Guest role**: Chỉ read-only access

## 📊 So Sánh Các Chế độ

| Chế độ | API Key | Bảo mật | Phù hợp cho |
|--------|---------|---------|-------------|
| `REQUIRE_OPENAI_AUTH=false` | ❌ Không cần | Thấp | Development, Demo |
| `REQUIRE_OPENAI_AUTH=true` | ✅ Bắt buộc | Cao | Production, Enterprise |

## 💡 Khuyến Nghị

### Development Environment
```bash
REQUIRE_OPENAI_AUTH=false  # Dễ dàng test
```

### Production Environment  
```bash
REQUIRE_OPENAI_AUTH=true   # Bảo mật cao
NODE_ENV=production
```

## 🔄 2 Loại Tích Hợp (Vẫn áp dụng)

### 1. **Open WebUI → Gemini CLI Wrapper** 
- **Mục đích**: Chat với Gemini models qua Open WebUI
- **API Key**: Tùy chọn (theo `REQUIRE_OPENAI_AUTH`)

### 2. **Gemini CLI Wrapper → Open WebUI** 
- **Mục đích**: LLM tương tác với Open WebUI tools
- **API Key**: **LUÔN CẦN** `OPENWEBUI_API_KEY` trong .env

## 🎯 Kết Luận

**Bây giờ bạn có toàn quyền kiểm soát bảo mật:**

1. **Muốn đơn giản**: `REQUIRE_OPENAI_AUTH=false`
2. **Muốn bảo mật**: `REQUIRE_OPENAI_AUTH=true` + API keys

**Để thiết lập bảo mật chi tiết, xem:** [`OPEN_WEBUI_SECURE_SETUP.md`](OPEN_WEBUI_SECURE_SETUP.md)

**Để test cấu hình bảo mật:** `./test_openwebui_security.sh`