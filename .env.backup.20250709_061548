# Server Configuration
PORT=8010
NODE_ENV=development

# API Security
API_KEY=sk-b520f0c448f445ac8bf9c7cfd51f8a3b

# Gemini Authentication (choose one method)
# Method 1: API Key Authentication
GEMINI_API_KEY=AIzaSyDdXCS0Cw58rfurmaKW061IdkOy1UHK51U

# Method 2: OAuth Authentication
GEMINI_USE_OAUTH=false

# Logging
LOG_LEVEL=info

# Timeouts (in milliseconds)
REQUEST_TIMEOUT=30000
GEMINI_CLI_TIMEOUT=60000

# === TÍNH NĂNG MỚI ===

# Thinking Models - Hiển thị quá trình suy nghĩ của AI (TẮT để tiết kiệm token)
ENABLE_FAKE_THINKING=false

# Stream thinking as content với <thinking> tags (DeepSeek R1 style)
STREAM_THINKING_AS_CONTENT=false

# Google OAuth2 credentials (tùy chọn, cho token caching nâng cao)
GOOGLE_CLIENT_ID=77185425430.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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

# Vision Support - Tự động bật cho các model hỗ trợ
ENABLE_VISION=true

# Token cache settings
TOKEN_CACHE_ENABLED=true
TOKEN_REFRESH_BUFFER=300000

# === SECURITY & PERMISSIONS ===

# CẢNH BÁO: Chỉ bật trong môi trường an toàn và tin cậy!
# Unrestricted Mode - Cho phép AI truy cập toàn bộ hệ thống
# - Đọc/ghi file ở bất kỳ đâu trên server
# - Thực thi bất kỳ lệnh nào
# - Truy cập các thư mục hệ thống
GEMINI_UNRESTRICTED_MODE=false

# Khi GEMINI_UNRESTRICTED_MODE=true:
# ⚠️  AI có thể đọc/ghi file: /etc/passwd, /home/<USER>/.ssh/id_rsa, etc.
# ⚠️  AI có thể chạy lệnh: rm -rf /, sudo, curl, wget, etc.
# ⚠️  AI có thể truy cập database, API keys, secrets
# ⚠️  Chỉ sử dụng trong sandbox hoặc container isolated

# === OPEN WEBUI INTEGRATION ===

# Open WebUI Base URL for integration tools
OPEN_WEBUI_BASE_URL=http://localhost:3000

# Enable Open WebUI tools integration
ENABLE_OPEN_WEBUI_TOOLS=true

# Open WebUI API authentication (if required)

# Browser automation settings for Open WebUI interaction
ENABLE_BROWSER_AUTOMATION=true
PUPPETEER_HEADLESS=true
OPEN_WEBUI_API_KEY=sk-b520f0c448f445ac8bf9c7cfd51f8a3b
ENABLE_BROWSER_AUTOMATION=true

# === CLAUDE INTEGRATION ===

# Claude Authentication - Third-party service
ANTHROPIC_BASE_URL="https://api.yescale.io"
ANTHROPIC_AUTH_TOKEN="sk-JCzvMLjfUEB99Px4lmOGurTi9KmOAT4vzRDQtKgrAU4Q7C5M"
