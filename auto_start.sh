#!/bin/bash

# Script tự động khởi động Gemini CLI Wrapper
# Đư<PERSON><PERSON> thiết kế để chạy khi server boot hoặc khi cần khởi động lại

# Màu sắc cho output
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Auto Start Gemini CLI Wrapper ===${NC}"

# Chuyển đến thư mục dự án
cd /home/<USER>/Gemini\ CLI\ Wrapper

# Kiểm tra PM2 có đang chạy không
if ! pgrep -f "PM2" > /dev/null; then
    echo -e "${YELLOW}PM2 chưa chạy, đang khởi động...${NC}"
    pm2 resurrect
fi

# Kiểm tra xem dự án có đang chạy không
if pm2 list | grep -q "gemini-cli-wrapper.*online"; then
    echo -e "${GREEN}✓ Gemini CLI Wrapper đã đang chạy${NC}"
else
    echo -e "${YELLOW}Đang khởi động Gemini CLI Wrapper...${NC}"
    
    # Dừng tất cả processes cũ
    pm2 delete all 2>/dev/null
    
    # Khởi động lại từ ecosystem config
    pm2 start ecosystem.config.cjs
    
    # Lưu cấu hình
    pm2 save
    
    echo -e "${GREEN}✓ Gemini CLI Wrapper đã được khởi động${NC}"
fi

# Hiển thị trạng thái
echo -e "\n${BLUE}Trạng thái hiện tại:${NC}"
pm2 status

# Hiển thị thông tin truy cập
SERVER_IP=$(hostname -I | awk '{print $1}')
echo -e "\n${GREEN}Server có thể truy cập tại:${NC}"
echo -e "${BLUE}Local:${NC} http://localhost:8010"
echo -e "${BLUE}External:${NC} http://$SERVER_IP:8010"

echo -e "\n${GREEN}Auto start script hoàn thành!${NC}"