# Hướng dẫn RBAC cho External Clients (VS Code, Cline, etc.)

## Tổng quan

Hệ thống RBAC đã được mở rộng để hỗ trợ các external clients như VS Code, Cursor, Cline, và các AI coding assistants khác. Điều này cho phép bạn kiểm soát quyền truy cập một cách linh hoạt cho từng loại client.

## Cách thức hoạt động

### 1. Phát hiện External Client
Hệ thống tự động phát hiện external clients dựa trên User-Agent header:

- **VS Code/Cursor**: `/vscode|cursor/i`
- **Cline**: `/cline|claude-dev/i`
- **Continue**: `/continue/i`
- **Aider**: `/aider/i`
- **GitHub Copilot**: `/copilot/i`

### 2. <PERSON><PERSON> quyền theo Client
Mỗi loại client có thể được cấu hình role riêng biệt thông qua environment variables.

## Cấu hình

### Bước 1: Tạo file .env
```bash
cp .env.example .env
```

### Bước 2: Cấu hình Role cho External Clients

#### Cấu hình cơ bản:
```env
# Role mặc định cho tất cả external clients
EXTERNAL_CLIENT_ROLE=user

# Role riêng cho từng client
VSCODE_CLIENT_ROLE=user
CLINE_CLIENT_ROLE=user
```

#### Cấu hình nâng cao với API Key mapping:
```env
# Mapping API key với role (JSON format)
API_KEY_ROLE_MAPPINGS={"vscode-admin-key":"admin","cline-user-key":"user","guest-key":"guest"}

# Hoặc sử dụng các key riêng biệt
ADMIN_API_KEY=your-admin-secret-key
USER_API_KEY=your-user-api-key
GUEST_API_KEY=your-guest-api-key
```

## Các Role và Quyền hạn

### 🔴 Admin Role
**Quyền hạn**: Toàn quyền truy cập
```json
{
  "allowed_actions": [
    "execute_command",    // ⚠️ NGUY HIỂM: Thực thi lệnh shell
    "write_file",         // ⚠️ NGUY HIỂM: Ghi file
    "read_file",
    "web_search",
    "analyze_code",
    "store_memory",
    "rbac_manage",
    "openwebui_*",
    "browser_*"
  ]
}
```

### 🟡 User Role (Khuyến nghị cho VS Code/Cline)
**Quyền hạn**: An toàn, không thể thay đổi hệ thống
```json
{
  "allowed_actions": [
    "read_file",                    // Đọc file
    "web_search",                   // Tìm kiếm web
    "analyze_code",                 // Phân tích code
    "store_memory",                 // Lưu trữ memory
    "openwebui_web_search",         // Tìm kiếm qua OpenWebUI
    "openwebui_get_models",         // Lấy danh sách model
    "openwebui_get_conversations",  // Lấy cuộc hội thoại
    "openwebui_create_chat"         // Tạo chat mới
  ]
}
```

### 🟢 Guest Role
**Quyền hạn**: Rất hạn chế, chỉ đọc
```json
{
  "allowed_actions": [
    "web_search",
    "openwebui_web_search",
    "openwebui_get_models"
  ]
}
```

## Cách sử dụng với các Client

### VS Code với Cursor/Continue Extension

1. **Cấu hình API endpoint**:
   ```json
   {
     "continue.apiBase": "http://localhost:8011/v1",
     "continue.apiKey": "your-user-api-key"
   }
   ```

2. **Thiết lập role**:
   ```env
   VSCODE_CLIENT_ROLE=user
   USER_API_KEY=your-user-api-key
   ```

### Cline

1. **Cấu hình trong Cline**:
   - API Base URL: `http://localhost:8011/v1`
   - API Key: `your-user-api-key`

2. **Thiết lập role**:
   ```env
   CLINE_CLIENT_ROLE=user
   USER_API_KEY=your-user-api-key
   ```

### Truy cập Admin (Cẩn thận!)

Nếu bạn cần quyền admin cho development:
```env
VSCODE_CLIENT_ROLE=admin
ADMIN_API_KEY=your-very-secret-admin-key
```

**⚠️ CẢNH BÁO**: Admin role có thể thực thi lệnh shell và ghi file. Chỉ sử dụng trong môi trường development an toàn!

## Kiểm tra và Debug

### 1. Kiểm tra role hiện tại:
```bash
curl -H "Authorization: Bearer your-api-key" \
     -H "User-Agent: vscode" \
     http://localhost:8011/rbac/status
```

### 2. Test permission:
```bash
curl -X POST http://localhost:8011/rbac/test-permission \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer your-api-key" \
     -H "User-Agent: vscode" \
     -d '{"action": "read_file"}'
```

### 3. Xem logs:
```bash
tail -f logs/app.log | grep RBAC
```

## Bảo mật

### Khuyến nghị:
1. **Sử dụng User role** cho hầu hết external clients
2. **Không chia sẻ Admin API key**
3. **Thường xuyên rotate API keys**
4. **Monitor access logs** để phát hiện truy cập bất thường

### Dangerous Actions được log:
- `execute_command`
- `write_file`
- `openwebui_send_message`
- `openwebui_upload_file`
- `browser_action`

## Troubleshooting

### Lỗi 403 Access Denied:
1. Kiểm tra API key có đúng không
2. Xác nhận role được cấu hình đúng
3. Kiểm tra User-Agent header
4. Xem logs để debug

### Client không được nhận diện:
1. Thêm pattern vào `isExternalClient()` method
2. Hoặc sử dụng API key mapping thay vì auto-detection

### Cần quyền cao hơn:
1. Cân nhắc tạo custom role
2. Hoặc sử dụng API key mapping với admin key (cẩn thận!)

## Ví dụ Configuration

### Development Setup (Tương đối an toàn):
```env
EXTERNAL_CLIENT_ROLE=user
VSCODE_CLIENT_ROLE=user
CLINE_CLIENT_ROLE=user
USER_API_KEY=dev-user-key-123
```

### Production Setup (Bảo mật cao):
```env
EXTERNAL_CLIENT_ROLE=guest
API_KEY_ROLE_MAPPINGS={"prod-admin-key-xyz":"admin","prod-user-key-abc":"user"}
```

Với cấu hình này, bạn có thể an toàn sử dụng Gemini CLI Wrapper với các AI coding assistants mà không lo ngại về bảo mật!