# Open WebUI Setup Guide

## ✅ Server Status
Your Gemini CLI Wrapper is running successfully and accessible at:
- **External URL**: `http://10.0.0.153:8010`
- **Health Check**: ✅ Working
- **Models Endpoint**: ✅ Working  
- **Chat Completions**: ✅ Working

## 🔧 Open WebUI Configuration

### Step 1: Add Connection
In Open WebUI, go to **Settings** → **Connections** → **OpenAI API**

### Step 2: Configure Connection
- **URL**: `http://10.0.0.153:8010/v1`
- **API Key**: Leave empty or use any placeholder (authentication is disabled for testing)
- **Connection Type**: Local

### Step 3: Test Connection
Click the **Test Connection** button. You should see:
- ✅ Connection successful
- Models loaded: `gemini-2.5-pro`, `gemini-2.5-flash`, `gemini-1.5-pro`, `gemini-1.5-flash`, `gemini-2.0-flash-exp`

### Step 4: Save Configuration
Click **Save** to store the connection.

## 🎯 Available Models

| Model | Vision | Thinking | Recommended Use |
|-------|--------|----------|-----------------|
| `gemini-2.5-pro` | ✅ | ✅ | Complex reasoning tasks |
| `gemini-2.5-flash` | ✅ | ✅ | Fast responses with thinking |
| `gemini-1.5-pro` | ✅ | ❌ | General purpose |
| `gemini-1.5-flash` | ✅ | ❌ | Quick responses |
| `gemini-2.0-flash-exp` | ✅ | ❌ | Experimental features |

## 🚀 Features Enabled

- ✅ **R1 Style Thinking**: Enabled by default for thinking models
- ✅ **Model Fallback**: Auto-fallback from `gemini-2.5-pro` to `gemini-2.5-flash` on quota limits
- ✅ **Vision Support**: All models support image inputs
- ✅ **Streaming**: Real-time response streaming
- ✅ **Tool Calling**: MCP tools integration
- ✅ **CORS**: Configured for web access

## 🔍 Troubleshooting

### If Open WebUI shows "Network Problem":
1. **Check URL**: Ensure you're using `http://10.0.0.153:8010/v1` (note the `/v1` suffix)
2. **Test Direct Access**: Try accessing `http://10.0.0.153:8010/health` in your browser
3. **Check Network**: Ensure both machines are on the same network
4. **Firewall**: Verify no firewall is blocking port 8010

### If Models Don't Load:
1. **Check Models Endpoint**: Visit `http://10.0.0.153:8010/v1/models` directly
2. **Clear Cache**: Clear Open WebUI cache and refresh
3. **Check Logs**: Monitor PM2 logs with `pm2 logs gemini-cli-wrapper`

### If Chat Doesn't Work:
1. **Test Simple Request**: Try a short message first
2. **Check Model**: Use `gemini-2.5-flash` for reliable responses
3. **Monitor Fallback**: Watch logs for quota fallback messages

## 📊 Server Management

### Check Status
```bash
pm2 status
pm2 logs gemini-cli-wrapper
```

### Restart Server
```bash
pm2 restart gemini-cli-wrapper
```

### Stop Server
```bash
pm2 stop gemini-cli-wrapper
```

## 🎉 Success Indicators

When properly configured, you should see:
- ✅ Green connection status in Open WebUI
- ✅ All 5 models available in the dropdown
- ✅ Successful chat responses
- ✅ Thinking content for supported models
- ✅ Image upload capability for vision models

Your server is ready for use with Open WebUI!