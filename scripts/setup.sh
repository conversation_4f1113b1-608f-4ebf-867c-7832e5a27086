#!/bin/bash

# Gemini CLI Wrapper Setup Script
echo "🚀 Setting up Gemini CLI Wrapper..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js 18 or higher."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    print_error "Node.js version 18 or higher is required. Current version: $(node -v)"
    exit 1
fi

print_status "Node.js version check passed: $(node -v)"

# Install dependencies
print_status "Installing project dependencies..."
npm install

# Install Gemini CLI globally
print_status "Installing Gemini CLI..."
npm install -g @google/gemini-cli

# Check if .env exists
if [ ! -f .env ]; then
    print_warning ".env file not found. Creating from template..."
    cp env.example .env
    print_status "Created .env file from template. Please edit it with your configuration."
else
    print_status ".env file already exists."
fi

# Create necessary directories
mkdir -p logs
mkdir -p temp

print_status "Setup complete! ✅"
echo ""
echo "Next steps:"
echo "1. Edit the .env file with your configuration:"
echo "   - Set your API_KEY for the wrapper service"
echo "   - Set your GEMINI_API_KEY from Google AI Studio"
echo "   - Or enable GEMINI_USE_OAUTH=true for OAuth authentication"
echo ""
echo "2. Start the development server:"
echo "   npm run dev"
echo ""
echo "3. Test the API:"
echo "   curl http://localhost:8010/health"
echo ""
echo "For more information, see README.md" 