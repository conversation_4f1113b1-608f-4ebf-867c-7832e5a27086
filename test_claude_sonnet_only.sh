#!/bin/bash

echo "🧪 Testing Claude 4 Sonnet Integration Only"
echo "=========================================="

BASE_URL="http://localhost:8010"
API_KEY="sk-b520f0c448f445ac8bf9c7cfd51f8a3b"

echo ""
echo "1. Testing Claude 4 Sonnet model availability..."
curl -s "$BASE_URL/v1/models" | jq '.data[] | select(.id == "claude-4-sonnet")'

echo ""
echo "2. Testing Claude 4 Sonnet chat completion..."
curl -X POST "$BASE_URL/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $API_KEY" \
  -d '{
    "model": "claude-4-sonnet",
    "messages": [
      {
        "role": "user", 
        "content": "Chào bạn! Bạn có thể nói tiếng Việt không?"
      }
    ],
    "max_tokens": 100
  }' | jq '.'

echo ""
echo "✅ Claude Sonnet test completed!"