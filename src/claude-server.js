import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import os from 'os';
import { providerManager } from './providers/providerManager.js';
import { authMiddleware } from './middleware/auth.js';
import { errorHandler } from './middleware/errorHandler.js';
import { logger } from './utils/logger.js';
import { mcpHandler } from './mcp/mcpHandler.js';
import { toolRegistry } from './tools/toolRegistry.js';
import { rbacManager, requirePermission } from './middleware/rbac.js';

dotenv.config();

const app = express();
const PORT = process.env.CLAUDE_PORT || 8011;

// Enhanced CORS configuration
app.use(cors({
  origin: function (origin, callback) {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);
    
    // Allow localhost and VPS IP origins
    const allowedOrigins = [
      'http://localhost:3000',
      'http://127.0.0.1:3000',
      'http://localhost:8080',
      'http://127.0.0.1:8080',
      'http://localhost:3001',
      'http://127.0.0.1:3001',
      'http://**********:3000',
      'http://**********:8080',
      'http://**********:3001',
      'http://**************:3000',
      'http://**************:8080',
      'http://**************:3001'
    ];
    
    // Allow any localhost port for development
    if (origin.match(/^https?:\/\/(localhost|127\.0\.0\.1):\d+$/)) {
      return callback(null, true);
    }
    
    // Allow internal VPS IP with any port
    if (origin.match(/^https?:\/\/10\.0\.0\.153:\d+$/)) {
      return callback(null, true);
    }
    
    // Allow external VPS IP with any port
    if (origin.match(/^https?:\/\/217\.142\.186\.49:\d+$/)) {
      return callback(null, true);
    }
    
    if (allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      // For production, you might want to be more restrictive
      // For now, allow all origins for maximum compatibility
      callback(null, true);
    }
  },
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'HEAD', 'PATCH'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Origin'],
  exposedHeaders: ['Content-Length', 'Content-Type'],
  credentials: false,
  maxAge: 86400 // 24 hours
}));

app.use(express.json());
app.use(express.static('public'));
app.use((req, res, next) => logger.request(req, res, next));

// Public routes
app.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy', 
    provider: 'claude',
    timestamp: new Date().toISOString() 
  });
});

// Modified authMiddleware with configurable OpenAI API security
const modifiedAuthMiddleware = (req, res, next) => {
  // Always skip auth for health check
  if (req.path === '/health') {
    return next();
  }
  
  // Check if OpenAI API endpoints should require authentication
  const requireOpenAIAuth = process.env.REQUIRE_OPENAI_AUTH === 'true';
  
  if (req.path.startsWith('/v1/') && !requireOpenAIAuth) {
    // Skip auth for OpenAI API endpoints if not required
    logger.info('🔓 Claude OpenAI API endpoint accessed without authentication (REQUIRE_OPENAI_AUTH=false)');
    return next();
  }
  
  // Use the original authMiddleware for all other routes (including /v1/ if auth is required)
  authMiddleware(req, res, next);
};

// Protected routes
const protectedRouter = express.Router();
protectedRouter.use(modifiedAuthMiddleware);

// OpenAI compatible routes - Force Claude models
protectedRouter.get('/v1/models', async (req, res) => {
  try {
    const models = await providerManager.getAvailableModels();
    // Filter only Claude models
    const claudeModels = models.filter(model => 
      model.id.includes('claude') || model.provider === 'claude'
    );
    
    const openAIModels = claudeModels.map(model => ({
      id: model.id,
      object: "model",
      created: Date.now(),
      owned_by: "claude-endpoint",
      permission: [],
      root: model.id,
      parent: null,
      capabilities: {
        vision: model.vision || false,
        thinking: model.thinking || false,
        max_tokens: model.maxTokens || 200000
      }
    }));
    res.json({ data: openAIModels, object: "list" });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

protectedRouter.post('/v1/chat/completions', async (req, res) => {
  try {
    const { model, messages, stream = false, tools, tool_choice, ...options } = req.body;
    
    // Force Claude model if not specified or if Gemini model is specified
    let claudeModel = model;
    if (!model || model.includes('gemini')) {
      claudeModel = 'claude-3-5-sonnet-20241022'; // Default Claude model
    }
    
    // Add model to options
    options.model = claudeModel;
    options.provider = 'claude'; // Force Claude provider
    
    // Add tools and session to options if provided
    if (tools) {
      options.tools = tools;
      options.tool_choice = tool_choice;
    }
    
    if (stream) {
      // Handle streaming response
      res.setHeader('Content-Type', 'text/event-stream');
      res.setHeader('Cache-Control', 'no-cache');
      res.setHeader('Connection', 'keep-alive');
      
      await providerManager.streamCompletion(messages, options, (chunk) => {
        res.write(`data: ${JSON.stringify(chunk)}\n\n`);
      });
      
      res.write('data: [DONE]\n\n');
      res.end();
    } else {
      // Handle regular response
      const response = await providerManager.createCompletion(messages, options);
      res.json(response);
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// MCP endpoints
protectedRouter.get('/mcp/info', (req, res) => {
  const info = mcpHandler.getServerInfo();
  info.provider = 'claude';
  res.json(info);
});

protectedRouter.get('/mcp/tools', (req, res) => {
  res.json({
    tools: mcpHandler.formatToolsForAPI(),
    provider: 'claude'
  });
});

protectedRouter.post('/mcp/tools/execute', async (req, res) => {
  try {
    const { tool_name, args, arguments: argumentsAlias, session_id } = req.body;
    
    if (!tool_name) {
      return res.status(400).json({ error: 'tool_name is required' });
    }
    
    // Support both 'args' and 'arguments' for compatibility
    const toolArgs = args || argumentsAlias || {};
    
    // Create user context for RBAC
    const userContext = {
      userRole: req.userRole || 'user',
      ip: req.ip || req.connection.remoteAddress,
      userAgent: req.headers['user-agent'],
      path: req.path,
      method: req.method,
      provider: 'claude'
    };
    
    const result = await toolRegistry.executeTool(tool_name, toolArgs, userContext);
    
    // Store in session if provided
    if (session_id) {
      const session = mcpHandler.initializeSession(session_id);
      session.context.set(`tool_${tool_name}_last`, result);
    }
    
    res.json({
      success: true,
      tool: tool_name,
      result,
      session_id,
      user_role: userContext.userRole,
      provider: 'claude'
    });
  } catch (error) {
    // Check if this is an RBAC authorization error
    if (error.message.includes('Access denied')) {
      return res.status(403).json({
        success: false,
        error: 'Access denied',
        message: error.message,
        tool: req.body.tool_name,
        user_role: req.userRole || 'user',
        provider: 'claude'
      });
    }
    
    res.status(500).json({
      success: false,
      error: error.message,
      tool: req.body.tool_name,
      provider: 'claude'
    });
  }
});

// RBAC Management Endpoints
protectedRouter.get('/rbac/status', (req, res) => {
  try {
    const userRole = req.userRole || 'user';
    const permissions = rbacManager.getUserPermissions(userRole);
    
    res.json({
      success: true,
      current_user: {
        role: userRole,
        permissions: permissions.allowed_actions,
        description: permissions.description
      },
      available_roles: Object.keys(rbacManager.policies),
      dangerous_actions: rbacManager.dangerousActions,
      rbac_enabled: true,
      provider: 'claude'
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Mount protected routes
app.use('/', protectedRouter);

// Error handling middleware
app.use(errorHandler);

// Start server
app.listen(PORT, '0.0.0.0', () => {
  logger.info(`🚀 Claude Endpoint running on port ${PORT}`);
  logger.info(`📊 Health check: http://localhost:${PORT}/health`);
  logger.info(`🔧 Environment: ${process.env.NODE_ENV || 'development'}`);
  logger.info(`🤖 Provider: Claude`);
  
  // Get server IP for external access
  const nets = os.networkInterfaces();
  const results = {};

  for (const name of Object.keys(nets)) {
    for (const net of nets[name]) {
      // Skip over non-IPv4 and internal (i.e. 127.0.0.1) addresses
      if (net.family === 'IPv4' && !net.internal) {
        if (!results[name]) {
          results[name] = [];
        }
        results[name].push(net.address);
      }
    }
  }
  
  // Log external access URLs
  for (const [dev, addresses] of Object.entries(results)) {
    for (const addr of addresses) {
      logger.info(`🌐 Claude External access: http://${addr}:${PORT}`);
    }
  }
});

export default app;