import express from 'express';
import { mcpHandler } from '../mcp/mcpHandler.js';
import { toolRegistry } from '../tools/toolRegistry.js';
import { logger } from '../utils/logger.js';
import { authMiddleware } from '../middleware/auth.js';

const router = express.Router();

// Dify endpoint to list available tools (public)
router.get('/', (req, res) => {
  try {
    logger.info('Dify tool list requested');
    const tools = mcpHandler.formatToolsForAPI().map(tool => {
        return {
            name: tool.function.name,
            description: tool.function.description,
            parameters: tool.function.parameters,
        }
    });
    res.json({ tools });
  } catch (error) {
    logger.error('Error fetching Dify tools:', error);
    res.status(500).json({ error: 'Failed to retrieve tools' });
  }
});

// Dify endpoint to execute a tool (protected)
router.post('/tools/execute', authMiddleware, async (req, res) => {
  try {
    const { tool_name, args } = req.body;

    if (!tool_name) {
      return res.status(400).json({ error: 'tool_name is required' });
    }

    const toolArgs = args || {};
    
    const userContext = {
      userRole: req.userRole || 'user',
      ip: req.ip,
      userAgent: req.headers['user-agent'],
    };

    logger.info(`Executing tool '${tool_name}' for Dify with args:`, toolArgs);
    const result = await toolRegistry.executeTool(tool_name, toolArgs, userContext);

    res.json({ result });
  } catch (error) {
    logger.error(`Error executing tool '${req.body.tool_name}' for Dify:`, error);
    if (error.message.includes('Access denied')) {
        return res.status(403).json({ error: 'Access denied', message: error.message });
    }
    res.status(500).json({ error: 'Failed to execute tool', details: error.message });
  }
});

export default router;