import { logger } from '../utils/logger.js';
import { rbacMiddleware } from './rbac.js';
import crypto from 'crypto';

export const authMiddleware = (req, res, next) => {
  // Skip auth for health check
  if (req.path === '/health') {
    return next();
  }

  // If using OAuth, we still need to extract user role for RBAC
  if (process.env.GEMINI_USE_OAUTH === 'true') {
    logger.info('🔐 Using OAuth authentication');
    // Apply RBAC middleware to extract user role from OAuth token
    return rbacMiddleware(req, res, next);
  }

  // Below this point, we're using API key authentication
  const authHeader = req.headers.authorization;
  
  // Check for Authorization header
  if (!authHeader) {
    return res.status(401).json({
      error: 'Unauthorized',
      message: 'Authentication required'
    });
  }

  // Extract token from "Bearer <token>" format
  const token = authHeader.startsWith('Bearer ')
    ? authHeader.substring(7)
    : authHeader;

  // Get API key from environment
  const apiKey = process.env.API_KEY;

  // Enhanced security: Require API key in production
  if (!apiKey) {
    if (process.env.NODE_ENV === 'production') {
      logger.error('❌ API_KEY not configured in production environment');
      return res.status(500).json({
        error: 'Server configuration error',
        message: 'Authentication not properly configured'
      });
    }
    logger.warn('⚠️ No API_KEY configured - running in development mode');
    // In development, apply RBAC middleware and continue
    return rbacMiddleware(req, res, next);
  }

  // Secure API key comparison to prevent timing attacks
  const tokenBuffer = Buffer.from(token, 'utf8');
  const apiKeyBuffer = Buffer.from(apiKey, 'utf8');
  
  // Ensure buffers are same length to prevent timing attacks
  if (tokenBuffer.length !== apiKeyBuffer.length) {
    return res.status(401).json({
      error: 'Unauthorized',
      message: 'Invalid credentials'
    });
  }

  // Use timing-safe comparison
  const isValid = crypto.timingSafeEqual(tokenBuffer, apiKeyBuffer);
  
  if (!isValid) {
    logger.warn('❌ Invalid API key attempt', {
      ip: req.ip || req.connection.remoteAddress,
      userAgent: req.headers['user-agent'],
      path: req.path
    });
    
    return res.status(401).json({
      error: 'Unauthorized',
      message: 'Invalid credentials'
    });
  }

  // Authentication successful - now apply RBAC
  logger.info('✅ API key authentication successful');
  rbacMiddleware(req, res, next);
};