import { logger } from '../utils/logger.js';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Role-Based Access Control (RBAC) System
 * Provides fine-grained access control for Open WebUI integration
 */
export class RBACManager {
  constructor() {
    this.policies = {
      admin: {
        allowed_actions: [
          'execute_command',
          'write_file',
          'read_file',
          'web_search',
          'analyze_code',
          'store_memory',
          'rbac_manage',  // RBAC management permissions
          'openwebui_*', // All Open WebUI tools
          'browser_*'    // All browser automation tools
        ],
        description: 'Full system access including dangerous operations and RBAC management'
      },
      user: {
        allowed_actions: [
          'read_file',
          'web_search',
          'analyze_code',
          'store_memory',
          'openwebui_web_search',
          'openwebui_get_models',
          'openwebui_get_conversations',
          'openwebui_create_chat'
        ],
        description: 'Safe operations only, no system modification'
      },
      guest: {
        allowed_actions: [
          'web_search',
          'openwebui_web_search',
          'openwebui_get_models'
        ],
        description: 'Very limited read-only access'
      }
    };
    
    this.defaultPolicy = this.policies;
    
    // Dangerous actions that require admin role
    this.dangerousActions = [
      'execute_command',
      'write_file',
      'openwebui_send_message',
      'openwebui_upload_file',
      'openwebui_browser_action',
      'browser_action',
      'browser_click',
      'browser_navigate'
    ];
    
    // Load policies asynchronously but don't block constructor
    this.loadPolicies().catch(error => {
      logger.error('Failed to load RBAC policies:', error);
    });
  }

  /**
   * Load RBAC policies from config file or use defaults
   */
  async loadPolicies() {
    try {
      const configPath = path.join(process.cwd(), 'rbac_policies.json');
      const configExists = await fs.access(configPath).then(() => true).catch(() => false);
      
      if (configExists) {
        const configData = await fs.readFile(configPath, 'utf8');
        const parsedConfig = JSON.parse(configData);
        // Extract policies from the config structure
        this.policies = parsedConfig.policies || parsedConfig;
        logger.info('✅ RBAC policies loaded from rbac_policies.json');
      } else {
        this.policies = this.defaultPolicy;
        // Create default config file
        await this.saveDefaultConfig();
        logger.info('✅ RBAC policies initialized with defaults');
      }
    } catch (error) {
      logger.error('❌ Failed to load RBAC policies, using defaults:', error.message);
      this.policies = this.defaultPolicy;
    }
  }

  /**
   * Save default configuration to file
   */
  async saveDefaultConfig() {
    try {
      const configPath = path.join(process.cwd(), 'rbac_policies.json');
      const configData = {
        policies: this.defaultPolicy,
        metadata: {
          created: new Date().toISOString(),
          version: '1.0.0',
          description: 'RBAC policies for Gemini CLI Wrapper Open WebUI integration'
        }
      };
      
      await fs.writeFile(configPath, JSON.stringify(configData, null, 2));
      logger.info('📝 Default RBAC config saved to rbac_policies.json');
    } catch (error) {
      logger.warn('⚠️ Failed to save default RBAC config:', error.message);
    }
  }

  /**
   * Extract user role from request
   * Supports multiple methods: JWT claims, headers, session data, API key mapping
   */
  extractUserRole(req) {
    // Method 1: Check custom header (recommended for Open WebUI)
    const roleHeader = req.headers['x-user-role'] || req.headers['x-openwebui-role'];
    if (roleHeader) {
      logger.debug(`🔍 Role from header: ${roleHeader}`);
      return roleHeader.toLowerCase();
    }

    // Method 2: Extract from JWT token in Authorization header
    try {
      const authHeader = req.headers.authorization;
      if (authHeader && authHeader.startsWith('Bearer ')) {
        const token = authHeader.substring(7);
        
        // Check if this is an API key instead of JWT
        const apiKeyRole = this.getApiKeyRole(token);
        if (apiKeyRole) {
          logger.debug(`🔍 Role from API key mapping: ${apiKeyRole}`);
          return apiKeyRole.toLowerCase();
        }
        
        const payload = this.decodeJWT(token);
        
        if (payload && payload.role) {
          logger.debug(`🔍 Role from JWT: ${payload.role}`);
          return payload.role.toLowerCase();
        }
        
        // Check for Open WebUI specific claims
        if (payload && payload.openwebui_role) {
          logger.debug(`🔍 Role from JWT openwebui_role: ${payload.openwebui_role}`);
          return payload.openwebui_role.toLowerCase();
        }
      }
    } catch (error) {
      logger.debug('JWT decode failed (non-critical):', error.message);
    }

    // Method 3: Check session data (if available)
    if (req.session && req.session.user_role) {
      logger.debug(`🔍 Role from session: ${req.session.user_role}`);
      return req.session.user_role.toLowerCase();
    }

    // Method 4: Check query parameter (for testing only)
    if (process.env.NODE_ENV !== 'production' && req.query.role) {
      logger.warn(`⚠️ Using role from query param (dev only): ${req.query.role}`);
      return req.query.role.toLowerCase();
    }

    // Method 5: Detect external clients (VS Code, Cline, etc.)
    const userAgent = req.headers['user-agent'] || '';
    const isExternalClient = this.isExternalClient(userAgent, req);
    
    if (isExternalClient) {
      const externalRole = this.getExternalClientRole(userAgent, req);
      logger.info(`🔍 External client detected: ${userAgent} -> role: ${externalRole}`);
      return externalRole.toLowerCase();
    }

    // Default to 'guest' role for unknown sources (more secure)
    logger.warn('🔍 No role found and not from known client, defaulting to: guest');
    return 'guest';
  }

  /**
   * Check if request is from external client (VS Code, Cline, etc.)
   */
  isExternalClient(userAgent, req) {
    const externalClientPatterns = [
      /vscode/i,
      /visual studio code/i,
      /cursor/i,
      /cline/i,
      /claude-dev/i,
      /continue/i,
      /aider/i,
      /copilot/i
    ];
    
    return externalClientPatterns.some(pattern => pattern.test(userAgent));
  }

  /**
   * Get role for external clients based on configuration
   */
  getExternalClientRole(userAgent, req) {
    // Check environment variable for external client role
    const externalRole = process.env.EXTERNAL_CLIENT_ROLE || 'user';
    
    // You can add more sophisticated logic here
    // For example, different roles for different clients
    if (/vscode|visual studio code|cursor/i.test(userAgent)) {
      return process.env.VSCODE_CLIENT_ROLE || externalRole;
    }
    
    if (/cline|claude-dev/i.test(userAgent)) {
      return process.env.CLINE_CLIENT_ROLE || externalRole;
    }
    
    return externalRole;
  }

  /**
   * Get role based on API key mapping
   */
  getApiKeyRole(apiKey) {
    // Load API key to role mappings from environment or config
    const apiKeyMappings = this.loadApiKeyMappings();
    return apiKeyMappings[apiKey] || null;
  }

  /**
   * Load API key to role mappings
   */
  loadApiKeyMappings() {
    try {
      // Try to load from environment variable first
      if (process.env.API_KEY_ROLE_MAPPINGS) {
        return JSON.parse(process.env.API_KEY_ROLE_MAPPINGS);
      }
      
      // Default mappings - you can extend this
      return {
        [process.env.ADMIN_API_KEY || 'admin-key']: 'admin',
        [process.env.USER_API_KEY || 'user-key']: 'user',
        [process.env.GUEST_API_KEY || 'guest-key']: 'guest'
      };
    } catch (error) {
      logger.warn('Failed to load API key mappings:', error.message);
      return {};
    }
  }

  /**
   * Simple JWT decoder (for extracting claims, not for verification)
   * Note: This is for claim extraction only, proper JWT verification should be done elsewhere
   */
  decodeJWT(token) {
    try {
      const parts = token.split('.');
      if (parts.length !== 3) return null;
      
      const payload = parts[1];
      const decoded = Buffer.from(payload, 'base64url').toString('utf8');
      return JSON.parse(decoded);
    } catch (error) {
      return null;
    }
  }

  /**
   * Check if a user role has permission to perform an action
   */
  checkPermission(userRole, action) {
    const role = userRole.toLowerCase();
    const policy = this.policies[role];
    
    if (!policy) {
      logger.warn(`❌ Unknown role: ${role}, denying access`);
      return false;
    }

    const allowedActions = policy.allowed_actions || [];
    
    // Check exact match
    if (allowedActions.includes(action)) {
      return true;
    }
    
    // Check wildcard patterns
    for (const allowedAction of allowedActions) {
      if (allowedAction.endsWith('*')) {
        const prefix = allowedAction.slice(0, -1);
        if (action.startsWith(prefix)) {
          return true;
        }
      }
    }
    
    return false;
  }

  /**
   * Check if an action is considered dangerous
   */
  isDangerousAction(action) {
    return this.dangerousActions.includes(action) || 
           this.dangerousActions.some(dangerous => 
             dangerous.endsWith('*') && action.startsWith(dangerous.slice(0, -1))
           );
  }

  /**
   * Get user permissions summary
   */
  getUserPermissions(userRole) {
    const role = userRole.toLowerCase();
    const policy = this.policies[role];
    
    if (!policy) {
      return {
        role: 'unknown',
        allowed_actions: [],
        description: 'Unknown role - no permissions'
      };
    }
    
    return {
      role,
      allowed_actions: policy.allowed_actions || [],
      description: policy.description || 'No description available'
    };
  }

  /**
   * Log access attempt for auditing
   */
  logAccess(userRole, action, allowed, req) {
    const logData = {
      user_role: userRole,
      action,
      allowed,
      ip: req.ip || req.connection.remoteAddress,
      user_agent: req.headers['user-agent'],
      timestamp: new Date().toISOString(),
      path: req.path,
      method: req.method
    };

    if (allowed) {
      logger.info(`✅ RBAC: ${userRole} allowed ${action}`, logData);
    } else {
      logger.warn(`❌ RBAC: ${userRole} denied ${action}`, logData);
    }
  }
}

// Singleton instance
export const rbacManager = new RBACManager();

/**
 * RBAC Middleware for Express
 * Extracts user role and adds to request context
 */
export const rbacMiddleware = (req, res, next) => {
  // Extract user role from request
  const userRole = rbacManager.extractUserRole(req);
  
  // Add role and RBAC context to request
  req.userRole = userRole;
  req.rbac = {
    checkPermission: (action) => rbacManager.checkPermission(userRole, action),
    isDangerousAction: (action) => rbacManager.isDangerousAction(action),
    getUserPermissions: () => rbacManager.getUserPermissions(userRole),
    logAccess: (action, allowed) => rbacManager.logAccess(userRole, action, allowed, req)
  };
  
  logger.debug(`🔐 RBAC: User role set to '${userRole}' for ${req.method} ${req.path}`);
  next();
};

/**
 * Tool execution authorization decorator
 * Use this to protect dangerous tool executions
 */
export const requirePermission = (action) => {
  return (req, res, next) => {
    const userRole = req.userRole || 'user';
    const allowed = rbacManager.checkPermission(userRole, action);
    
    // Log the access attempt
    rbacManager.logAccess(userRole, action, allowed, req);
    
    if (!allowed) {
      return res.status(403).json({
        error: 'Access denied',
        message: `Role '${userRole}' is not authorized to perform action '${action}'`,
        required_role: 'admin',
        current_role: userRole,
        action,
        timestamp: new Date().toISOString()
      });
    }
    
    next();
  };
};

/**
 * Enhanced tool execution wrapper with RBAC
 */
export const executeToolWithRBAC = async (toolName, args, req) => {
  const userRole = req.userRole || 'user';
  const allowed = rbacManager.checkPermission(userRole, toolName);
  
  // Log the access attempt
  rbacManager.logAccess(userRole, toolName, allowed, req);
  
  if (!allowed) {
    throw new Error(`Access denied: Role '${userRole}' is not authorized to execute tool '${toolName}'`);
  }
  
  // If this is a dangerous action, add extra logging
  if (rbacManager.isDangerousAction(toolName)) {
    logger.warn(`🚨 DANGEROUS ACTION: ${userRole} executing ${toolName}`, {
      args: JSON.stringify(args),
      ip: req.ip,
      timestamp: new Date().toISOString()
    });
  }
  
  return { authorized: true, userRole, action: toolName };
};