export const errorHandler = (err, req, res, next) => {
  console.error('❌ Error:', err);

  // Default error
  let status = 500;
  let message = 'Internal server error';
  let details = null;

  // Handle different error types
  if (err.name === 'ValidationError') {
    status = 400;
    message = 'Validation error';
    details = err.message;
  } else if (err.name === 'UnauthorizedError') {
    status = 401;
    message = 'Unauthorized';
    details = err.message;
  } else if (err.name === 'NotFoundError') {
    status = 404;
    message = 'Resource not found';
    details = err.message;
  } else if (err.message) {
    // Use the error message if available
    message = err.message;
    
    // Set appropriate status codes based on error content
    if (err.message.includes('not found')) {
      status = 404;
    } else if (err.message.includes('unauthorized') || err.message.includes('authentication')) {
      status = 401;
    } else if (err.message.includes('forbidden')) {
      status = 403;
    } else if (err.message.includes('timeout')) {
      status = 408;
    } else if (err.message.includes('rate limit')) {
      status = 429;
    }
  }

  // Send error response
  res.status(status).json({
    error: {
      message,
      status,
      timestamp: new Date().toISOString(),
      ...(details && { details }),
      ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
    }
  });
}; 