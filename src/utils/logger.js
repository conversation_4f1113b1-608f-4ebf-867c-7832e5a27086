import util from 'util';

const LOG_LEVELS = {
  debug: 0,
  info: 1,
  warn: 2,
  error: 3
};

const COLORS = {
  debug: '\x1b[36m',    // Cyan
  info: '\x1b[32m',     // Green
  warn: '\x1b[33m',     // Yellow
  error: '\x1b[31m',    // Red
  reset: '\x1b[0m'      // Reset
};

class Logger {
  constructor() {
    this.level = process.env.LOG_LEVEL || 'info';
    this.minLevel = LOG_LEVELS[this.level] || LOG_LEVELS.info;
  }

  formatMessage(level, message, ...args) {
    const timestamp = new Date().toISOString();
    const color = COLORS[level] || COLORS.reset;
    const formattedArgs = args.map(arg => 
      typeof arg === 'object' ? util.inspect(arg, { depth: 3, colors: true }) : arg
    );
    
    return `${color}[${timestamp}] ${level.toUpperCase()}: ${message}${COLORS.reset} ${formattedArgs.join(' ')}`;
  }

  log(level, message, ...args) {
    if (LOG_LEVELS[level] >= this.minLevel) {
      console.log(this.formatMessage(level, message, ...args));
    }
  }

  debug(message, ...args) {
    this.log('debug', message, ...args);
  }

  info(message, ...args) {
    this.log('info', message, ...args);
  }

  warn(message, ...args) {
    this.log('warn', message, ...args);
  }

  error(message, ...args) {
    this.log('error', message, ...args);
  }

  // Special methods for specific use cases
  request(req, res, next) {
    const start = Date.now();
    const { method, url, ip } = req;
    
    this.info(`${method} ${url} - ${ip}`);
    
    res.on('finish', () => {
      const duration = Date.now() - start;
      const { statusCode } = res;
      const color = statusCode >= 400 ? 'error' : statusCode >= 300 ? 'warn' : 'info';
      
      this.log(color, `${method} ${url} - ${statusCode} - ${duration}ms`);
    });
    
    if (next) next();
  }

  geminiCommand(command, args = []) {
    this.debug('Executing Gemini CLI:', { command, args });
  }

  geminiResponse(response, duration) {
    this.debug('Gemini CLI response received', { 
      responseLength: response?.length || 0, 
      duration: `${duration}ms` 
    });
  }

  auth(result, ip, userAgent) {
    const level = result === 'success' ? 'info' : 'warn';
    this.log(level, `Authentication ${result}`, { ip, userAgent });
  }
}

export const logger = new Logger();
export default logger;