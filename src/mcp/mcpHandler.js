import { toolRegistry } from '../tools/toolRegistry.js';
import { logger } from '../utils/logger.js';

/**
 * MCP (Model Context Protocol) Handler
 * Handles communication between the AI model and tools
 */
export class MCPHandler {
  constructor() {
    this.sessions = new Map();
    this.defaultSession = 'default';
  }

  /**
   * Initialize a new MCP session
   */
  initializeSession(sessionId = this.defaultSession) {
    if (!this.sessions.has(sessionId)) {
      this.sessions.set(sessionId, {
        id: sessionId,
        created: new Date(),
        context: new Map(),
        toolCalls: [],
        lastActivity: new Date()
      });
      logger.debug(`Initialized MCP session: ${sessionId}`);
    }
    return this.sessions.get(sessionId);
  }

  /**
   * Process tool calls from messages following OpenAI format
   */
  async processToolCalls(messages, tools = [], sessionId = this.defaultSession) {
    const session = this.initializeSession(sessionId);
    session.lastActivity = new Date();

    const results = [];
    const toolCallsNeeded = [];

    // Extract tool calls from messages
    for (const message of messages) {
      if (message.role === 'assistant' && message.tool_calls) {
        for (const toolCall of message.tool_calls) {
          toolCallsNeeded.push(toolCall);
        }
      }
    }

    // If no explicit tool calls, check if we need to infer them
    if (toolCallsNeeded.length === 0) {
      const lastMessage = messages[messages.length - 1];
      if (lastMessage.role === 'user') {
        const inferredCalls = await this.inferToolCalls(lastMessage.content, tools);
        toolCallsNeeded.push(...inferredCalls);
      }
    }

    // Execute tool calls
    for (const toolCall of toolCallsNeeded) {
      try {
        const result = await this.executeToolCall(toolCall, sessionId);
        results.push(result);
      } catch (error) {
        logger.error(`Tool call failed:`, error);
        results.push({
          tool_call_id: toolCall.id,
          success: false,
          error: error.message,
          source: 'mcp_handler'
        });
      }
    }

    return results;
  }

  /**
   * Execute a single tool call
   */
  async executeToolCall(toolCall, sessionId = this.defaultSession) {
    const session = this.sessions.get(sessionId);
    
    logger.debug(`Executing tool call: ${toolCall.function?.name || toolCall.name}`);

    const toolName = toolCall.function?.name || toolCall.name;
    const args = toolCall.function?.arguments || toolCall.arguments;

    // Parse arguments if they're a string
    let parsedArgs = args;
    if (typeof args === 'string') {
      try {
        parsedArgs = JSON.parse(args);
      } catch (error) {
        throw new Error(`Invalid tool arguments JSON: ${error.message}`);
      }
    }

    // Execute the tool
    const result = await toolRegistry.executeTool(toolName, parsedArgs);

    // Store in session context
    const callRecord = {
      id: toolCall.id || `call_${Date.now()}`,
      tool: toolName,
      arguments: parsedArgs,
      result,
      timestamp: new Date()
    };

    session.toolCalls.push(callRecord);
    session.context.set(`tool_${toolName}_last`, result);

    return {
      tool_call_id: callRecord.id,
      role: 'tool',
      name: toolName,
      content: JSON.stringify(result),
      success: true,
      source: 'mcp_handler'
    };
  }

  /**
   * Infer tool calls from natural language
   */
  async inferToolCalls(content, availableTools = []) {
    const toolCalls = [];
    const lowerContent = content.toLowerCase();

    // Simple heuristics for common tool needs
    if (lowerContent.includes('search') || lowerContent.includes('find online') || lowerContent.includes('web')) {
      const searchMatch = content.match(/search\s+(?:for\s+)?(.+?)(?:\.|$|\?)/i);
      if (searchMatch) {
        toolCalls.push({
          id: `call_${Date.now()}_search`,
          type: 'function',
          function: {
            name: 'web_search',
            arguments: JSON.stringify({ query: searchMatch[1].trim() })
          }
        });
      }
    }

    if (lowerContent.includes('read file') || lowerContent.includes('analyze file')) {
      const fileMatch = content.match(/(?:read|analyze)\s+(?:file\s+)?(.+?)(?:\.|$|\?)/i);
      if (fileMatch) {
        toolCalls.push({
          id: `call_${Date.now()}_read`,
          type: 'function',
          function: {
            name: 'read_file',
            arguments: JSON.stringify({ path: fileMatch[1].trim() })
          }
        });
      }
    }

    if (lowerContent.includes('execute') || lowerContent.includes('run command')) {
      const cmdMatch = content.match(/(?:execute|run)\s+(?:command\s+)?(.+?)(?:\.|$|\?)/i);
      if (cmdMatch) {
        toolCalls.push({
          id: `call_${Date.now()}_exec`,
          type: 'function',
          function: {
            name: 'execute_command',
            arguments: JSON.stringify({ command: cmdMatch[1].trim() })
          }
        });
      }
    }

    return toolCalls;
  }

  /**
   * Format tools for OpenAI-compatible API
   */
  formatToolsForAPI() {
    const tools = toolRegistry.listTools();
    return tools.map(tool => ({
      type: 'function',
      function: {
        name: tool.name,
        description: tool.description,
        parameters: tool.inputSchema
      }
    }));
  }

  /**
   * Get session context
   */
  getSessionContext(sessionId = this.defaultSession) {
    const session = this.sessions.get(sessionId);
    if (!session) return null;

    return {
      id: session.id,
      created: session.created,
      lastActivity: session.lastActivity,
      toolCallsCount: session.toolCalls.length,
      availableTools: toolRegistry.listTools().map(t => t.name),
      recentToolCalls: session.toolCalls.slice(-5).map(call => ({
        tool: call.tool,
        timestamp: call.timestamp,
        success: !!call.result.success
      }))
    };
  }

  /**
   * Enhanced message processing with tool integration
   */
  async enhanceMessages(messages, sessionId = this.defaultSession) {
    const session = this.initializeSession(sessionId);
    const enhancedMessages = [...messages];

    // Add system message about available tools if not present
    const hasSystemMessage = messages.some(m => m.role === 'system');
    if (!hasSystemMessage) {
      const availableTools = toolRegistry.listTools();
      const toolDescriptions = availableTools.map(tool => 
        `- ${tool.name}: ${tool.description}`
      ).join('\n');

      enhancedMessages.unshift({
        role: 'system',
        content: `You are an AI assistant with powerful MCP (Model Context Protocol) capabilities. You have access to the following tools and can use them automatically:

## 🛠️ Available Tools:
${toolDescriptions}

## 🎯 How to Use Tools:

### Automatic Tool Usage:
You can use tools naturally by understanding user intent:
- "Search for..." → automatically use web_search
- "Read the file..." → automatically use read_file
- "Execute command..." → automatically use execute_command
- "Analyze the code..." → automatically use analyze_code
- "Remember this..." → automatically use store_memory
- "Take a screenshot of..." → automatically use browser_launch + browser_action
- "Open website..." → automatically use browser_launch
- "Click on..." → automatically use browser_action
- "Fill out form..." → automatically use browser_action (type, click)
- "Search Open WebUI..." → automatically use openwebui_web_search
- "Create chat in Open WebUI..." → automatically use openwebui_create_chat
- "Send message to Open WebUI..." → automatically use openwebui_send_message
- "Get Open WebUI conversations..." → automatically use openwebui_get_conversations

### Tool Chaining:
You can chain multiple tools in sequence:
1. Search for information → web_search
2. Save findings → store_memory
3. Read project files → read_file
4. Analyze and provide recommendations

**Browser Automation Chains:**
1. Website testing → browser_launch → browser_action (screenshot) → browser_close
2. Form automation → browser_launch → browser_action (type, click) → browser_close
3. Web scraping → browser_launch → browser_action (evaluate) → store_memory → browser_close

### Session Context:
- Session ID: ${session.id}
- Tool calls made: ${session.toolCalls.length}
- Last activity: ${session.lastActivity.toISOString()}
- You can reference previous tool results and maintain context

## 🚀 Instructions:
1. **Be proactive**: Use tools when they would help answer the user's question
2. **Chain tools**: Combine multiple tools for comprehensive responses
3. **Explain your actions**: Tell the user what tools you're using and why
4. **Use session memory**: Store important information for later reference
5. **Handle errors gracefully**: If a tool fails, try alternatives or explain the limitation

## 🔒 Security:
- File operations are restricted to the project directory (unless unrestricted mode is enabled)
- Command execution is limited to safe commands in restricted mode
- Always prioritize user safety and data security

## 🌐 Open WebUI Integration:
- Use Open WebUI tools when users mention Open WebUI specifically
- For Open WebUI API access, check if OPEN_WEBUI_API_KEY is configured
- If API calls fail with 403/401, inform user about API key requirement
- Browser automation requires ENABLE_BROWSER_AUTOMATION=true
- Default Open WebUI URL is http://localhost:3000

**Open WebUI Tool Usage:**
- Web search → openwebui_web_search
- Chat management → openwebui_create_chat, openwebui_send_message
- File uploads → openwebui_upload_file
- UI automation → openwebui_browser_action
- Model info → openwebui_get_models

You are intelligent, helpful, and capable of using these tools effectively to provide comprehensive assistance.`
      });
    }

    return enhancedMessages;
  }

  /**
   * Clean up expired sessions
   */
  cleanupSessions(maxAge = 3600000) { // 1 hour default
    const now = Date.now();
    for (const [sessionId, session] of this.sessions.entries()) {
      if (now - session.lastActivity.getTime() > maxAge) {
        this.sessions.delete(sessionId);
        logger.debug(`Cleaned up expired session: ${sessionId}`);
      }
    }
  }

  /**
   * Get MCP server info
   */
  getServerInfo() {
    return {
      name: 'Gemini CLI Wrapper MCP Server',
      version: '1.0.0',
      description: 'MCP server for Gemini CLI with tool integration',
      capabilities: {
        tools: true,
        resources: true,
        prompts: true,
        logging: true
      },
      tools: toolRegistry.listTools(),
      activeSessions: this.sessions.size,
      supportedProtocols: ['mcp-0.1.0', 'openai-tools']
    };
  }
}

export const mcpHandler = new MCPHandler(); 