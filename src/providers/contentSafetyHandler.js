import { logger } from '../utils/logger.js';

/**
 * Content Safety Handler - Configurable Gemini Moderation Settings
 * Provides fine-grained control over Gemini's built-in safety filters
 */
export class ContentSafetyHandler {
  constructor() {
    // Safety categories supported by Gemini
    this.safetyCategories = {
      HARASSMENT: 'HARM_CATEGORY_HARASSMENT',
      HATE_SPEECH: 'HARM_CATEGORY_HATE_SPEECH', 
      SEXUALLY_EXPLICIT: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
      DANGEROUS_CONTENT: 'HARM_CATEGORY_DANGEROUS_CONTENT'
    };
    
    // Safety thresholds
    this.safetyThresholds = {
      BLOCK_NONE: 'BLOCK_NONE',
      BLOCK_FEW: 'BLOCK_FEW',
      BLOCK_SOME: 'BLOCK_SOME',
      BLOCK_ONLY_HIGH: 'BLOCK_ONLY_HIGH',
      HARM_BLOCK_THRESHOLD_UNSPECIFIED: 'HARM_BLOCK_THRESHOLD_UNSPECIFIED'
    };
    
    // Load configuration from environment
    this.config = this.loadSafetyConfig();
    
    logger.info('🛡️ Content Safety Handler initialized', {
      config: this.config
    });
  }

  /**
   * Load safety configuration from environment variables
   */
  loadSafetyConfig() {
    return {
      harassment: process.env.GEMINI_MODERATION_HARASSMENT_THRESHOLD || 'BLOCK_SOME',
      hateSpeech: process.env.GEMINI_MODERATION_HATE_SPEECH_THRESHOLD || 'BLOCK_SOME',
      sexuallyExplicit: process.env.GEMINI_MODERATION_SEXUALLY_EXPLICIT_THRESHOLD || 'BLOCK_SOME',
      dangerousContent: process.env.GEMINI_MODERATION_DANGEROUS_CONTENT_THRESHOLD || 'BLOCK_ONLY_HIGH',
      
      // Global safety settings
      enableSafety: process.env.GEMINI_ENABLE_SAFETY === 'true', // Default disabled
      strictMode: process.env.GEMINI_STRICT_MODE === 'true', // Default disabled
      customRules: this.parseCustomRules(process.env.GEMINI_CUSTOM_SAFETY_RULES)
    };
  }

  /**
   * Parse custom safety rules from environment
   */
  parseCustomRules(rulesString) {
    if (!rulesString) return [];
    
    try {
      return JSON.parse(rulesString);
    } catch (error) {
      logger.warn('Failed to parse custom safety rules:', error.message);
      return [];
    }
  }

  /**
   * Validate safety threshold
   */
  isValidThreshold(threshold) {
    return Object.values(this.safetyThresholds).includes(threshold);
  }

  /**
   * Get safety settings for Gemini CLI
   */
  getSafetySettings(options = {}) {
    if (!this.config.enableSafety) {
      return [];
    }
    
    // Override with request-specific settings
    const harassment = options.harassment_threshold || this.config.harassment;
    const hateSpeech = options.hate_speech_threshold || this.config.hateSpeech;
    const sexuallyExplicit = options.sexually_explicit_threshold || this.config.sexuallyExplicit;
    const dangerousContent = options.dangerous_content_threshold || this.config.dangerousContent;
    
    const safetySettings = [
      {
        category: this.safetyCategories.HARASSMENT,
        threshold: harassment
      },
      {
        category: this.safetyCategories.HATE_SPEECH,
        threshold: hateSpeech
      },
      {
        category: this.safetyCategories.SEXUALLY_EXPLICIT,
        threshold: sexuallyExplicit
      },
      {
        category: this.safetyCategories.DANGEROUS_CONTENT,
        threshold: dangerousContent
      }
    ];
    
    // Filter out invalid thresholds
    return safetySettings.filter(setting => this.isValidThreshold(setting.threshold));
  }

  /**
   * Add safety parameters to Gemini CLI command
   */
  addSafetyToCommand(args, options = {}) {
    if (!this.config.enableSafety) {
      return args;
    }
    
    const safetySettings = this.getSafetySettings(options);
    const enhancedArgs = [...args];
    
    // Add safety settings as CLI parameters
    for (const setting of safetySettings) {
      enhancedArgs.push('--safety-setting');
      enhancedArgs.push(`${setting.category}=${setting.threshold}`);
    }
    
    // Add strict mode if enabled
    if (this.config.strictMode || options.strict_mode) {
      enhancedArgs.push('--strict-safety');
    }
    
    logger.debug('Added safety parameters to Gemini CLI command', {
      safetySettings,
      strictMode: this.config.strictMode || options.strict_mode,
      originalArgs: args,
      enhancedArgs
    });
    
    return enhancedArgs;
  }

  /**
   * Process safety response from Gemini CLI
   */
  processSafetyResponse(response, options = {}) {
    if (!response || typeof response !== 'string') {
      return { content: response, safetyRatings: null, blocked: false };
    }
    
    // Check for safety blocks in response
    const safetyBlockPatterns = [
      /blocked.*safety/i,
      /content.*filtered/i,
      /safety.*violation/i,
      /inappropriate.*content/i
    ];
    
    const isBlocked = safetyBlockPatterns.some(pattern => pattern.test(response));
    
    if (isBlocked) {
      logger.warn('Content blocked by safety filters', {
        response: response.substring(0, 200),
        safetyConfig: this.config
      });
      
      return {
        content: null,
        safetyRatings: this.extractSafetyRatings(response),
        blocked: true,
        blockReason: 'Content blocked by safety filters'
      };
    }
    
    return {
      content: response,
      safetyRatings: this.extractSafetyRatings(response),
      blocked: false
    };
  }

  /**
   * Extract safety ratings from response (if available)
   */
  extractSafetyRatings(response) {
    // Try to extract safety ratings from Gemini CLI response
    // This is a placeholder - actual implementation depends on CLI output format
    const safetyMatch = response.match(/safety_ratings:\s*(\{.*?\})/s);
    if (safetyMatch) {
      try {
        return JSON.parse(safetyMatch[1]);
      } catch (error) {
        logger.debug('Failed to parse safety ratings:', error.message);
      }
    }
    
    return null;
  }

  /**
   * Update safety configuration
   */
  updateSafetyConfig(newConfig) {
    const validConfig = {};
    
    // Validate and update thresholds
    if (newConfig.harassment && this.isValidThreshold(newConfig.harassment)) {
      validConfig.harassment = newConfig.harassment;
      process.env.GEMINI_MODERATION_HARASSMENT_THRESHOLD = newConfig.harassment;
    }
    
    if (newConfig.hateSpeech && this.isValidThreshold(newConfig.hateSpeech)) {
      validConfig.hateSpeech = newConfig.hateSpeech;
      process.env.GEMINI_MODERATION_HATE_SPEECH_THRESHOLD = newConfig.hateSpeech;
    }
    
    if (newConfig.sexuallyExplicit && this.isValidThreshold(newConfig.sexuallyExplicit)) {
      validConfig.sexuallyExplicit = newConfig.sexuallyExplicit;
      process.env.GEMINI_MODERATION_SEXUALLY_EXPLICIT_THRESHOLD = newConfig.sexuallyExplicit;
    }
    
    if (newConfig.dangerousContent && this.isValidThreshold(newConfig.dangerousContent)) {
      validConfig.dangerousContent = newConfig.dangerousContent;
      process.env.GEMINI_MODERATION_DANGEROUS_CONTENT_THRESHOLD = newConfig.dangerousContent;
    }
    
    // Update boolean settings
    if (typeof newConfig.enableSafety === 'boolean') {
      validConfig.enableSafety = newConfig.enableSafety;
      process.env.GEMINI_ENABLE_SAFETY = newConfig.enableSafety.toString();
    }
    
    if (typeof newConfig.strictMode === 'boolean') {
      validConfig.strictMode = newConfig.strictMode;
      process.env.GEMINI_STRICT_MODE = newConfig.strictMode.toString();
    }
    
    // Update configuration
    this.config = { ...this.config, ...validConfig };
    
    logger.info('Safety configuration updated', {
      updatedConfig: validConfig,
      fullConfig: this.config
    });
    
    return this.config;
  }

  /**
   * Get current safety configuration
   */
  getSafetyConfig() {
    return {
      ...this.config,
      availableThresholds: Object.keys(this.safetyThresholds),
      availableCategories: Object.keys(this.safetyCategories)
    };
  }

  /**
   * Check if content should be pre-filtered
   */
  shouldPreFilter(content, options = {}) {
    if (!this.config.enableSafety) {
      return false;
    }
    
    // Simple pre-filtering for obviously problematic content
    const strictPatterns = [
      /explicit.*sexual/i,
      /graphic.*violence/i,
      /hate.*speech/i,
      /terrorist.*instructions/i
    ];
    
    if (this.config.strictMode || options.strict_mode) {
      return strictPatterns.some(pattern => pattern.test(content));
    }
    
    return false;
  }

  /**
   * Generate safety-compliant error message
   */
  generateSafetyErrorMessage(blockReason, safetyRatings = null) {
    const baseMessage = "I can't provide a response to that request due to safety guidelines.";
    
    if (safetyRatings) {
      const categories = Object.keys(safetyRatings).join(', ');
      return `${baseMessage} The content was flagged in the following categories: ${categories}.`;
    }
    
    return `${baseMessage} ${blockReason || 'Please try rephrasing your request.'}`;
  }

  /**
   * Get safety statistics
   */
  getSafetyStats() {
    // This would be implemented with actual usage tracking
    return {
      totalRequests: 0,
      blockedRequests: 0,
      blockRate: 0,
      topBlockReasons: [],
      safetyConfig: this.config
    };
  }
}

export const contentSafetyHandler = new ContentSafetyHandler();