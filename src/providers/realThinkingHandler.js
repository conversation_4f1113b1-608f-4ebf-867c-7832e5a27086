import { logger } from '../utils/logger.js';

/**
 * Real Thinking Handler - Native Gemini Thinking Support
 * Replaces fake thinking with actual Gemini reasoning capabilities
 */
export class RealThinkingHandler {
  constructor() {
    this.thinkingModels = new Set([
      'gemini-2.5-pro',
      'gemini-2.5-flash'
    ]);
    
    // Configuration from environment
    this.enableRealThinking = process.env.ENABLE_REAL_THINKING === 'true';
    this.enableFakeThinking = process.env.ENABLE_FAKE_THINKING === 'true';
    this.streamThinkingAsContent = process.env.STREAM_THINKING_AS_CONTENT === 'true';
    // Some CLI versions do not support reasoning flags. Gate behind an explicit flag.
    this.cliSupportsReasoning = process.env.GEMINI_CLI_SUPPORTS_REASONING === 'true';
    
    // Default thinking budget (-1 = dynamic allocation by Gemini)
    this.defaultThinkingBudget = parseInt(process.env.DEFAULT_THINKING_BUDGET) || -1;
    
    logger.info('🧠 Real Thinking Handler initialized', {
      enableRealThinking: this.enableRealThinking,
      enableFakeThinking: this.enableFakeThinking,
      streamThinkingAsContent: this.streamThinkingAsContent,
      defaultThinkingBudget: this.defaultThinkingBudget,
      cliSupportsReasoning: this.cliSupportsReasoning
    });
  }

  /**
   * Check if model supports thinking
   */
  isThinkingModel(modelId) {
    return this.thinkingModels.has(modelId);
  }

  /**
   * Check if real thinking is enabled for this request
   */
  shouldUseRealThinking(modelId, options = {}) {
    if (!this.isThinkingModel(modelId)) {
      return false;
    }
    
    // Short-circuit if CLI does not support reasoning flags
    if (!this.cliSupportsReasoning) {
      if (this.enableRealThinking) {
        logger.warn('Gemini CLI does not support reasoning flags on this version. Disabling real thinking for this request.');
      }
      return false;
    }

    // Check if explicitly disabled in request
    if (options.include_reasoning === false) {
      return false;
    }
    
    // Check if thinking budget is set to 0 (disabled)
    if (options.thinking_budget === 0) {
      return false;
    }
    
    // Check reasoning effort
    if (options.reasoning_effort === 'none') {
      return false;
    }
    
    return this.enableRealThinking;
  }

  /**
   * Should use fake thinking as fallback
   */
  shouldUseFakeThinking(modelId, options = {}) {
    if (!this.isThinkingModel(modelId)) {
      return false;
    }
    
    // Use fake thinking if real thinking is disabled but fake is enabled
    if (!this.enableRealThinking && this.enableFakeThinking) {
      return true;
    }
    
    return false;
  }

  /**
   * Calculate thinking budget based on reasoning effort
   */
  calculateThinkingBudget(options = {}) {
    // If explicitly set, use that value
    if (typeof options.thinking_budget === 'number') {
      return options.thinking_budget;
    }
    
    // Calculate based on reasoning effort
    const reasoningEffort = options.reasoning_effort || 'medium';
    const modelId = options.model || 'gemini-2.5-pro';
    
    switch (reasoningEffort) {
      case 'none':
        return 0;
      case 'low':
        return 1024;
      case 'medium':
        return modelId.includes('flash') ? 12288 : 16384;
      case 'high':
        return modelId.includes('flash') ? 24576 : 32768;
      default:
        return this.defaultThinkingBudget;
    }
  }

  /**
   * Prepare thinking parameters for Gemini CLI
   */
  prepareThinkingParameters(options = {}) {
    if (!this.shouldUseRealThinking(options.model, options)) {
      return {};
    }
    
    const thinkingBudget = this.calculateThinkingBudget(options);
    const includeReasoning = options.include_reasoning !== false;
    
    return {
      include_reasoning: includeReasoning,
      thinking_budget: thinkingBudget,
      reasoning_effort: options.reasoning_effort || 'medium'
    };
  }

  /**
   * Add thinking parameters to Gemini CLI command
   */
  addThinkingToCommand(args, options = {}) {
    const thinkingParams = this.prepareThinkingParameters(options);
    
    if (Object.keys(thinkingParams).length === 0) {
      return args;
    }
    
    const enhancedArgs = [...args];
    
    // Add thinking budget parameter
    if (typeof thinkingParams.thinking_budget === 'number') {
      enhancedArgs.push('--thinking-budget', thinkingParams.thinking_budget.toString());
    }
    
    // Add include reasoning flag
    if (thinkingParams.include_reasoning) {
      enhancedArgs.push('--include-reasoning');
    }
    
    logger.debug('Added thinking parameters to Gemini CLI command', {
      thinkingParams,
      originalArgs: args,
      enhancedArgs
    });
    
    return enhancedArgs;
  }

  /**
   * Process thinking response from Gemini CLI
   */
  processThinkingResponse(response, options = {}) {
    if (!response || typeof response !== 'string') {
      return { content: response, reasoning: null };
    }
    
    // Try to extract reasoning from response
    const reasoningMatch = response.match(/<reasoning>(.*?)<\/reasoning>/s);
    if (reasoningMatch) {
      const reasoning = reasoningMatch[1].trim();
      const content = response.replace(/<reasoning>.*?<\/reasoning>/s, '').trim();
      
      return {
        content,
        reasoning,
        hasRealThinking: true
      };
    }
    
    // Check for thinking tags (alternative format)
    const thinkingMatch = response.match(/<thinking>(.*?)<\/thinking>/s);
    if (thinkingMatch) {
      const reasoning = thinkingMatch[1].trim();
      const content = response.replace(/<thinking>.*?<\/thinking>/s, '').trim();
      
      return {
        content,
        reasoning,
        hasRealThinking: true
      };
    }
    
    // No thinking content found
    return {
      content: response,
      reasoning: null,
      hasRealThinking: false
    };
  }

  /**
   * Stream thinking content with proper formatting
   */
  async streamThinking(thinkingContent, onChunk, options = {}) {
    if (!thinkingContent || !thinkingContent.reasoning) {
      return;
    }
    
    const model = options.model || 'gemini-2.5-pro';
    const reasoning = thinkingContent.reasoning;
    
    if (this.streamThinkingAsContent) {
      // Stream as content with thinking tags (R1 style)
      
      // Send opening tag
      onChunk({
        id: `gemini-${Date.now()}`,
        object: 'chat.completion.chunk',
        created: Math.floor(Date.now() / 1000),
        model,
        choices: [{
          index: 0,
          delta: {
            content: '<thinking>\n'
          },
          finish_reason: null
        }]
      });
      
      // Stream reasoning content in chunks
      const chunks = this.splitReasoningIntoChunks(reasoning);
      
      for (let i = 0; i < chunks.length; i++) {
        const chunk = chunks[i];
        
        onChunk({
          id: `gemini-${Date.now()}`,
          object: 'chat.completion.chunk',
          created: Math.floor(Date.now() / 1000),
          model,
          choices: [{
            index: 0,
            delta: {
              content: chunk
            },
            finish_reason: null
          }]
        });
        
        // Small delay between chunks for natural streaming
        if (i < chunks.length - 1) {
          await this.sleep(50);
        }
      }
      
      // Send closing tag
      onChunk({
        id: `gemini-${Date.now()}`,
        object: 'chat.completion.chunk',
        created: Math.floor(Date.now() / 1000),
        model,
        choices: [{
          index: 0,
          delta: {
            content: '\n</thinking>\n\n'
          },
          finish_reason: null
        }]
      });
      
    } else {
      // Stream as reasoning field (original mode)
      const chars = Array.from(reasoning);
      
      for (let i = 0; i < chars.length; i++) {
        const char = chars[i];
        
        onChunk({
          id: `gemini-${Date.now()}`,
          object: 'chat.completion.chunk',
          created: Math.floor(Date.now() / 1000),
          model,
          choices: [{
            index: 0,
            delta: {
              reasoning: char
            },
            finish_reason: null
          }]
        });
        
        // Character-by-character delay
        if (i < chars.length - 1) {
          await this.sleep(30);
        }
      }
    }
  }

  /**
   * Split reasoning into natural chunks for streaming
   */
  splitReasoningIntoChunks(reasoning, chunkSize = 100) {
    const chunks = [];
    let remainingText = reasoning;
    
    while (remainingText.length > 0) {
      if (remainingText.length <= chunkSize) {
        chunks.push(remainingText);
        break;
      }
      
      // Find natural break points
      let chunkEnd = chunkSize;
      const searchSpace = remainingText.substring(0, chunkEnd + 20);
      
      // Look for good break points
      const breakPoints = ['\n\n', '. ', '? ', '! ', '\n', ', ', ' '];
      
      for (const breakPoint of breakPoints) {
        const lastBreak = searchSpace.lastIndexOf(breakPoint);
        if (lastBreak > chunkSize * 0.7) {
          chunkEnd = lastBreak + breakPoint.length;
          break;
        }
      }
      
      chunks.push(remainingText.substring(0, chunkEnd));
      remainingText = remainingText.substring(chunkEnd);
    }
    
    return chunks;
  }

  /**
   * Add thinking to completion response
   */
  addThinkingToResponse(response, thinkingContent) {
    if (!thinkingContent || !thinkingContent.reasoning) {
      return response;
    }
    
    if (this.streamThinkingAsContent) {
      // For R1 style, thinking is included in content
      const thinkingText = `<thinking>\n${thinkingContent.reasoning}\n</thinking>\n\n`;
      
      return {
        ...response,
        choices: response.choices.map(choice => ({
          ...choice,
          message: {
            ...choice.message,
            content: thinkingText + choice.message.content
          }
        }))
      };
    } else {
      // Add as reasoning field
      return {
        ...response,
        reasoning: thinkingContent.reasoning,
        choices: response.choices.map(choice => ({
          ...choice,
          message: {
            ...choice.message,
            reasoning: thinkingContent.reasoning
          }
        }))
      };
    }
  }

  /**
   * Get thinking configuration for debugging
   */
  getThinkingConfig() {
    return {
      enableRealThinking: this.enableRealThinking,
      enableFakeThinking: this.enableFakeThinking,
      streamThinkingAsContent: this.streamThinkingAsContent,
      defaultThinkingBudget: this.defaultThinkingBudget,
      supportedModels: Array.from(this.thinkingModels)
    };
  }

  /**
   * Sleep utility
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

export const realThinkingHandler = new RealThinkingHandler();