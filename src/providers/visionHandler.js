import { logger } from '../utils/logger.js';

export class VisionHandler {
  constructor() {
    this.supportedFormats = ['jpeg', 'jpg', 'png', 'gif', 'webp'];
    this.maxImageSize = 20 * 1024 * 1024; // 20MB
  }

  /**
   * Check if model supports vision
   */
  isVisionCapable(modelId) {
    const visionModels = [
      'gemini-2.5-pro',
      'gemini-2.5-flash', 
      'gemini-1.5-pro',
      'gemini-1.5-flash',
      'gemini-2.0-flash-exp'
    ];
    return visionModels.includes(modelId);
  }

  /**
   * Process messages with images
   */
  async processVisionMessages(messages) {
    const processedMessages = [];

    for (const message of messages) {
      if (typeof message.content === 'string') {
        processedMessages.push(message);
        continue;
      }

      // Handle array content (text + images)
      if (Array.isArray(message.content)) {
        let textContent = '';
        const images = [];

        for (const content of message.content) {
          if (content.type === 'text') {
            textContent += content.text + '\n';
          } else if (content.type === 'image_url') {
            const imageData = await this.processImage(content.image_url);
            if (imageData) {
              images.push(imageData);
            }
          }
        }

        processedMessages.push({
          ...message,
          content: textContent.trim(),
          images: images.length > 0 ? images : undefined
        });
      } else {
        processedMessages.push(message);
      }
    }

    return processedMessages;
  }

  /**
   * Process single image
   */
  async processImage(imageUrl) {
    try {
      let imageData;
      
      if (imageUrl.url.startsWith('data:')) {
        // Base64 encoded image
        imageData = this.processBase64Image(imageUrl.url);
      } else {
        // External URL
        imageData = await this.fetchExternalImage(imageUrl.url);
      }

      if (!imageData) {
        logger.warn('Failed to process image:', imageUrl.url.substring(0, 50) + '...');
        return null;
      }

      return {
        mimeType: imageData.mimeType,
        data: imageData.data
      };
    } catch (error) {
      logger.error('Error processing image:', error);
      return null;
    }
  }

  /**
   * Process base64 image
   */
  processBase64Image(dataUrl) {
    try {
      const [header, data] = dataUrl.split(',');
      const mimeMatch = header.match(/data:([^;]+)/);
      
      if (!mimeMatch) {
        throw new Error('Invalid data URL format');
      }

      const mimeType = mimeMatch[1];
      const format = mimeType.split('/')[1];

      if (!this.supportedFormats.includes(format.toLowerCase())) {
        throw new Error(`Unsupported image format: ${format}`);
      }

      // Check size (approximate)
      const sizeInBytes = (data.length * 3) / 4;
      if (sizeInBytes > this.maxImageSize) {
        throw new Error(`Image too large: ${Math.round(sizeInBytes / 1024 / 1024)}MB`);
      }

      return {
        mimeType,
        data: data
      };
    } catch (error) {
      logger.error('Error processing base64 image:', error);
      return null;
    }
  }

  /**
   * Fetch external image
   */
  async fetchExternalImage(url) {
    try {
      const response = await fetch(url, {
        headers: {
          'User-Agent': 'Gemini-CLI-Wrapper/1.0'
        },
        timeout: 10000 // 10 seconds
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.startsWith('image/')) {
        throw new Error(`Invalid content type: ${contentType}`);
      }

      const buffer = await response.arrayBuffer();
      if (buffer.byteLength > this.maxImageSize) {
        throw new Error(`Image too large: ${Math.round(buffer.byteLength / 1024 / 1024)}MB`);
      }

      const base64Data = Buffer.from(buffer).toString('base64');

      return {
        mimeType: contentType,
        data: base64Data
      };
    } catch (error) {
      logger.error('Error fetching external image:', error);
      return null;
    }
  }

  /**
   * Format images for Gemini CLI
   */
  formatImagesForCLI(images) {
    if (!images || images.length === 0) {
      return '';
    }

    return images.map(img => `[Image: ${img.mimeType}]`).join('\n');
  }
}

export const visionHandler = new VisionHandler();