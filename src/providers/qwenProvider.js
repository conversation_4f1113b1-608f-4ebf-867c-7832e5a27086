import { EventEmitter } from 'events';
import fetch from 'node-fetch';
import { logger } from '../utils/logger.js';
import { mcpHandler } from '../mcp/mcpHandler.js';
import { visionHandler } from './visionHandler.js';
import { thinkingHandler } from './thinkingHandler.js';

/**
 * Qwen OpenAI-compatible Provider
 *
 * This provider talks to any OpenAI-compatible endpoint that serves Qwen models
 * (e.g., Alibaba Cloud DashScope compatible mode, ModelScope, OpenRouter, etc.).
 *
 * Required env vars (examples in env.example):
 * - QWEN_API_KEY
 * - QWEN_API_BASE_URL (e.g., https://dashscope.aliyuncs.com/compatible-mode/v1 or https://openrouter.ai/api/v1)
 */
export class QwenCLIProvider extends EventEmitter {
  constructor() {
    super();
    this.isInitialized = false;
    this.sessionId = null;
    this.customModels = [];
    this.apiKeys = [];
    this.useOAuth = String(process.env.QWEN_USE_OAUTH).toLowerCase() === 'true';
    this.initialize();
  }

  async initialize() {
    try {
      this.defaultModel = process.env.QWEN_DEFAULT_MODEL || 'qwen3-coder-plus';
      this.isInitialized = true;
      logger.info('✅ Qwen Provider initialized successfully');
    } catch (error) {
      logger.error('❌ Failed to initialize Qwen Provider:', error);
      throw error;
    }
  }

  async setupAuthentication() {
    if (this.useOAuth) {
      if (!process.env.QWEN_OAUTH_TOKEN) {
        logger.warn('⚠️ QWEN_USE_OAUTH=true but QWEN_OAUTH_TOKEN is not set. Using mock until token is provided.');
      }
    } else if (!process.env.QWEN_API_KEY) {
      logger.warn('⚠️ QWEN_API_KEY is not set and OAuth not enabled. The provider will fallback to mock responses.');
    }
    if (!process.env.QWEN_API_BASE_URL) {
      logger.warn('⚠️ QWEN_API_BASE_URL is not set. Using default OpenRouter base URL.');
    }
  }

  async getAvailableModels() {
    // OpenAI-compatible provider - return empty by default since we prefer Qwen Code CLI
    // Users can add custom models via API if they want to use OpenAI-compatible endpoints
    return [...this.customModels];
  }

  async resetModels() {
    this.customModels = [];
    logger.info('Qwen models reset to defaults');
    return { success: true };
  }

  async addCustomModel(model) {
    const existingModelIndex = this.customModels.findIndex(m => m.id === model.id);
    if (existingModelIndex >= 0) {
      this.customModels[existingModelIndex] = { ...model, provider: 'qwen' };
      logger.info(`Updated custom Qwen model: ${model.id}`);
    } else {
      this.customModels.push({ ...model, provider: 'qwen' });
      logger.info(`Added custom Qwen model: ${model.id}`);
    }
    return { ...model, provider: 'qwen' };
  }

  async deleteModel(modelId) {
    const initialLength = this.customModels.length;
    this.customModels = this.customModels.filter(model => model.id !== modelId);
    const wasDeleted = this.customModels.length < initialLength;
    if (wasDeleted) {
      logger.info(`Deleted custom Qwen model: ${modelId}`);
    } else {
      logger.warn(`Qwen model not found: ${modelId}`);
    }
    return wasDeleted;
  }

  async getStatus() {
    return {
      initialized: this.isInitialized,
      defaultModel: this.defaultModel || 'qwen3-coder-plus',
      authMethod: this.useOAuth ? 'OAuth' : 'API Key',
      baseUrl: this.resolveApiUrlBase(),
      contextWindow: 200000,
      provider: 'qwen',
      features: {
        textGeneration: true,
        codeGeneration: true,
        reasoning: true,
        fileSystem: true,
        webSearch: true,
        multimodal: false,
        streaming: true,
        tools: true,
        toolCalling: 'advanced',
        mcp: true
      }
    };
  }

  async createCompletion(messages, options = {}) {
    if (!this.isInitialized) {
      throw new Error('Qwen Provider not initialized');
    }

    try {
      const sessionId = options.session_id || 'default';
      const modelId = options.model || this.defaultModel;

      // Process vision messages if ever supported by chosen model
      let processedMessages = messages;
      if (visionHandler.isVisionCapable && visionHandler.isVisionCapable(modelId)) {
        processedMessages = await visionHandler.processVisionMessages(messages);
      }

      // Tools via MCP
      const toolResults = await mcpHandler.processToolCalls(processedMessages, options.tools, sessionId);
      let enhancedMessages = await mcpHandler.enhanceMessages(processedMessages, sessionId);
      if (toolResults.length > 0) {
        for (const result of toolResults) {
          enhancedMessages.push(result);
        }
      }

      const prompt = this.formatMessagesAsPrompt(enhancedMessages);

      // Optional fake thinking injection
      const thinkingContent = thinkingHandler && thinkingHandler.isThinkingModel && thinkingHandler.isThinkingModel(modelId)
        ? thinkingHandler.generateThinkingContent(prompt, options)
        : null;

      logger.debug('Creating Qwen completion', {
        promptLength: prompt.length,
        toolResultsCount: toolResults.length,
        hasVision: processedMessages.some(m => m.images),
        hasThinking: !!thinkingContent,
        sessionId,
        model: modelId
      });

      const startTime = Date.now();
      const responseText = await this.executeQwenOpenAIChat(prompt, options, modelId);
      const duration = Date.now() - startTime;
      logger.info(`Qwen response received in ${duration}ms`);

      let completionResponse = {
        id: `qwen-${Date.now()}`,
        object: 'chat.completion',
        created: Math.floor(Date.now() / 1000),
        model: modelId,
        choices: [{
          index: 0,
          message: { role: 'assistant', content: responseText },
          finish_reason: 'stop'
        }],
        usage: {
          prompt_tokens: this.estimateTokens(prompt),
          completion_tokens: this.estimateTokens(responseText),
          total_tokens: this.estimateTokens(prompt + responseText)
        },
        tool_calls: toolResults.length > 0 ? toolResults : undefined,
        session_id: sessionId,
        provider: 'qwen'
      };

      if (thinkingContent) {
        completionResponse = thinkingHandler.addThinkingToResponse(completionResponse, thinkingContent);
      }

      return completionResponse;
    } catch (error) {
      logger.error('Failed to create Qwen completion:', error);
      throw new Error(`Failed to create Qwen completion: ${error.message}`);
    }
  }

  async streamCompletion(messages, options = {}, onChunk) {
    if (!this.isInitialized) {
      throw new Error('Qwen Provider not initialized');
    }

    try {
      const sessionId = options.session_id || 'default';
      const modelId = options.model || this.defaultModel;

      let processedMessages = messages;
      if (visionHandler.isVisionCapable && visionHandler.isVisionCapable(modelId)) {
        processedMessages = await visionHandler.processVisionMessages(messages);
      }

      const toolResults = await mcpHandler.processToolCalls(processedMessages, options.tools, sessionId);
      let enhancedMessages = await mcpHandler.enhanceMessages(processedMessages, sessionId);
      if (toolResults.length > 0) {
        for (const result of toolResults) {
          enhancedMessages.push(result);
        }
      }

      const prompt = this.formatMessagesAsPrompt(enhancedMessages);

      const thinkingContent = thinkingHandler && thinkingHandler.isThinkingModel && thinkingHandler.isThinkingModel(modelId)
        ? thinkingHandler.generateThinkingContent(prompt, options)
        : null;

      logger.debug('Starting Qwen streaming completion', {
        promptLength: prompt.length,
        toolResultsCount: toolResults.length,
        hasVision: processedMessages.some(m => m.images),
        hasThinking: !!thinkingContent,
        sessionId,
        model: modelId
      });

      // Send tool calls first if any
      if (toolResults.length > 0) {
        for (const result of toolResults) {
          onChunk({
            id: `qwen-${Date.now()}`,
            object: 'chat.completion.chunk',
            created: Math.floor(Date.now() / 1000),
            model: modelId,
            choices: [{
              index: 0,
              delta: { tool_calls: [result] },
              finish_reason: null
            }]
          });
        }
      }

      if (thinkingContent) {
        await thinkingHandler.streamThinking(thinkingContent, onChunk, { model: modelId });
      }

      await this.executeQwenOpenAIChatStream(prompt, options, modelId, (textChunk) => {
        const formattedChunk = {
          id: `qwen-${Date.now()}`,
          object: 'chat.completion.chunk',
          created: Math.floor(Date.now() / 1000),
          model: modelId,
          choices: [{
            index: 0,
            delta: { content: textChunk },
            finish_reason: null
          }]
        };
        onChunk(formattedChunk);
      });

      onChunk({
        id: `qwen-${Date.now()}`,
        object: 'chat.completion.chunk',
        created: Math.floor(Date.now() / 1000),
        model: modelId,
        choices: [{ index: 0, delta: {}, finish_reason: 'stop' }],
        session_id: sessionId,
        provider: 'qwen'
      });
    } catch (error) {
      logger.error('Failed to stream Qwen completion:', error);
      throw new Error(`Failed to stream Qwen completion: ${error.message}`);
    }
  }

  formatMessagesAsPrompt(messages) {
    return messages.map(msg => {
      if (msg.role === 'system') return `System: ${msg.content}`;
      if (msg.role === 'user') return `User: ${msg.content}`;
      if (msg.role === 'assistant') return `Assistant: ${msg.content}`;
      return msg.content;
    }).join('\n\n');
  }

  resolveApiUrlBase() {
    // Prefer explicit QWEN_API_BASE_URL; default to OpenRouter base
    return (process.env.QWEN_API_BASE_URL || 'https://openrouter.ai/api/v1').replace(/\/$/, '');
  }

  resolveChatCompletionsUrl() {
    const base = this.resolveApiUrlBase();
    // If user already provided full /chat/completions endpoint, use as-is
    if (base.match(/\/chat\/completions$/)) return base;
    return `${base}/chat/completions`;
  }

  buildAuthHeaders() {
    const headers = { 'Content-Type': 'application/json' };
    const oauthToken = process.env.QWEN_OAUTH_TOKEN;
    const apiKey = process.env.QWEN_API_KEY;
    if (this.useOAuth && oauthToken) {
      headers['Authorization'] = `Bearer ${oauthToken}`;
    } else if (apiKey) {
      headers['Authorization'] = `Bearer ${apiKey}`;
    }
    // Optional provider-specific headers
    if (process.env.QWEN_HTTP_REFERER) headers['HTTP-Referer'] = process.env.QWEN_HTTP_REFERER;
    if (process.env.QWEN_HTTP_TITLE) headers['X-Title'] = process.env.QWEN_HTTP_TITLE;
    return headers;
  }

  async executeQwenOpenAIChat(prompt, options = {}, modelId) {
    const hasAuth = (this.useOAuth && !!process.env.QWEN_OAUTH_TOKEN) || !!process.env.QWEN_API_KEY;
    if (!hasAuth) {
      logger.warn('No Qwen auth configured (OAuth token or API key). Returning mock response');
      return this.generateMockResponse(prompt, options);
    }

    const url = this.resolveChatCompletionsUrl();
    const messages = [{ role: 'user', content: prompt }];

    const requestBody = {
      model: modelId,
      messages,
      max_tokens: options.max_tokens || 4000,
      temperature: options.temperature ?? 0.7
    };

    logger.debug('Making direct OpenAI-compatible API call to Qwen:', {
      url,
      model: modelId,
      promptLength: prompt.length
    });

    const headers = this.buildAuthHeaders();

    const response = await fetch(url, { method: 'POST', headers, body: JSON.stringify(requestBody) });
    if (!response.ok) {
      const errorText = await response.text();
      logger.error(`Qwen API error: ${response.status} ${response.statusText}`, errorText);
      // fallback to mock
      return this.generateMockResponse(prompt, options);
    }

    const data = await response.json();
    // Standard OpenAI response shape
    const content = data?.choices?.[0]?.message?.content || '';
    return content;
  }

  async executeQwenOpenAIChatStream(prompt, options = {}, modelId, onTextChunk) {
    const hasAuth = (this.useOAuth && !!process.env.QWEN_OAUTH_TOKEN) || !!process.env.QWEN_API_KEY;
    if (!hasAuth) {
      const mock = this.generateMockResponse(prompt, options);
      const sentences = mock.split(/(?<=[.!?])\s+/);
      for (let i = 0; i < sentences.length; i++) {
        onTextChunk(sentences[i] + (i < sentences.length - 1 ? ' ' : ''));
        await new Promise(r => setTimeout(r, 200));
      }
      return Promise.resolve();
    }

    const url = this.resolveChatCompletionsUrl();
    const messages = [{ role: 'user', content: prompt }];
    const requestBody = {
      model: modelId,
      messages,
      max_tokens: options.max_tokens || 4000,
      temperature: options.temperature ?? 0.7,
      stream: true
    };

    const headers = this.buildAuthHeaders();

    const response = await fetch(url, { method: 'POST', headers, body: JSON.stringify(requestBody) });
    if (!response.ok) {
      const errorText = await response.text();
      logger.error(`Qwen streaming API error: ${response.status} ${response.statusText}`, errorText);
      const mock = this.generateMockResponse(prompt, options);
      const sentences = mock.split(/(?<=[.!?])\s+/);
      for (let i = 0; i < sentences.length; i++) {
        onTextChunk(sentences[i] + (i < sentences.length - 1 ? ' ' : ''));
        await new Promise(r => setTimeout(r, 200));
      }
      return Promise.resolve();
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split('\n');
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') return Promise.resolve();
            try {
              const parsed = JSON.parse(data);
              const delta = parsed?.choices?.[0]?.delta?.content;
              if (delta) onTextChunk(delta);
            } catch {
              // ignore parse errors for SSE framing noise
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }
    return Promise.resolve();
  }

  generateMockResponse(prompt, _options) {
    return `This is a mock Qwen response generated because the API call could not be completed.\n\n` +
      `Summary of your prompt (first 200 chars):\n` + prompt.slice(0, 200);
  }

  estimateTokens(text) {
    return Math.ceil((text || '').length / 4);
  }

  async getApiKeys() {
    return this.apiKeys.map(key => ({
      id: key.id,
      name: key.name,
      createdAt: key.createdAt,
      key: key.key.substring(0, 4) + '...' + key.key.substring(key.key.length - 4)
    }));
  }

  async addApiKey(key, name = 'Qwen API Key') {
    if (!key || key.length < 10) {
      throw new Error('Invalid API key format.');
    }
    const keyId = `qwen_key_${Date.now()}`;
    const newKey = { id: keyId, key, name, createdAt: new Date() };
    this.apiKeys.push(newKey);
    logger.info(`Added new Qwen API key: ${name}`);
    process.env.QWEN_API_KEY = key;
    return { id: keyId, name, createdAt: newKey.createdAt };
  }

  async deleteApiKey(keyId) {
    const initialLength = this.apiKeys.length;
    this.apiKeys = this.apiKeys.filter(k => k.id !== keyId);
    const wasDeleted = this.apiKeys.length < initialLength;
    if (wasDeleted) {
      logger.info(`Deleted Qwen API key: ${keyId}`);
      if (this.apiKeys.length === 0) {
        delete process.env.QWEN_API_KEY;
      } else {
        process.env.QWEN_API_KEY = this.apiKeys[0].key;
      }
    }
    return wasDeleted;
  }

  async updateSettings(settings) {
    const { defaultModel, baseUrl, authToken, authMethod } = settings;
    if (defaultModel) {
      const models = await this.getAvailableModels();
      const exists = models.some(m => m.id === defaultModel);
      if (!exists) throw new Error(`Qwen model ${defaultModel} not found`);
      this.defaultModel = defaultModel;
      logger.info(`Default Qwen model set to ${defaultModel}`);
    }
    if (baseUrl) {
      process.env.QWEN_API_BASE_URL = baseUrl;
      logger.info(`Qwen base URL set to ${baseUrl}`);
    }
    if (authToken) {
      // Decide where to store the token based on current/desired method
      if (authMethod && authMethod.toLowerCase() === 'oauth') {
        process.env.QWEN_OAUTH_TOKEN = authToken;
        this.useOAuth = true;
        logger.info('Qwen OAuth token updated');
      } else {
        process.env.QWEN_API_KEY = authToken;
        this.useOAuth = false;
        logger.info('Qwen API key updated');
      }
    }
    if (authMethod) {
      this.useOAuth = authMethod.toLowerCase() === 'oauth';
      logger.info(`Qwen auth method set to ${this.useOAuth ? 'OAuth' : 'API Key'}`);
    }
    return {
      defaultModel: this.defaultModel || 'qwen3-coder-plus',
      baseUrl: this.resolveApiUrlBase(),
      hasAuthToken: this.useOAuth ? !!process.env.QWEN_OAUTH_TOKEN : !!process.env.QWEN_API_KEY,
      authMethod: this.useOAuth ? 'OAuth' : 'API Key'
    };
  }

  async destroy() {
    this.isInitialized = false;
    logger.info('Qwen Provider destroyed');
  }
}

