import { spawn } from 'child_process';
import { EventEmitter } from 'events';
import path from 'path';
import fs from 'fs/promises';
import fetch from 'node-fetch';
import { logger } from '../utils/logger.js';
import { mcpHandler } from '../mcp/mcpHandler.js';
import { visionHandler } from './visionHandler.js';
import { thinkingHandler } from './thinkingHandler.js';

export class ClaudeCLIProvider extends EventEmitter {
  constructor() {
    super();
    this.isInitialized = false;
    this.sessionId = null;
    this.cliProcess = null;
    this.customModels = [];
    this.apiKeys = [];
    this.initialize();
  }

  async initialize() {
    try {
      // Set default model
      this.defaultModel = 'claude-4-sonnet';
      
      this.isInitialized = true;
      logger.info('✅ Claude CLI Provider initialized successfully');
    } catch (error) {
      logger.error('❌ Failed to initialize Claude CLI Provider:', error);
      throw error;
    }
  }

  async checkClaudeCLI() {
    return new Promise((resolve, reject) => {
      logger.debug('Checking Claude CLI installation...');
      
      const checkProcess = spawn('claude', ['--version'], {
        stdio: 'pipe'
      });

      checkProcess.on('close', (code) => {
        if (code === 0) {
          logger.debug('Claude CLI found and working');
          resolve();
        } else {
          reject(new Error('Claude CLI not found. Please install it first.'));
        }
      });

      checkProcess.on('error', (error) => {
        reject(new Error(`Failed to check Claude CLI: ${error.message}`));
      });
    });
  }

  async setupAuthentication() {
    // Use API key authentication with custom base URL
    logger.info('🔐 Using API key authentication for Claude');
    
    if (!process.env.ANTHROPIC_AUTH_TOKEN) {
      logger.warn('⚠️ ANTHROPIC_AUTH_TOKEN not set, Claude CLI may not work properly');
    }
    
    if (!process.env.ANTHROPIC_BASE_URL) {
      logger.warn('⚠️ ANTHROPIC_BASE_URL not set, using default Claude API');
    }
  }

  async getAvailableModels() {
    // Combine default models with any custom models
    const defaultModels = [
      {
        id: 'claude-4-sonnet',
        name: 'Claude 4 Sonnet',
        description: 'Most capable Claude model with enhanced reasoning and analysis',
        maxTokens: 200000,
        vision: true,
        thinking: false,
        provider: 'claude'
      },
      {
        id: 'claude-4-opus',
        name: 'Claude 4 Opus',
        description: 'Premium Claude model with superior performance for complex tasks',
        maxTokens: 200000,
        vision: true,
        thinking: false,
        provider: 'claude'
      }
    ];
    
    return [...defaultModels, ...this.customModels];
  }
  
  async resetModels() {
    // Reset custom models
    this.customModels = [];
    logger.info('Claude models reset to defaults');
    return { success: true };
  }
  
  async addCustomModel(model) {
    // Check if model with same ID already exists
    const existingModelIndex = this.customModels.findIndex(m => m.id === model.id);
    
    if (existingModelIndex >= 0) {
      // Update existing model
      this.customModels[existingModelIndex] = { ...model, provider: 'claude' };
      logger.info(`Updated custom Claude model: ${model.id}`);
    } else {
      // Add new model
      this.customModels.push({ ...model, provider: 'claude' });
      logger.info(`Added custom Claude model: ${model.id}`);
    }
    
    return { ...model, provider: 'claude' };
  }
  
  async deleteModel(modelId) {
    // Check if it's a default model
    const defaultModels = ['claude-4-sonnet', 'claude-4-opus'];
    if (defaultModels.includes(modelId)) {
      logger.warn(`Cannot delete default Claude model: ${modelId}`);
      throw new Error(`Cannot delete default Claude model: ${modelId}`);
    }
    
    // Find and remove custom model
    const initialLength = this.customModels.length;
    this.customModels = this.customModels.filter(model => model.id !== modelId);
    
    const wasDeleted = this.customModels.length < initialLength;
    if (wasDeleted) {
      logger.info(`Deleted custom Claude model: ${modelId}`);
    } else {
      logger.warn(`Claude model not found: ${modelId}`);
    }
    
    return wasDeleted;
  }

  async getStatus() {
    return {
      initialized: this.isInitialized,
      defaultModel: this.defaultModel || 'claude-4-sonnet',
      authMethod: 'API Key',
      baseUrl: process.env.ANTHROPIC_BASE_URL || 'https://api.anthropic.com',
      contextWindow: 200000,
      provider: 'claude',
      features: {
        textGeneration: true,
        codeGeneration: true,
        reasoning: true,
        fileSystem: true,
        webSearch: true,
        multimodal: true,
        streaming: true,
        tools: true,
        toolCalling: 'advanced',
        mcp: true
      },
      capabilities: [
        'Text generation',
        'Code understanding and generation',
        'Complex reasoning',
        'File system operations',
        'Web search integration',
        'Multimodal input (text, images)',
        'Streaming responses'
      ],
      limitations: [
        'File access limited to project directory for security',
        'Command execution restricted to safe commands only',
        'Session cleanup after 1 hour of inactivity',
        'Requires ANTHROPIC_AUTH_TOKEN for authentication'
      ]
    };
  }

  async createCompletion(messages, options = {}) {
    if (!this.isInitialized) {
      throw new Error('Claude Provider not initialized');
    }

    try {
      const sessionId = options.session_id || 'default';
      const modelId = options.model || 'claude-4-sonnet';
      
      // Process vision messages if model supports it
      let processedMessages = messages;
      if (visionHandler.isVisionCapable(modelId)) {
        processedMessages = await visionHandler.processVisionMessages(messages);
      }
      
      // Process tools through MCP handler
      const toolResults = await mcpHandler.processToolCalls(processedMessages, options.tools, sessionId);
      
      // Enhance messages with tool context and results
      let enhancedMessages = await mcpHandler.enhanceMessages(processedMessages, sessionId);
      
      // Add tool results to messages if any
      if (toolResults.length > 0) {
        for (const result of toolResults) {
          enhancedMessages.push(result);
        }
      }
      
      // Generate thinking content if applicable
      const prompt = this.formatMessagesAsPrompt(enhancedMessages);
      const thinkingContent = thinkingHandler.isThinkingModel(modelId)
        ? thinkingHandler.generateThinkingContent(prompt, options)
        : null;
      
      logger.debug('Creating Claude completion for prompt', {
        promptLength: prompt.length,
        toolResultsCount: toolResults.length,
        hasVision: processedMessages.some(m => m.images),
        hasThinking: !!thinkingContent,
        sessionId,
        model: modelId
      });
      
      // Execute Claude CLI command
      const startTime = Date.now();
      const response = await this.executeClaudeCommand(prompt, options);
      const duration = Date.now() - startTime;
      
      logger.info(`Claude response received in ${duration}ms`);
      
      let completionResponse = {
        id: `claude-${Date.now()}`,
        object: 'chat.completion',
        created: Math.floor(Date.now() / 1000),
        model: modelId,
        choices: [{
          index: 0,
          message: {
            role: 'assistant',
            content: response
          },
          finish_reason: 'stop'
        }],
        usage: {
          prompt_tokens: this.estimateTokens(prompt),
          completion_tokens: this.estimateTokens(response),
          total_tokens: this.estimateTokens(prompt + response)
        },
        tool_calls: toolResults.length > 0 ? toolResults : undefined,
        session_id: sessionId,
        provider: 'claude'
      };

      // Add thinking content if applicable
      if (thinkingContent) {
        completionResponse = thinkingHandler.addThinkingToResponse(completionResponse, thinkingContent);
      }

      return completionResponse;
    } catch (error) {
      logger.error('Failed to create Claude completion:', error);
      throw new Error(`Failed to create Claude completion: ${error.message}`);
    }
  }

  async streamCompletion(messages, options = {}, onChunk) {
    if (!this.isInitialized) {
      throw new Error('Claude Provider not initialized');
    }

    try {
      const sessionId = options.session_id || 'default';
      const modelId = options.model || 'claude-4-sonnet';
      
      // Process vision messages if model supports it
      let processedMessages = messages;
      if (visionHandler.isVisionCapable(modelId)) {
        processedMessages = await visionHandler.processVisionMessages(messages);
      }
      
      // Process tools through MCP handler
      const toolResults = await mcpHandler.processToolCalls(processedMessages, options.tools, sessionId);
      
      // Enhance messages with tool context and results
      let enhancedMessages = await mcpHandler.enhanceMessages(processedMessages, sessionId);
      
      // Add tool results to messages if any
      if (toolResults.length > 0) {
        for (const result of toolResults) {
          enhancedMessages.push(result);
        }
      }
      
      const prompt = this.formatMessagesAsPrompt(enhancedMessages);
      
      // Generate thinking content if applicable
      const thinkingContent = thinkingHandler.isThinkingModel(modelId)
        ? thinkingHandler.generateThinkingContent(prompt, options)
        : null;
      
      logger.debug('Starting Claude streaming completion', {
        promptLength: prompt.length,
        toolResultsCount: toolResults.length,
        hasVision: processedMessages.some(m => m.images),
        hasThinking: !!thinkingContent,
        sessionId,
        model: modelId
      });
      
      // Send tool results first if any
      if (toolResults.length > 0) {
        for (const result of toolResults) {
          onChunk({
            id: `claude-${Date.now()}`,
            object: 'chat.completion.chunk',
            created: Math.floor(Date.now() / 1000),
            model: modelId,
            choices: [{
              index: 0,
              delta: {
                tool_calls: [result]
              },
              finish_reason: null
            }]
          });
        }
      }

      // Stream thinking content if applicable
      if (thinkingContent) {
        await thinkingHandler.streamThinking(thinkingContent, onChunk, { model: modelId });
      }
      
      await this.executeClaudeCommandStream(prompt, options, (chunk) => {
        const formattedChunk = {
          id: `claude-${Date.now()}`,
          object: 'chat.completion.chunk',
          created: Math.floor(Date.now() / 1000),
          model: modelId,
          choices: [{
            index: 0,
            delta: {
              content: chunk
            },
            finish_reason: null
          }]
        };
        onChunk(formattedChunk);
      });

      // Send final chunk
      onChunk({
        id: `claude-${Date.now()}`,
        object: 'chat.completion.chunk',
        created: Math.floor(Date.now() / 1000),
        model: modelId,
        choices: [{
          index: 0,
          delta: {},
          finish_reason: 'stop'
        }],
        session_id: sessionId,
        provider: 'claude'
      });
    } catch (error) {
      logger.error('Failed to stream Claude completion:', error);
      throw new Error(`Failed to stream Claude completion: ${error.message}`);
    }
  }

  formatMessagesAsPrompt(messages) {
    return messages.map(msg => {
      if (msg.role === 'system') {
        return `System: ${msg.content}`;
      } else if (msg.role === 'user') {
        return `User: ${msg.content}`;
      } else if (msg.role === 'assistant') {
        return `Assistant: ${msg.content}`;
      }
      return msg.content;
    }).join('\n\n');
  }

  async executeClaudeCommand(prompt, options = {}) {
    try {
      const currentModel = options.model || 'claude-4-sonnet';
      
      // Map our model names to OpenRouter Claude model names
      const modelMapping = {
        'claude-4-sonnet': 'anthropic/claude-3.5-sonnet',
        'claude-4-opus': 'anthropic/claude-3-opus'
      };
      
      const claudeModel = modelMapping[currentModel] || 'anthropic/claude-3.5-sonnet';
      
      // Get API configuration
      const apiUrl = process.env.CLAUDE_API_BASE_URL || 'https://kilocode.ai/api/openrouter/chat/completions';
      const apiKey = process.env.CLAUDE_API_KEY || process.env.ANTHROPIC_AUTH_TOKEN;
      
      if (!apiKey) {
        logger.error('No API key found for Claude');
        return this.generateMockResponse(prompt, options);
      }
      
      // Convert prompt to messages format
      const messages = [
        {
          role: 'user',
          content: prompt
        }
      ];
      
      const requestBody = {
        model: claudeModel,
        messages: messages,
        max_tokens: options.max_tokens || 4000,
        temperature: options.temperature || 0.7
      };
      
      logger.debug('Making direct API call to Claude:', {
        url: apiUrl,
        model: claudeModel,
        promptLength: prompt.length
      });
      
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`,
          'HTTP-Referer': 'https://kilocode.ai',
          'X-Title': 'Gemini CLI Wrapper'
        },
        body: JSON.stringify(requestBody)
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        logger.error(`Claude API error: ${response.status} ${response.statusText}`, errorText);
        
        // Handle specific error cases
        if (response.status === 401 || response.status === 403) {
          logger.warn('Authentication error, falling back to mock response');
          return this.generateMockResponse(prompt, options);
        } else if (response.status === 429) {
          logger.warn('Rate limit exceeded, falling back to mock response');
          return this.generateMockResponse(prompt, options);
        } else if (errorText.includes('Credit balance is too low')) {
          logger.warn('Credit balance too low, falling back to mock response');
          return this.generateMockResponse(prompt, options);
        }
        
        throw new Error(`Claude API error: ${response.status} ${errorText}`);
      }
      
      const data = await response.json();
      
      if (data.choices && data.choices.length > 0 && data.choices[0].message) {
        const content = data.choices[0].message.content;
        logger.info('Claude API response received successfully');
        return content;
      } else {
        logger.error('Invalid response format from Claude API:', data);
        return this.generateMockResponse(prompt, options);
      }
      
    } catch (error) {
      logger.error('Failed to execute Claude API call:', error);
      
      // Fallback to mock response for any errors
      logger.warn('Falling back to mock Claude response due to API error');
      return this.generateMockResponse(prompt, options);
    }
  }

  generateMockResponse(prompt, options) {
    const modelName = options.model || 'claude-4-sonnet';
    
    // Check if we have proper authentication configured
    const hasAuthToken = !!(process.env.CLAUDE_API_KEY || process.env.ANTHROPIC_AUTH_TOKEN);
    const hasBaseUrl = !!(process.env.CLAUDE_API_BASE_URL || process.env.ANTHROPIC_BASE_URL);
    
    if (!hasAuthToken || !hasBaseUrl) {
      return `Hello! I'm Claude (${modelName}), an AI assistant created by Anthropic.

⚠️ **Configuration Required**: This is a mock response because:
${!hasAuthToken ? '- CLAUDE_API_KEY or ANTHROPIC_AUTH_TOKEN is not configured' : ''}
${!hasBaseUrl ? '- CLAUDE_API_BASE_URL or ANTHROPIC_BASE_URL is not configured' : ''}

To enable real Claude API access:
1. Set CLAUDE_API_KEY in your environment
2. Set CLAUDE_API_BASE_URL (e.g., https://kilocode.ai/api/openrouter/chat/completions)
3. Restart the server

I can help you with programming, writing, analysis, and many other tasks once properly configured.`;
    }
    
    // If we have auth but still using mock, it means API call failed
    return `Hello! I'm Claude (${modelName}), an AI assistant created by Anthropic.

✅ **API Configuration**: Authentication and endpoint are configured correctly.

⚠️ **API Error**: This is a mock response because the direct API call failed. This could be due to:
- Network connectivity issues
- API quota/credit limits
- Invalid credentials
- Temporary service issues

Please check the server logs for more details. I can help you with programming, writing, analysis, and many other tasks once the API issue is resolved.`;
  }

  async executeClaudeCommandStream(prompt, options = {}, onChunk) {
    try {
      const currentModel = options.model || 'claude-4-sonnet';
      
      // Map our model names to OpenRouter Claude model names
      const modelMapping = {
        'claude-4-sonnet': 'anthropic/claude-3.5-sonnet',
        'claude-4-opus': 'anthropic/claude-3-opus'
      };
      
      const claudeModel = modelMapping[currentModel] || 'anthropic/claude-3.5-sonnet';
      
      // Get API configuration
      const apiUrl = process.env.CLAUDE_API_BASE_URL || 'https://kilocode.ai/api/openrouter/chat/completions';
      const apiKey = process.env.CLAUDE_API_KEY || process.env.ANTHROPIC_AUTH_TOKEN;
      
      if (!apiKey) {
        logger.error('No API key found for Claude streaming');
        const mockResponse = this.generateMockResponse(prompt, options);
        const sentences = mockResponse.split(/(?<=[.!?])\s+/);
        
        for (let i = 0; i < sentences.length; i++) {
          onChunk(sentences[i] + (i < sentences.length - 1 ? ' ' : ''));
          await new Promise(resolve => setTimeout(resolve, 200));
        }
        return Promise.resolve();
      }
      
      // Convert prompt to messages format
      const messages = [
        {
          role: 'user',
          content: prompt
        }
      ];
      
      const requestBody = {
        model: claudeModel,
        messages: messages,
        max_tokens: options.max_tokens || 4000,
        temperature: options.temperature || 0.7,
        stream: true
      };
      
      logger.debug('Making streaming API call to Claude:', {
        url: apiUrl,
        model: claudeModel,
        promptLength: prompt.length
      });
      
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`,
          'HTTP-Referer': 'https://kilocode.ai',
          'X-Title': 'Gemini CLI Wrapper'
        },
        body: JSON.stringify(requestBody)
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        logger.error(`Claude streaming API error: ${response.status} ${response.statusText}`, errorText);
        
        // Fallback to mock streaming response
        logger.warn('Falling back to mock Claude streaming response due to API error');
        const mockResponse = this.generateMockResponse(prompt, options);
        const sentences = mockResponse.split(/(?<=[.!?])\s+/);
        
        for (let i = 0; i < sentences.length; i++) {
          onChunk(sentences[i] + (i < sentences.length - 1 ? ' ' : ''));
          await new Promise(resolve => setTimeout(resolve, 200));
        }
        return Promise.resolve();
      }
      
      // Handle streaming response
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      
      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;
          
          const chunk = decoder.decode(value, { stream: true });
          const lines = chunk.split('\n');
          
          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6);
              if (data === '[DONE]') {
                return Promise.resolve();
              }
              
              try {
                const parsed = JSON.parse(data);
                if (parsed.choices && parsed.choices[0] && parsed.choices[0].delta && parsed.choices[0].delta.content) {
                  onChunk(parsed.choices[0].delta.content);
                }
              } catch (parseError) {
                // Ignore parsing errors for streaming data
                logger.debug('Failed to parse streaming chunk:', parseError);
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
      }
      
      return Promise.resolve();
      
    } catch (error) {
      logger.error('Failed to stream Claude completion:', error);
      
      // Fallback to simulated streaming using non-streaming API
      logger.warn('Falling back to simulated streaming using non-streaming API');
      try {
        const fullResponse = await this.executeClaudeCommand(prompt, options);
        
        // Simulate streaming by breaking response into chunks
        const words = fullResponse.split(' ');
        const chunkSize = 3; // Send 3 words at a time
        
        for (let i = 0; i < words.length; i += chunkSize) {
          const chunk = words.slice(i, i + chunkSize).join(' ');
          onChunk(chunk + (i + chunkSize < words.length ? ' ' : ''));
          
          // Add small delay to simulate streaming
          await new Promise(resolve => setTimeout(resolve, 50));
        }
        
        return Promise.resolve();
      } catch (fallbackError) {
        logger.error('Fallback streaming also failed:', fallbackError);
        
        // Final fallback to mock streaming response
        const mockResponse = this.generateMockResponse(prompt, options);
        const sentences = mockResponse.split(/(?<=[.!?])\s+/);
        
        for (let i = 0; i < sentences.length; i++) {
          onChunk(sentences[i] + (i < sentences.length - 1 ? ' ' : ''));
          await new Promise(resolve => setTimeout(resolve, 200));
        }
        
        return Promise.resolve();
      }
    }
  }

  estimateTokens(text) {
    // Simple token estimation (roughly 4 characters per token)
    return Math.ceil(text.length / 4);
  }

  async destroy() {
    if (this.cliProcess) {
      this.cliProcess.kill();
      this.cliProcess = null;
    }
    this.isInitialized = false;
    logger.info('Claude CLI Provider destroyed');
  }

  // API Keys management (similar to Gemini provider)
  async getApiKeys() {
    return this.apiKeys.map(key => ({
      id: key.id,
      name: key.name,
      createdAt: key.createdAt,
      // Don't return the actual key value for security
      key: key.key.substring(0, 4) + '...' + key.key.substring(key.key.length - 4)
    }));
  }
  
  async addApiKey(key, name = 'Claude API Key') {
    // Validate the API key (basic validation)
    if (!key || key.length < 10) {
      throw new Error('Invalid API key format. API key should be at least 10 characters long.');
    }
    
    const keyId = `claude_key_${Date.now()}`;
    const newKey = {
      id: keyId,
      key,
      name,
      createdAt: new Date()
    };
    
    this.apiKeys.push(newKey);
    logger.info(`Added new Claude API key: ${name}`);
    
    // Save to environment variable for immediate use
    process.env.ANTHROPIC_AUTH_TOKEN = key;
    
    return {
      id: keyId,
      name,
      createdAt: newKey.createdAt
    };
  }
  
  async deleteApiKey(keyId) {
    const initialLength = this.apiKeys.length;
    this.apiKeys = this.apiKeys.filter(key => key.id !== keyId);
    
    const wasDeleted = this.apiKeys.length < initialLength;
    
    if (wasDeleted) {
      logger.info(`Deleted Claude API key: ${keyId}`);
      
      // If we deleted the active key, clear the environment variable
      if (this.apiKeys.length === 0) {
        delete process.env.ANTHROPIC_AUTH_TOKEN;
      } else {
        // Set the first available key as active
        process.env.ANTHROPIC_AUTH_TOKEN = this.apiKeys[0].key;
      }
    }
    
    return wasDeleted;
  }

  async updateSettings(settings) {
    const { defaultModel, baseUrl, authToken } = settings;
    
    // Update default model if provided
    if (defaultModel) {
      // Check if model exists
      const models = await this.getAvailableModels();
      const modelExists = models.some(model => model.id === defaultModel);
      
      if (!modelExists) {
        throw new Error(`Claude model ${defaultModel} not found`);
      }
      
      this.defaultModel = defaultModel;
      logger.info(`Default Claude model set to ${defaultModel}`);
    }
    
    // Update base URL if provided
    if (baseUrl) {
      process.env.ANTHROPIC_BASE_URL = baseUrl;
      logger.info(`Claude base URL set to ${baseUrl}`);
    }
    
    // Update auth token if provided
    if (authToken) {
      process.env.ANTHROPIC_AUTH_TOKEN = authToken;
      logger.info('Claude auth token updated');
    }
    
    return {
      defaultModel: this.defaultModel || 'claude-4-sonnet',
      baseUrl: process.env.ANTHROPIC_BASE_URL || 'https://api.anthropic.com',
      hasAuthToken: !!process.env.ANTHROPIC_AUTH_TOKEN
    };
  }
}