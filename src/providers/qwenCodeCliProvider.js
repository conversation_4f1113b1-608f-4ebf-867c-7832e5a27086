import { spawn } from 'child_process';
import { EventEmitter } from 'events';
import { logger } from '../utils/logger.js';
import { mcpHandler } from '../mcp/mcpHandler.js';
import { visionHandler } from './visionHandler.js';
import { thinkingHandler } from './thinkingHandler.js';

/**
 * Qwen Code CLI Provider
 *
 * Uses the installed `qwen` CLI (from @qwen-code/qwen-code) and its OAuth flow.
 * Mirrors the Gemini CLI spawn pattern for compatibility.
 */
export class QwenCodeCLIProvider extends EventEmitter {
  constructor() {
    super();
    this.isInitialized = false;
    this.defaultModel = process.env.QWEN_DEFAULT_MODEL || 'qwen3-coder-plus';
    this.initialize();
  }

  async initialize() {
    try {
      this.isInitialized = true;
      logger.info('✅ Qwen Code CLI Provider initialized successfully');
    } catch (error) {
      logger.error('❌ Failed to initialize Qwen Code CLI Provider:', error);
      throw error;
    }
  }

  async getAvailableModels() {
    // Based on testing, Qwen Code CLI only supports qwen3-coder-plus model
    // Other models result in "400 model `model-name` is not supported" errors
    return [
      { 
        id: 'qwen3-coder-plus', 
        name: 'Qwen3 Coder Plus', 
        provider: 'qwen-cli', 
        vision: false, 
        thinking: false, 
        maxTokens: 200000,
        description: 'Qwen3 Coder (Plus) - The only supported model in Qwen Code CLI'
      }
    ];
  }

  async getStatus() {
    return {
      initialized: this.isInitialized,
      defaultModel: this.defaultModel,
      authMethod: 'OAuth (Qwen Code CLI)',
      provider: 'qwen-cli',
      features: {
        textGeneration: true,
        streaming: true,
        tools: true,
        mcp: true
      }
    };
  }

  formatMessagesAsPrompt(messages) {
    return messages.map(msg => {
      if (msg.role === 'system') return `System: ${msg.content}`;
      if (msg.role === 'user') return `User: ${msg.content}`;
      if (msg.role === 'assistant') return `Assistant: ${msg.content}`;
      return msg.content;
    }).join('\n\n');
  }

  async createCompletion(messages, options = {}) {
    if (!this.isInitialized) throw new Error('Qwen Code CLI Provider not initialized');

    const sessionId = options.session_id || 'default';
    const modelId = options.model || this.defaultModel;

    let processedMessages = messages;
    if (visionHandler.isVisionCapable && visionHandler.isVisionCapable(modelId)) {
      processedMessages = await visionHandler.processVisionMessages(messages);
    }

    const toolResults = await mcpHandler.processToolCalls(processedMessages, options.tools, sessionId);
    let enhancedMessages = await mcpHandler.enhanceMessages(processedMessages, sessionId);
    if (toolResults.length > 0) {
      for (const result of toolResults) enhancedMessages.push(result);
    }

    const prompt = this.formatMessagesAsPrompt(enhancedMessages);

    const response = await this.executeQwenCliCommand(prompt, options);
    return {
      id: `qwen-cli-${Date.now()}`,
      object: 'chat.completion',
      created: Math.floor(Date.now() / 1000),
      model: modelId,
      choices: [{ index: 0, message: { role: 'assistant', content: response }, finish_reason: 'stop' }]
    };
  }

  async streamCompletion(messages, options = {}, onChunk) {
    if (!this.isInitialized) throw new Error('Qwen Code CLI Provider not initialized');

    const sessionId = options.session_id || 'default';
    const modelId = options.model || this.defaultModel;

    let processedMessages = messages;
    if (visionHandler.isVisionCapable && visionHandler.isVisionCapable(modelId)) {
      processedMessages = await visionHandler.processVisionMessages(messages);
    }

    const toolResults = await mcpHandler.processToolCalls(processedMessages, options.tools, sessionId);
    let enhancedMessages = await mcpHandler.enhanceMessages(processedMessages, sessionId);
    if (toolResults.length > 0) {
      for (const result of toolResults) enhancedMessages.push(result);
    }
    const prompt = this.formatMessagesAsPrompt(enhancedMessages);

    // Emit tool calls first
    if (toolResults.length > 0) {
      for (const result of toolResults) {
        onChunk({
          id: `qwen-cli-${Date.now()}`,
          object: 'chat.completion.chunk',
          created: Math.floor(Date.now() / 1000),
          model: modelId,
          choices: [{ index: 0, delta: { tool_calls: [result] }, finish_reason: null }]
        });
      }
    }

    await this.executeQwenCliCommandStream(prompt, options, (text) => {
      onChunk({
        id: `qwen-cli-${Date.now()}`,
        object: 'chat.completion.chunk',
        created: Math.floor(Date.now() / 1000),
        model: modelId,
        choices: [{ index: 0, delta: { content: text }, finish_reason: null }]
      });
    });

    onChunk({
      id: `qwen-cli-${Date.now()}`,
      object: 'chat.completion.chunk',
      created: Math.floor(Date.now() / 1000),
      model: modelId,
      choices: [{ index: 0, delta: {}, finish_reason: 'stop' }]
    });
  }

  async executeQwenCliCommand(prompt, options = {}) {
    return new Promise((resolve, reject) => {
      const currentModel = options.model || this.defaultModel;
      // Assume `qwen` CLI supports -p for prompt and --model
      const args = ['-p'];
      if (currentModel) {
        args.push('--model', currentModel);
      }
      logger.geminiCommand('qwen', args); // reuse logging format

      const qwenProcess = spawn('qwen', args, { stdio: ['pipe', 'pipe', 'pipe'] });

      let output = '';
      let errorOutput = '';

      qwenProcess.stdout.on('data', (d) => { output += d.toString(); });
      qwenProcess.stderr.on('data', (d) => { errorOutput += d.toString(); });
      qwenProcess.on('close', (code) => {
        if (code === 0) {
          // Filter out "Loaded cached Qwen credentials" from output
          const cleanOutput = output.replace(/^Loaded cached Qwen credentials\.\n?/, '').trim();
          return resolve(cleanOutput);
        }
        logger.error('Qwen Code CLI exited with code:', code, errorOutput);
        reject(new Error(errorOutput || `Qwen Code CLI exited with code ${code}`));
      });
      qwenProcess.on('error', (err) => reject(err));

      qwenProcess.stdin.write(prompt);
      qwenProcess.stdin.end();
    });
  }

  async executeQwenCliCommandStream(prompt, options = {}, onText) {
    return new Promise((resolve, reject) => {
      const currentModel = options.model || this.defaultModel;
      const args = ['-p'];
      if (currentModel) args.push('--model', currentModel);

      const qwenProcess = spawn('qwen', args, { stdio: ['pipe', 'pipe', 'pipe'] });
      let errorOutput = '';

      qwenProcess.stdout.on('data', (d) => {
        const chunk = d.toString();
        // Filter out "Loaded cached Qwen credentials" from streaming output
        const cleanChunk = chunk.replace(/^Loaded cached Qwen credentials\.\n?/, '');
        if (cleanChunk) {
          onText(cleanChunk);
        }
      });
      qwenProcess.stderr.on('data', (d) => { errorOutput += d.toString(); });
      qwenProcess.on('close', (code) => {
        if (code === 0) return resolve();
        logger.error('Qwen Code CLI stream exited with code:', code, errorOutput);
        reject(new Error(errorOutput || `Qwen Code CLI exited with code ${code}`));
      });
      qwenProcess.on('error', (err) => reject(err));

      qwenProcess.stdin.write(prompt);
      qwenProcess.stdin.end();
    });
  }

  async resetModels() { return { success: true }; }
  async addCustomModel(model) { return model; }
  async deleteModel() { return false; }
  async updateSettings() { return this.getStatus(); }
  async getApiKeys() { return []; }
  async addApiKey() { throw new Error('Not supported for Qwen Code CLI'); }
  async deleteApiKey() { throw new Error('Not supported for Qwen Code CLI'); }
  async destroy() { this.isInitialized = false; }
}

