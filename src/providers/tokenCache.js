import fs from 'fs/promises';
import path from 'path';
import { logger } from '../utils/logger.js';

export class TokenCache {
  constructor() {
    this.cacheDir = path.join(process.cwd(), '.cache');
    this.tokenFile = path.join(this.cacheDir, 'oauth_tokens.json');
    this.cache = new Map();
    this.tokenRefreshPromises = new Map();
    
    // Initialize configuration
    this.config = {
      maxRetries: parseInt(process.env.TOKEN_CACHE_MAX_RETRIES) || 3,
      retryDelay: parseInt(process.env.TOKEN_CACHE_RETRY_DELAY) || 1000,
      expiryBuffer: parseInt(process.env.TOKEN_CACHE_EXPIRY_BUFFER) || 300000, // 5 minutes
      enableDistributedCache: process.env.ENABLE_DISTRIBUTED_TOKEN_CACHE === 'true',
      redisUrl: process.env.REDIS_URL,
      backupInterval: parseInt(process.env.TOKEN_CACHE_BACKUP_INTERVAL) || 3600000 // 1 hour
    };
    
    // Initialize statistics
    this.stats = {
      hits: 0,
      misses: 0,
      errors: 0,
      refreshes: 0,
      lastRefresh: null,
      lastError: null,
      startTime: new Date()
    };
    
    this.initialize();
  }

  async initialize() {
    try {
      await fs.mkdir(this.cacheDir, { recursive: true });
      await this.loadFromDisk();
      
      // Initialize distributed cache if enabled
      if (this.config.enableDistributedCache) {
        await this.initializeDistributedCache();
      }
      
      // Start periodic backup
      this.startPeriodicBackup();
      
      logger.info('Enhanced Token cache initialized', {
        distributedCache: this.config.enableDistributedCache,
        maxRetries: this.config.maxRetries,
        expiryBuffer: this.config.expiryBuffer
      });
    } catch (error) {
      logger.error('Failed to initialize token cache:', error);
      this.stats.errors++;
      this.stats.lastError = error.message;
    }
  }

  /**
   * Load tokens from disk
   */
  async loadFromDisk() {
    try {
      const data = await fs.readFile(this.tokenFile, 'utf8');
      const tokens = JSON.parse(data);
      
      for (const [key, value] of Object.entries(tokens)) {
        this.cache.set(key, {
          ...value,
          expiresAt: new Date(value.expiresAt)
        });
      }
      
      logger.debug(`Loaded ${this.cache.size} tokens from cache`);
    } catch (error) {
      if (error.code !== 'ENOENT') {
        logger.error('Failed to load token cache:', error);
      }
    }
  }

  /**
   * Save tokens to disk
   */
  async saveToDisk() {
    try {
      const tokens = {};
      for (const [key, value] of this.cache.entries()) {
        tokens[key] = {
          ...value,
          expiresAt: value.expiresAt.toISOString()
        };
      }
      
      await fs.writeFile(this.tokenFile, JSON.stringify(tokens, null, 2));
      logger.debug('Token cache saved to disk');
    } catch (error) {
      logger.error('Failed to save token cache:', error);
    }
  }

  /**
   * Get cached token with enhanced error handling
   */
  async getToken(key = 'default') {
    try {
      // Try distributed cache first if enabled
      if (this.config.enableDistributedCache) {
        const distributedToken = await this.getFromDistributedCache(key);
        if (distributedToken) {
          this.stats.hits++;
          return distributedToken;
        }
      }
      
      const cached = this.cache.get(key);
      
      if (!cached) {
        this.stats.misses++;
        return null;
      }

      // Check if token is expired (with configurable buffer)
      const now = new Date();
      
      if (cached.expiresAt && (cached.expiresAt.getTime() - now.getTime()) < this.config.expiryBuffer) {
        logger.debug('Token expired, attempting refresh');
        const refreshedToken = await this.refreshToken(key);
        if (refreshedToken) {
          this.stats.hits++;
          return refreshedToken;
        } else {
          this.stats.misses++;
          return null;
        }
      }

      this.stats.hits++;
      return cached.accessToken;
    } catch (error) {
      logger.error('Error getting token from cache:', error);
      this.stats.errors++;
      this.stats.lastError = error.message;
      return null;
    }
  }

  /**
   * Set token in cache
   */
  async setToken(accessToken, refreshToken, expiresIn, key = 'default') {
    const expiresAt = new Date(Date.now() + (expiresIn * 1000));
    
    this.cache.set(key, {
      accessToken,
      refreshToken,
      expiresAt,
      createdAt: new Date()
    });

    await this.saveToDisk();
    logger.debug(`Token cached for key: ${key}`);
  }

  /**
   * Refresh token with retry logic
   */
  async refreshToken(key = 'default') {
    // Prevent multiple simultaneous refresh attempts
    if (this.tokenRefreshPromises.has(key)) {
      return await this.tokenRefreshPromises.get(key);
    }

    const refreshPromise = this._performTokenRefreshWithRetry(key);
    this.tokenRefreshPromises.set(key, refreshPromise);

    try {
      const result = await refreshPromise;
      this.stats.refreshes++;
      this.stats.lastRefresh = new Date();
      return result;
    } catch (error) {
      this.stats.errors++;
      this.stats.lastError = error.message;
      throw error;
    } finally {
      this.tokenRefreshPromises.delete(key);
    }
  }

  /**
   * Perform token refresh with retry logic
   */
  async _performTokenRefreshWithRetry(key, attempt = 1) {
    const cached = this.cache.get(key);
    
    if (!cached || !cached.refreshToken) {
      logger.warn(`No refresh token available for key: ${key}`);
      return null;
    }

    try {
      const response = await this._makeTokenRefreshRequest(cached.refreshToken);
      const data = await response.json();
      
      await this.setToken(
        data.access_token,
        data.refresh_token || cached.refreshToken,
        data.expires_in || 3600,
        key
      );

      logger.info(`Token refreshed successfully for key: ${key} (attempt ${attempt})`);
      return data.access_token;

    } catch (error) {
      logger.error(`Failed to refresh token for key ${key} (attempt ${attempt}):`, error);
      
      // Retry logic
      if (attempt < this.config.maxRetries) {
        logger.info(`Retrying token refresh for key ${key} in ${this.config.retryDelay}ms`);
        await this.sleep(this.config.retryDelay * attempt); // Exponential backoff
        return await this._performTokenRefreshWithRetry(key, attempt + 1);
      }
      
      // Max retries exceeded, remove invalid token
      this.cache.delete(key);
      await this.saveToDisk();
      
      if (this.config.enableDistributedCache) {
        await this.removeFromDistributedCache(key);
      }
      
      throw new Error(`Token refresh failed after ${this.config.maxRetries} attempts: ${error.message}`);
    }
  }

  /**
   * Make token refresh request with timeout
   */
  async _makeTokenRefreshRequest(refreshToken) {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout
    
    try {
      const response = await fetch('https://oauth2.googleapis.com/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          client_id: process.env.GOOGLE_CLIENT_ID || '77185425430.apps.googleusercontent.com',
          client_secret: process.env.GOOGLE_CLIENT_SECRET || '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',
          refresh_token: refreshToken,
          grant_type: 'refresh_token'
        }),
        signal: controller.signal
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      return response;
    } finally {
      clearTimeout(timeoutId);
    }
  }

  /**
   * Clear token from cache
   */
  async clearToken(key = 'default') {
    this.cache.delete(key);
    await this.saveToDisk();
    logger.debug(`Token cleared for key: ${key}`);
  }

  /**
   * Clear all tokens
   */
  async clearAll() {
    this.cache.clear();
    await this.saveToDisk();
    logger.info('All tokens cleared from cache');
  }

  /**
   * Get cache status
   */
  getStatus() {
    const tokens = [];
    
    for (const [key, value] of this.cache.entries()) {
      tokens.push({
        key,
        hasAccessToken: !!value.accessToken,
        hasRefreshToken: !!value.refreshToken,
        expiresAt: value.expiresAt,
        isExpired: value.expiresAt < new Date(),
        createdAt: value.createdAt
      });
    }

    return {
      totalTokens: this.cache.size,
      tokens
    };
  }

  /**
   * Import OAuth credentials from Gemini CLI
   */
  async importFromGeminiCLI() {
    try {
      const homeDir = process.env.HOME || process.env.USERPROFILE;
      const geminiCredsPath = path.join(homeDir, '.gemini', 'oauth_creds.json');
      
      const data = await fs.readFile(geminiCredsPath, 'utf8');
      const creds = JSON.parse(data);
      
      if (creds.access_token && creds.refresh_token) {
        // Calculate expiry (default to 1 hour if not provided)
        const expiresIn = 3600; // 1 hour default
        
        await this.setToken(
          creds.access_token,
          creds.refresh_token,
          expiresIn,
          'gemini-cli'
        );
        
        logger.info('Successfully imported OAuth credentials from Gemini CLI');
        return true;
      } else {
        throw new Error('Invalid credentials format');
      }
    } catch (error) {
      logger.error('Failed to import OAuth credentials from Gemini CLI:', error);
      return false;
    }
  }

  /**
   * Initialize distributed cache (Redis)
   */
  async initializeDistributedCache() {
    if (!this.config.redisUrl) {
      logger.warn('Redis URL not provided, distributed cache disabled');
      this.config.enableDistributedCache = false;
      return;
    }
    
    try {
      // This would require redis client - placeholder for now
      logger.info('Distributed cache would be initialized here with Redis');
      // const redis = new Redis(this.config.redisUrl);
      // this.redisClient = redis;
    } catch (error) {
      logger.error('Failed to initialize distributed cache:', error);
      this.config.enableDistributedCache = false;
    }
  }

  /**
   * Get token from distributed cache
   */
  async getFromDistributedCache(key) {
    if (!this.config.enableDistributedCache) {
      return null;
    }
    
    try {
      // Placeholder for Redis implementation
      // const data = await this.redisClient.get(`token:${key}`);
      // return data ? JSON.parse(data) : null;
      return null;
    } catch (error) {
      logger.error('Error getting token from distributed cache:', error);
      return null;
    }
  }

  /**
   * Remove token from distributed cache
   */
  async removeFromDistributedCache(key) {
    if (!this.config.enableDistributedCache) {
      return;
    }
    
    try {
      // Placeholder for Redis implementation
      // await this.redisClient.del(`token:${key}`);
    } catch (error) {
      logger.error('Error removing token from distributed cache:', error);
    }
  }

  /**
   * Start periodic backup
   */
  startPeriodicBackup() {
    setInterval(async () => {
      try {
        await this.saveToDisk();
        logger.debug('Periodic token cache backup completed');
      } catch (error) {
        logger.error('Periodic backup failed:', error);
      }
    }, this.config.backupInterval);
  }

  /**
   * Sleep utility
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get enhanced cache statistics
   */
  getEnhancedStats() {
    const hitRate = this.stats.hits + this.stats.misses > 0
      ? (this.stats.hits / (this.stats.hits + this.stats.misses) * 100).toFixed(2)
      : 0;

    return {
      ...this.stats,
      hitRate: `${hitRate}%`,
      totalRequests: this.stats.hits + this.stats.misses,
      config: {
        maxRetries: this.config.maxRetries,
        retryDelay: this.config.retryDelay,
        expiryBuffer: this.config.expiryBuffer,
        distributedCache: this.config.enableDistributedCache
      },
      cacheSize: this.cache.size,
      uptime: process.uptime()
    };
  }
}

export const tokenCache = new TokenCache();