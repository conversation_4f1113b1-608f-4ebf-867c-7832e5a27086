import { spawn } from 'child_process';
import { EventEmitter } from 'events';
import path from 'path';
import fs from 'fs/promises';
import { logger } from '../utils/logger.js';
import { mcpHandler } from '../mcp/mcpHandler.js';
import { visionHandler } from './visionHandler.js';
import { thinkingHandler } from './thinkingHandler.js';
import { realThinkingHandler } from './realThinkingHandler.js';
import { contentSafetyHandler } from './contentSafetyHandler.js';
import { tokenCache } from './tokenCache.js';

export class GeminiCLIProvider extends EventEmitter {
  constructor() {
    super();
    this.isInitialized = false;
    this.sessionId = null;
    this.cliProcess = null;
    this.customModels = [];
    this.apiKeys = [];
    this.initialize();
  }

  async initialize() {
    try {
      // Always use OAuth by default
      process.env.GEMINI_USE_OAUTH = 'true';
      
      // Set default model
      this.defaultModel = 'gemini-2.5-flash';
      
      this.isInitialized = true;
      logger.info('✅ Gemini CLI Provider initialized successfully');
    } catch (error) {
      logger.error('❌ Failed to initialize Gemini CLI Provider:', error);
      throw error;
    }
  }

  async checkGeminiCLI() {
    return new Promise((resolve, reject) => {
      logger.debug('Checking Gemini CLI installation...');
      
      const checkProcess = spawn('npx', ['@google/gemini-cli', '--version'], {
        stdio: 'pipe'
      });

      checkProcess.on('close', (code) => {
        if (code === 0) {
          logger.debug('Gemini CLI found and working');
          resolve();
        } else {
          reject(new Error('Gemini CLI not found. Please install it first.'));
        }
      });

      checkProcess.on('error', (error) => {
        reject(new Error(`Failed to check Gemini CLI: ${error.message}`));
      });
    });
  }

  async setupAuthentication() {
    // Always use OAuth
    logger.info('🔐 Using OAuth authentication');
    // The CLI will handle OAuth flow automatically
    process.env.GEMINI_USE_OAUTH = 'true';
  }

  async getAvailableModels() {
    // Combine default models with any custom models
    const defaultModels = [
      {
        id: 'gemini-2.5-pro',
        name: 'Gemini 2.5 Pro',
        description: 'Latest and most capable model with enhanced reasoning and 1M token context',
        maxTokens: 1000000,
        vision: true,
        thinking: true, // Real thinking support enabled
        realThinking: true, // Native Gemini thinking
        fallback: 'gemini-2.5-flash' // Auto fallback to flash when quota exceeded
      },
      {
        id: 'gemini-2.5-flash',
        name: 'Gemini 2.5 Flash',
        description: 'Fast and efficient model with reasoning capabilities (unlimited)',
        maxTokens: 1000000,
        vision: true,
        thinking: true, // Real thinking support enabled
        realThinking: true // Native Gemini thinking
      },
      {
        id: 'gemini-1.5-pro',
        name: 'Gemini 1.5 Pro',
        description: 'Most capable model with 1M token context window',
        maxTokens: 1000000,
        vision: true,
        thinking: false
      },
      {
        id: 'gemini-1.5-flash',
        name: 'Gemini 1.5 Flash',
        description: 'Fast and efficient model for most tasks',
        maxTokens: 1000000,
        vision: true,
        thinking: false
      },
      {
        id: 'gemini-2.0-flash-exp',
        name: 'Gemini 2.0 Flash Experimental',
        description: 'Latest experimental model with enhanced capabilities',
        maxTokens: 1000000,
        vision: true,
        thinking: false
      }
    ];
    
    return [...defaultModels, ...this.customModels];
  }
  
  async resetModels() {
    // Reset custom models
    this.customModels = [];
    logger.info('Models reset to defaults');
    return { success: true };
  }
  
  async addCustomModel(model) {
    // Check if model with same ID already exists
    const existingModelIndex = this.customModels.findIndex(m => m.id === model.id);
    
    if (existingModelIndex >= 0) {
      // Update existing model
      this.customModels[existingModelIndex] = model;
      logger.info(`Updated custom model: ${model.id}`);
    } else {
      // Add new model
      this.customModels.push(model);
      logger.info(`Added custom model: ${model.id}`);
    }
    
    return model;
  }
  
  async deleteModel(modelId) {
    // Check if it's a default model
    const defaultModels = ['gemini-2.5-pro', 'gemini-2.5-flash', 'gemini-1.5-pro', 'gemini-1.5-flash', 'gemini-2.0-flash-exp'];
    if (defaultModels.includes(modelId)) {
      logger.warn(`Cannot delete default model: ${modelId}`);
      throw new Error(`Cannot delete default model: ${modelId}`);
    }
    
    // Find and remove custom model
    const initialLength = this.customModels.length;
    this.customModels = this.customModels.filter(model => model.id !== modelId);
    
    const wasDeleted = this.customModels.length < initialLength;
    if (wasDeleted) {
      logger.info(`Deleted custom model: ${modelId}`);
    } else {
      logger.warn(`Model not found: ${modelId}`);
    }
    
    return wasDeleted;
  }

  async getStatus() {
    return {
      initialized: this.isInitialized,
      defaultModel: this.defaultModel || 'gemini-2.5-flash',
      authMethod: process.env.GEMINI_USE_OAUTH === 'true' ? 'OAuth' : 'API Key',
      contextWindow: 1000000,
      features: {
        textGeneration: true,
        codeGeneration: true,
        reasoning: true,
        fileSystem: true,
        webSearch: true,
        multimodal: true,
        streaming: true,
        tools: true,     // Available through MCP tool registry
        toolCalling: 'advanced', // Full OpenAI-style function calling via MCP
        mcp: true       // MCP fully implemented
      },
      capabilities: [
        'Text generation',
        'Code understanding and generation',
        'Complex reasoning',
        'File system operations',
        'Web search integration',
        'Multimodal input (text, images)',
        'Streaming responses'
      ],
            limitations: [
        'File access limited to project directory for security',
        'Command execution restricted to safe commands only',
        'Session cleanup after 1 hour of inactivity'
      ]
    };
  }

  async createCompletion(messages, options = {}) {
    if (!this.isInitialized) {
      throw new Error('Provider not initialized');
    }

    try {
      const sessionId = options.session_id || 'default';
      const modelId = options.model || 'gemini-2.5-flash';
      
      // Process vision messages if model supports it
      let processedMessages = messages;
      if (visionHandler.isVisionCapable(modelId)) {
        processedMessages = await visionHandler.processVisionMessages(messages);
      }
      
      // Process tools through MCP handler
      const toolResults = await mcpHandler.processToolCalls(processedMessages, options.tools, sessionId);
      
      // Enhance messages with tool context and results
      let enhancedMessages = await mcpHandler.enhanceMessages(processedMessages, sessionId);
      
      // Add tool results to messages if any
      if (toolResults.length > 0) {
        for (const result of toolResults) {
          enhancedMessages.push(result);
        }
      }
      
      // Generate thinking content if applicable
      const prompt = this.formatMessagesAsPrompt(enhancedMessages);
      
      // Use real thinking if enabled, fallback to fake thinking
      let thinkingContent = null;
      if (realThinkingHandler.shouldUseRealThinking(modelId, options)) {
        // Real thinking will be handled in CLI command execution
        thinkingContent = { useRealThinking: true };
      } else if (realThinkingHandler.shouldUseFakeThinking(modelId, options)) {
        thinkingContent = thinkingHandler.generateThinkingContent(prompt, options);
      }
      
      logger.debug('Creating completion for prompt', {
        promptLength: prompt.length,
        toolResultsCount: toolResults.length,
        hasVision: processedMessages.some(m => m.images),
        hasThinking: !!thinkingContent,
        sessionId
      });
      
      // Execute Gemini CLI command with thinking support
      const startTime = Date.now();
      const response = await this.executeGeminiCommand(prompt, options);
      const duration = Date.now() - startTime;
      
      // Process thinking response if using real thinking
      let processedResponse = response;
      let realThinkingContent = null;
      
      if (thinkingContent && thinkingContent.useRealThinking) {
        const thinkingResult = realThinkingHandler.processThinkingResponse(response, options);
        processedResponse = thinkingResult.content;
        if (thinkingResult.reasoning) {
          realThinkingContent = {
            reasoning: thinkingResult.reasoning,
            hasRealThinking: thinkingResult.hasRealThinking
          };
        }
      }
      
      // Process content safety
      const safetyResult = contentSafetyHandler.processSafetyResponse(processedResponse, options);
      if (safetyResult.blocked) {
        throw new Error(contentSafetyHandler.generateSafetyErrorMessage(safetyResult.blockReason, safetyResult.safetyRatings));
      }
      processedResponse = safetyResult.content;
      
      logger.geminiResponse(processedResponse, duration);
      
      let completionResponse = {
        id: `gemini-${Date.now()}`,
        object: 'chat.completion',
        created: Math.floor(Date.now() / 1000),
        model: modelId,
        choices: [{
          index: 0,
          message: {
            role: 'assistant',
            content: processedResponse
          },
          finish_reason: 'stop'
        }],
        usage: {
          prompt_tokens: this.estimateTokens(prompt),
          completion_tokens: this.estimateTokens(processedResponse),
          total_tokens: this.estimateTokens(prompt + processedResponse)
        },
        tool_calls: toolResults.length > 0 ? toolResults : undefined,
        session_id: sessionId
      };

      // Add thinking content if applicable
      if (realThinkingContent) {
        completionResponse = realThinkingHandler.addThinkingToResponse(completionResponse, realThinkingContent);
      } else if (thinkingContent && !thinkingContent.useRealThinking) {
        completionResponse = thinkingHandler.addThinkingToResponse(completionResponse, thinkingContent);
      }

      return completionResponse;
    } catch (error) {
      logger.error('Failed to create completion:', error);
      throw new Error(`Failed to create completion: ${error.message}`);
    }
  }

  async streamCompletion(messages, options = {}, onChunk) {
    if (!this.isInitialized) {
      throw new Error('Provider not initialized');
    }

    try {
      const sessionId = options.session_id || 'default';
      const modelId = options.model || 'gemini-2.5-flash';
      
      // Process vision messages if model supports it
      let processedMessages = messages;
      if (visionHandler.isVisionCapable(modelId)) {
        processedMessages = await visionHandler.processVisionMessages(messages);
      }
      
      // Process tools through MCP handler
      const toolResults = await mcpHandler.processToolCalls(processedMessages, options.tools, sessionId);
      
      // Enhance messages with tool context and results
      let enhancedMessages = await mcpHandler.enhanceMessages(processedMessages, sessionId);
      
      // Add tool results to messages if any
      if (toolResults.length > 0) {
        for (const result of toolResults) {
          enhancedMessages.push(result);
        }
      }
      
      const prompt = this.formatMessagesAsPrompt(enhancedMessages);
      
      // Use real thinking if enabled, fallback to fake thinking
      let thinkingContent = null;
      if (realThinkingHandler.shouldUseRealThinking(modelId, options)) {
        // Real thinking will be handled in CLI command execution
        thinkingContent = { useRealThinking: true };
      } else if (realThinkingHandler.shouldUseFakeThinking(modelId, options)) {
        thinkingContent = thinkingHandler.generateThinkingContent(prompt, options);
      }
      
      logger.debug('Starting streaming completion', {
        promptLength: prompt.length,
        toolResultsCount: toolResults.length,
        hasVision: processedMessages.some(m => m.images),
        hasThinking: !!thinkingContent,
        sessionId
      });
      
      // Send tool results first if any
      if (toolResults.length > 0) {
        for (const result of toolResults) {
          onChunk({
            id: `gemini-${Date.now()}`,
            object: 'chat.completion.chunk',
            created: Math.floor(Date.now() / 1000),
            model: modelId,
            choices: [{
              index: 0,
              delta: {
                tool_calls: [result]
              },
              finish_reason: null
            }]
          });
        }
      }

      // Stream thinking content if applicable
      if (thinkingContent && !thinkingContent.useRealThinking) {
        await thinkingHandler.streamThinking(thinkingContent, onChunk, { model: modelId });
      }
      
      // Execute streaming with real thinking support
      let realThinkingContent = null;
      
      await this.executeGeminiCommandStream(prompt, options, (chunk) => {
        // Check if this chunk contains thinking content
        if (thinkingContent && thinkingContent.useRealThinking) {
          const thinkingResult = realThinkingHandler.processThinkingResponse(chunk, options);
          if (thinkingResult.reasoning && !realThinkingContent) {
            realThinkingContent = {
              reasoning: thinkingResult.reasoning,
              hasRealThinking: thinkingResult.hasRealThinking
            };
            // Stream the thinking content
            realThinkingHandler.streamThinking(realThinkingContent, onChunk, { model: modelId });
          }
          chunk = thinkingResult.content;
        }
        
        if (chunk) {
          const formattedChunk = {
            id: `gemini-${Date.now()}`,
            object: 'chat.completion.chunk',
            created: Math.floor(Date.now() / 1000),
            model: modelId,
            choices: [{
              index: 0,
              delta: {
                content: chunk
              },
              finish_reason: null
            }]
          };
          onChunk(formattedChunk);
        }
      });

      // Send final chunk
      onChunk({
        id: `gemini-${Date.now()}`,
        object: 'chat.completion.chunk',
        created: Math.floor(Date.now() / 1000),
        model: modelId,
        choices: [{
          index: 0,
          delta: {},
          finish_reason: 'stop'
        }],
        session_id: sessionId
      });
    } catch (error) {
      logger.error('Failed to stream completion:', error);
      throw new Error(`Failed to stream completion: ${error.message}`);
    }
  }

  formatMessagesAsPrompt(messages) {
    return messages.map(msg => {
      if (msg.role === 'system') {
        return `System: ${msg.content}`;
      } else if (msg.role === 'user') {
        return `User: ${msg.content}`;
      } else if (msg.role === 'assistant') {
        return `Assistant: ${msg.content}`;
      }
      return msg.content;
    }).join('\n\n');
  }

  async executeGeminiCommand(prompt, options = {}) {
    return this.executeGeminiCommandWithFallback(prompt, options, false);
  }

  async executeGeminiCommandWithFallback(prompt, options = {}, isRetry = false) {
    return new Promise(async (resolve, reject) => {
      const currentModel = options.model || 'gemini-2.5-flash';
      
      // Use -y (YOLO mode) to automatically accept all actions
      let args = ['@google/gemini-cli', '-p', '-y'];
      
      // Add model selection if specified
      if (currentModel) {
        args.push('--model', currentModel);
      }
      
      // Add thinking parameters if using real thinking
      args = realThinkingHandler.addThinkingToCommand(args, options);
      
      // Add content safety parameters
      args = contentSafetyHandler.addSafetyToCommand(args, options);
      
      // Add content safety parameters
      args = contentSafetyHandler.addSafetyToCommand(args, options);

      logger.geminiCommand('npx', args);

      const geminiProcess = spawn('npx', args, {
        stdio: ['pipe', 'pipe', 'pipe'],
        env: {
          ...process.env,
          GEMINI_USE_OAUTH: 'true',
          NODE_ENV: 'development'
        }
      });

      let output = '';
      let errorOutput = '';

      geminiProcess.stdout.on('data', (data) => {
        const chunk = data.toString();
        logger.debug('Gemini CLI output:', chunk);
        output += chunk;
      });

      geminiProcess.stderr.on('data', (data) => {
        const chunk = data.toString();
        logger.error('Gemini CLI error:', chunk);
        errorOutput += chunk;
      });

      geminiProcess.on('close', async (code) => {
        if (code === 0) {
          resolve(output.trim());
        } else {
          logger.error('Gemini CLI process exited with code:', code);
          logger.error('Error output:', errorOutput);
          
          // FOR TESTING FALLBACK: Simulate quota error for gemini-2.5-pro
          if (!isRetry && currentModel === 'gemini-2.5-pro') {
            logger.warn(`Simulating quota exceeded for ${currentModel}, forcing fallback to gemini-2.5-flash`);
            const isQuotaError = true; // Force quota error for testing
          }
          // END FOR TESTING FALLBACK

          // Check if it's a quota exceeded error and we can fallback
          const isQuotaError = errorOutput.includes('Quota exceeded') ||
                              errorOutput.includes('quota metric') ||
                              errorOutput.includes('rateLimitExceeded') ||
                              errorOutput.includes('RESOURCE_EXHAUSTED');
          
          if (isQuotaError && !isRetry && currentModel === 'gemini-2.5-pro') {
            logger.warn(`Quota exceeded for ${currentModel}, falling back to gemini-2.5-flash`);
            try {
              const fallbackOptions = { ...options, model: 'gemini-2.5-flash' };
              const fallbackResult = await this.executeGeminiCommandWithFallback(prompt, fallbackOptions, true);
              resolve(fallbackResult);
              return;
            } catch (fallbackError) {
              logger.error('Fallback model also failed:', fallbackError);
              // Continue to mock response fallback
            }
          }
          
          // Only fallback to mock response for specific CLI issues
          if (errorOutput.includes('Unknown argument') ||
              errorOutput.includes('command not found') ||
              errorOutput.includes('ENOENT')) {
            logger.warn('Falling back to mock response due to CLI installation issues:', errorOutput);
            const mockResponse = this.generateMockResponse(prompt, options);
            resolve(mockResponse);
          } else {
            // For other errors, log them and reject instead of using mock
            logger.error('Gemini CLI error (not using mock):', errorOutput);
            reject(new Error(`Gemini CLI error: ${errorOutput || 'Unknown error'}`));
          }
        }
      });

      geminiProcess.on('error', (error) => {
        logger.error('Failed to execute Gemini CLI:', error);
        reject(new Error(`Failed to execute Gemini CLI: ${error.message}`));
      });

      // Send the prompt to CLI
      geminiProcess.stdin.write(prompt);
      geminiProcess.stdin.end();
    });
  }

  generateMockResponse(prompt, options) {
    const responses = [
      "Hello! I'm Gemini, an AI assistant. I can help you with various tasks including coding, analysis, and answering questions.",
      "I understand you're testing the Gemini CLI Wrapper. The system is working correctly and I'm ready to assist you.",
      "This is a mock response from the Gemini CLI Wrapper. The real CLI integration would provide more sophisticated responses.",
      "I can help you with programming, writing, analysis, and many other tasks. What would you like to know?",
      "The Gemini CLI Wrapper is functioning properly. This response demonstrates the API integration is working."
    ];
    
    // Simple hash-based selection for consistent responses
    const hash = prompt.split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0);
      return a & a;
    }, 0);
    
    const selectedResponse = responses[Math.abs(hash) % responses.length];
    
    // Log why we're using mock response for debugging
    logger.warn('Using mock response - this should only happen when Gemini CLI fails');
    
    return `${selectedResponse}\n\n[Note: This is a mock response because the Gemini CLI encountered an error. Node.js version is ${process.version} which is compatible.]`;
  }

  async executeGeminiCommandStream(prompt, options = {}, onChunk) {
    return this.executeGeminiCommandStreamWithFallback(prompt, options, onChunk, false);
  }

  async executeGeminiCommandStreamWithFallback(prompt, options = {}, onChunk, isRetry = false) {
    return new Promise(async (resolve, reject) => {
      const currentModel = options.model || 'gemini-2.5-flash';
      
      let args = ['@google/gemini-cli', '-p', '-y'];
      
      if (currentModel) {
        args.push('--model', currentModel);
      }
      
      // Add thinking parameters if using real thinking
      args = realThinkingHandler.addThinkingToCommand(args, options);

      logger.geminiCommand('npx (streaming)', args);

      const geminiProcess = spawn('npx', args, {
        stdio: ['pipe', 'pipe', 'pipe'],
        env: {
          ...process.env,
          GEMINI_USE_OAUTH: 'true',
          NODE_ENV: 'development'
        }
      });

      let errorOutput = '';
      let receivedData = false;

      geminiProcess.stdout.on('data', (data) => {
        receivedData = true;
        const chunk = data.toString();
        logger.debug('Gemini CLI output (streaming):', chunk);
        onChunk(chunk);
      });

      geminiProcess.stderr.on('data', (data) => {
        const chunk = data.toString();
        logger.error('Gemini CLI error (streaming):', chunk);
        errorOutput += chunk;
      });

      geminiProcess.on('close', async (code) => {
        if (code === 0) {
          resolve();
        } else {
          logger.error('Gemini CLI process exited with code (streaming):', code);
          logger.error('Error output (streaming):', errorOutput);
          
          // FOR TESTING FALLBACK: Simulate quota error for gemini-2.5-pro
          if (!isRetry && currentModel === 'gemini-2.5-pro') {
            logger.warn(`Simulating quota exceeded for ${currentModel}, forcing fallback to gemini-2.5-flash for streaming`);
            const isQuotaError = true; // Force quota error for testing
          }
          // END FOR TESTING FALLBACK

          // Check if it's a quota exceeded error and we can fallback
          const isQuotaError = errorOutput.includes('Quota exceeded') ||
                              errorOutput.includes('quota metric') ||
                              errorOutput.includes('rateLimitExceeded') ||
                              errorOutput.includes('RESOURCE_EXHAUSTED');
          
          if (isQuotaError && !isRetry && currentModel === 'gemini-2.5-pro') {
            logger.warn(`Quota exceeded for ${currentModel}, falling back to gemini-2.5-flash for streaming`);
            try {
              const fallbackOptions = { ...options, model: 'gemini-2.5-flash' };
              await this.executeGeminiCommandStreamWithFallback(prompt, fallbackOptions, onChunk, true);
              resolve();
              return;
            } catch (fallbackError) {
              logger.error('Fallback model also failed for streaming:', fallbackError);
              // Continue to mock response fallback
            }
          }
          
          // Only fallback to mock for specific CLI installation issues
          if (!receivedData && (errorOutput.includes('Unknown argument') ||
                               errorOutput.includes('command not found') ||
                               errorOutput.includes('ENOENT'))) {
            logger.warn('Falling back to mock streaming response due to CLI installation issues:', errorOutput);
            const mockResponse = this.generateMockResponse(prompt, options);
            
            // Improved streaming simulation
            const sentences = mockResponse.split(/(?<=[.!?])\s+/);
            
            sentences.forEach((sentence, index) => {
              setTimeout(() => {
                onChunk(sentence + ' ');
                if (index === sentences.length - 1) {
                  resolve();
                }
              }, index * 300); // 300ms delay between sentences for more natural streaming
            });
          } else {
            // For other errors, log them and reject instead of using mock
            logger.error('Gemini CLI streaming error (not using mock):', errorOutput);
            reject(new Error(`Gemini CLI streaming error: ${errorOutput || 'Unknown error'}`));
          }
        }
      });

      geminiProcess.on('error', (error) => {
        logger.error('Failed to execute Gemini CLI (streaming):', error);
        reject(new Error(`Failed to execute Gemini CLI: ${error.message}`));
      });

      // Send the prompt
      geminiProcess.stdin.write(prompt);
      geminiProcess.stdin.end();
    });
  }

  estimateTokens(text) {
    // Simple token estimation (roughly 4 characters per token)
    return Math.ceil(text.length / 4);
  }


  async destroy() {
    if (this.cliProcess) {
      this.cliProcess.kill();
      this.cliProcess = null;
    }
    this.isInitialized = false;
    logger.info('Gemini CLI Provider destroyed');
  }

  async getApiKeys() {
    return this.apiKeys.map(key => ({
      id: key.id,
      name: key.name,
      createdAt: key.createdAt,
      // Don't return the actual key value for security
      key: key.key.substring(0, 4) + '...' + key.key.substring(key.key.length - 4)
    }));
  }
  
  async addApiKey(key, name = 'API Key') {
    // Validate the API key (basic validation)
    if (!key || key.length < 10) {
      throw new Error('Invalid API key format. API key should be at least 10 characters long.');
    }
    
    const keyId = `key_${Date.now()}`;
    const newKey = {
      id: keyId,
      key,
      name,
      createdAt: new Date()
    };
    
    this.apiKeys.push(newKey);
    logger.info(`Added new API key: ${name}`);
    
    // Save to environment variable for immediate use if we're using API key auth
    if (process.env.GEMINI_USE_OAUTH !== 'true') {
      process.env.API_KEY = key;
    }
    
    return {
      id: keyId,
      name,
      createdAt: newKey.createdAt
    };
  }
  
  async deleteApiKey(keyId) {
    const initialLength = this.apiKeys.length;
    this.apiKeys = this.apiKeys.filter(key => key.id !== keyId);
    
    const wasDeleted = this.apiKeys.length < initialLength;
    
    if (wasDeleted) {
      logger.info(`Deleted API key: ${keyId}`);
      
      // If we deleted the active key, switch back to OAuth
      if (this.apiKeys.length === 0) {
        process.env.GEMINI_USE_OAUTH = 'true';
        delete process.env.API_KEY;
      } else {
        // Set the first available key as active
        process.env.API_KEY = this.apiKeys[0].key;
      }
    }
    
    return wasDeleted;
  }

  async updateSettings(settings) {
    const { defaultModel, authMethod } = settings;
    
    // Update default model if provided
    if (defaultModel) {
      // Check if model exists
      const models = await this.getAvailableModels();
      const modelExists = models.some(model => model.id === defaultModel);
      
      if (!modelExists) {
        throw new Error(`Model ${defaultModel} not found`);
      }
      
      this.defaultModel = defaultModel;
      logger.info(`Default model set to ${defaultModel}`);
    }
    
    // Update auth method if provided
    if (authMethod) {
      if (authMethod === 'oauth') {
        process.env.GEMINI_USE_OAUTH = 'true';
        delete process.env.API_KEY;
        logger.info('Authentication method set to OAuth');
      } else if (authMethod === 'api_key') {
        if (this.apiKeys.length === 0) {
          throw new Error('No API keys available. Please add an API key first.');
        }
        
        process.env.GEMINI_USE_OAUTH = 'false';
        process.env.API_KEY = this.apiKeys[0].key;
        logger.info('Authentication method set to API Key');
      } else {
        throw new Error(`Invalid authentication method: ${authMethod}`);
      }
    }
    
    return {
      defaultModel: this.defaultModel || 'gemini-2.5-flash',
      authMethod: process.env.GEMINI_USE_OAUTH === 'true' ? 'oauth' : 'api_key'
    };
  }
}