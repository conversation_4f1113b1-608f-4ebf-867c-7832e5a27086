import { logger } from '../utils/logger.js';
import { REASONING_TEMPLATES, REASONING_KEYWORDS, REASONING_CHUNK_DELAY, THINKING_CONTENT_CHUNK_SIZE } from '../constants.js';

export class ThinkingHandler {
  constructor() {
    this.thinkingModels = new Set([
      'gemini-2.5-pro',
      'gemini-2.5-flash'
    ]);
    this.enableFakeThinking = process.env.ENABLE_FAKE_THINKING === 'true';
    this.streamThinkingAsContent = process.env.STREAM_THINKING_AS_CONTENT === 'true';
  }

  /**
   * Check if model supports thinking
   */
  isThinkingModel(modelId) {
    return this.thinkingModels.has(modelId) && this.enableFakeThinking;
  }

  /**
   * Generate sophisticated thinking content based on user request with adaptive depth
   */
  generateThinkingContent(prompt, options = {}) {
    if (!this.enableFakeThinking) {
      return null;
    }

    // Extract and analyze the user question
    const userQuestion = this.extractUserQuestion(prompt);
    const requestPreview = this.createIntelligentPreview(userQuestion);
    const promptAnalysis = this.analyzePromptComplexity(userQuestion);
    
    // Determine reasoning type with sophisticated analysis
    const reasoningType = this.determineReasoningType(userQuestion);
    
    // Get appropriate template and customize based on complexity
    const template = REASONING_TEMPLATES[reasoningType] || REASONING_TEMPLATES.general;
    
    // Generate base reasoning text with dynamic substitutions
    const reasoningTexts = template.map(msg => {
      return msg
        .replace("{requestPreview}", requestPreview)
        .replace("{complexityLevel}", this.getComplexityDescription(promptAnalysis.complexity))
        .replace("{reasoningType}", reasoningType);
    });
    
    // Add sophisticated additional thoughts
    const additionalThoughts = this.generateAdditionalThoughts(userQuestion, reasoningType);
    if (additionalThoughts) {
      reasoningTexts.push(additionalThoughts);
    }
    
    // Add meta-cognitive reflection for complex queries
    if (promptAnalysis.complexity > 0.6) {
      reasoningTexts.push(this.generateMetaCognitiveReflection(userQuestion, reasoningType));
    }
    
    // Add reasoning validation step for high-stakes queries
    if (this.isHighStakesQuery(userQuestion)) {
      reasoningTexts.push(this.generateReasoningValidation(reasoningType));
    }
    
    const fullText = reasoningTexts.join("");
    
    // Enhanced chunking based on reasoning complexity
    const chunks = this.splitIntoIntelligentChunks(fullText, promptAnalysis);
    
    return {
      fullText,
      chunks,
      metadata: {
        reasoningType,
        complexity: promptAnalysis.complexity,
        thoughtDepth: this.calculateThoughtDepth(fullText),
        processingTime: Date.now() // Simulated processing time
      }
    };
  }

  /**
   * Create an intelligent preview that captures the essence of the question
   */
  createIntelligentPreview(userQuestion) {
    const maxLength = 150;
    
    if (userQuestion.length <= maxLength) {
      return userQuestion;
    }
    
    // Try to find a natural break point
    const sentences = userQuestion.split(/[.!?]+/);
    if (sentences[0] && sentences[0].length <= maxLength) {
      return sentences[0].trim() + "...";
    }
    
    // Find the last complete word within the limit
    const truncated = userQuestion.substring(0, maxLength);
    const lastSpace = truncated.lastIndexOf(' ');
    
    if (lastSpace > maxLength * 0.7) {
      return truncated.substring(0, lastSpace) + "...";
    }
    
    return truncated + "...";
  }

  /**
   * Generate meta-cognitive reflection for complex reasoning
   */
  generateMetaCognitiveReflection(userQuestion, reasoningType) {
    const reflections = [
      "Meta-cognitive check - let me step back and evaluate my thinking process:\n\n",
      "Am I approaching this from the right angle? Let me consider:\n",
      "- Have I fully understood the nuances of this question?\n",
      "- Are there any biases or assumptions in my reasoning?\n",
      "- What alternative perspectives should I consider?\n",
      "- How can I ensure my response is both comprehensive and accessible?\n\n",
      "This is a complex topic, so I want to make sure I'm being thorough while remaining clear and practical.\n\n"
    ];
    
    return reflections.join("");
  }

  /**
   * Generate reasoning validation for high-stakes queries
   */
  generateReasoningValidation(reasoningType) {
    const validations = {
      technical: "Technical validation check:\n- Are my recommendations following current best practices?\n- Have I considered security and performance implications?\n- Is my technical advice actionable and safe to implement?\n\n",
      
      ethical: "Ethical reasoning validation:\n- Have I considered multiple ethical frameworks?\n- Am I being fair to all stakeholders involved?\n- Are there any potential harmful consequences I should address?\n\n",
      
      mathematical: "Mathematical verification:\n- Let me double-check my calculations and logic\n- Are there any computational errors or logical fallacies?\n- Does my solution method align with standard mathematical practices?\n\n",
      
      problem_solving: "Solution validation:\n- Is this solution practical and implementable?\n- Have I considered potential risks and mitigation strategies?\n- Will this actually solve the root problem, not just symptoms?\n\n"
    };
    
    return validations[reasoningType] || "Reasoning validation:\n- Is my analysis comprehensive and balanced?\n- Have I addressed the core question effectively?\n- Are there important considerations I might have missed?\n\n";
  }

  /**
   * Check if a query is high-stakes (requires extra validation)
   */
  isHighStakesQuery(userQuestion) {
    const highStakesIndicators = [
      'business', 'investment', 'medical', 'legal', 'safety', 'security',
      'financial', 'health', 'critical', 'important decision', 'career',
      'relationship', 'emergency', 'risk', 'dangerous', 'harmful'
    ];
    
    const lowerQuestion = userQuestion.toLowerCase();
    return highStakesIndicators.some(indicator => lowerQuestion.includes(indicator));
  }

  /**
   * Get complexity description for dynamic template substitution
   */
  getComplexityDescription(complexity) {
    if (complexity > 0.8) return "highly complex";
    if (complexity > 0.6) return "moderately complex";
    if (complexity > 0.4) return "somewhat complex";
    return "straightforward";
  }

  /**
   * Calculate thought depth metric
   */
  calculateThoughtDepth(fullText) {
    const indicators = [
      'consider', 'analyze', 'evaluate', 'examine', 'explore',
      'perspective', 'approach', 'framework', 'implication', 'consequence'
    ];
    
    let depth = 0;
    const lowerText = fullText.toLowerCase();
    
    indicators.forEach(indicator => {
      const matches = (lowerText.match(new RegExp(indicator, 'g')) || []).length;
      depth += matches;
    });
    
    return Math.min(depth / 10, 1.0); // Normalize to 0-1 scale
  }

  /**
   * Enhanced chunking that considers reasoning flow
   */
  splitIntoIntelligentChunks(text, promptAnalysis) {
    const baseChunkSize = THINKING_CONTENT_CHUNK_SIZE;
    
    // Adjust chunk size based on complexity
    const adjustedChunkSize = promptAnalysis.complexity > 0.7 ?
      baseChunkSize * 1.5 : baseChunkSize;
    
    const chunks = [];
    let remainingText = text;

    while (remainingText.length > 0) {
      if (remainingText.length <= adjustedChunkSize) {
        chunks.push(remainingText);
        break;
      }

      // Find intelligent break points
      let chunkEnd = adjustedChunkSize;
      const searchSpace = remainingText.substring(0, chunkEnd + 20);
      
      // Prioritize breaking at reasoning boundaries
      const reasoningBreaks = ["\n\n", ":\n", ".\n", "?\n", "!\n"];
      const punctuationBreaks = [". ", "? ", "! ", "; ", ", "];
      const spaceBreaks = [" "];
      
      const allBreaks = [...reasoningBreaks, ...punctuationBreaks, ...spaceBreaks];
      
      for (const breakPattern of allBreaks) {
        const lastBreak = searchSpace.lastIndexOf(breakPattern);
        if (lastBreak > adjustedChunkSize * 0.6) {
          chunkEnd = lastBreak + breakPattern.length;
          break;
        }
      }

      chunks.push(remainingText.substring(0, chunkEnd));
      remainingText = remainingText.substring(chunkEnd);
    }

    return chunks;
  }

  /**
   * Extract the actual user question from the full prompt
   */
  extractUserQuestion(prompt) {
    // Look for "User:" pattern to extract user's actual question
    const userMatch = prompt.match(/User:\s*(.+?)(?:\n\n|$)/s);
    if (userMatch) {
      return userMatch[1].trim();
    }
    
    // Fallback: look for the last meaningful line
    const lines = prompt.split('\n').filter(line => line.trim());
    if (lines.length > 0) {
      return lines[lines.length - 1].trim();
    }
    
    // Final fallback: use first 200 chars
    return prompt.substring(0, 200);
  }

  /**
   * Determine the type of reasoning needed based on prompt content with sophisticated analysis
   */
  determineReasoningType(prompt) {
    const lowerPrompt = prompt.toLowerCase();
    const scores = {};
    
    // Calculate weighted scores for each reasoning type
    for (const [type, keywords] of Object.entries(REASONING_KEYWORDS)) {
      scores[type] = 0;
      
      for (const keyword of keywords) {
        if (lowerPrompt.includes(keyword)) {
          // Give higher weight to longer, more specific keywords
          const weight = keyword.length > 5 ? 2 : 1;
          // Give extra weight if keyword appears multiple times
          const occurrences = (lowerPrompt.match(new RegExp(keyword, 'g')) || []).length;
          scores[type] += weight * occurrences;
        }
      }
    }
    
    // Additional contextual analysis
    this.analyzePromptContext(lowerPrompt, scores);
    
    // Find the highest scoring type
    const maxScore = Math.max(...Object.values(scores));
    if (maxScore === 0) {
      return 'general';
    }
    
    const bestType = Object.keys(scores).find(type => scores[type] === maxScore);
    
    // Log reasoning type selection for debugging
    logger.debug(`Reasoning type selected: ${bestType} (score: ${maxScore})`, { scores });
    
    return bestType;
  }

  /**
   * Analyze prompt context for additional reasoning type hints
   */
  analyzePromptContext(lowerPrompt, scores) {
    // Mathematical patterns
    if (/\d+[\+\-\*\/\=]\d+|equation|formula|calculate/.test(lowerPrompt)) {
      scores.mathematical = (scores.mathematical || 0) + 3;
    }
    
    // Ethical/philosophical patterns
    if (/should i|is it right|moral|ethical|what would you do/.test(lowerPrompt)) {
      scores.ethical = (scores.ethical || 0) + 3;
    }
    
    // Complex technical patterns
    if (/architecture|design pattern|best practice|scalable|performance/.test(lowerPrompt)) {
      scores.technical = (scores.technical || 0) + 2;
    }
    
    // Problem-solving indicators
    if (/how do i|help me|stuck|not working|broken/.test(lowerPrompt)) {
      scores.problem_solving = (scores.problem_solving || 0) + 2;
    }
    
    // Creative indicators
    if (/creative|unique|original|innovative|brainstorm/.test(lowerPrompt)) {
      scores.creative = (scores.creative || 0) + 2;
    }
    
    // Question complexity analysis
    const questionMarks = (lowerPrompt.match(/\?/g) || []).length;
    if (questionMarks > 1) {
      // Multiple questions suggest complex reasoning needed
      scores.general = (scores.general || 0) + 1;
    }
  }

  /**
   * Generate sophisticated additional contextual thoughts based on the prompt and reasoning type
   */
  generateAdditionalThoughts(prompt, reasoningType) {
    const thoughts = [];
    const promptAnalysis = this.analyzePromptComplexity(prompt);
    
    // Add type-specific additional thoughts with enhanced depth
    switch (reasoningType) {
      case 'technical':
        thoughts.push("Technical deep-dive considerations:\n");
        thoughts.push("- Architecture and design patterns that would be most appropriate\n");
        thoughts.push("- Performance implications and optimization opportunities\n");
        thoughts.push("- Security considerations and potential vulnerabilities\n");
        thoughts.push("- Error handling and edge case management\n");
        thoughts.push("- Testing strategies and validation approaches\n");
        thoughts.push("- Scalability and maintainability factors\n");
        thoughts.push("- Integration points and dependency management\n");
        if (promptAnalysis.complexity > 0.7) {
          thoughts.push("- This seems like a complex technical challenge, so I should also consider:\n");
          thoughts.push("  * Breaking this down into smaller, manageable components\n");
          thoughts.push("  * Potential migration or refactoring strategies\n");
          thoughts.push("  * Documentation and knowledge transfer needs\n");
        }
        thoughts.push("\n");
        break;
        
      case 'factual':
        thoughts.push("Factual accuracy and completeness check:\n");
        thoughts.push("- Verifying the accuracy of core facts and figures\n");
        thoughts.push("- Considering temporal context - has this information changed recently?\n");
        thoughts.push("- Identifying any important nuances, exceptions, or edge cases\n");
        thoughts.push("- Providing relevant historical context or background\n");
        thoughts.push("- Considering different perspectives or interpretations\n");
        if (promptAnalysis.hasNumbers) {
          thoughts.push("- Double-checking any numerical data or statistics\n");
        }
        if (promptAnalysis.hasComparisons) {
          thoughts.push("- Ensuring fair and accurate comparisons\n");
        }
        thoughts.push("- Suggesting reliable sources for further verification\n\n");
        break;
        
      case 'problem_solving':
        thoughts.push("Comprehensive problem-solving analysis:\n");
        thoughts.push("- Root cause analysis - what's really causing this issue?\n");
        thoughts.push("- Constraint identification - what limitations do we need to work within?\n");
        thoughts.push("- Resource assessment - what tools, time, or expertise is available?\n");
        thoughts.push("- Risk evaluation - what could go wrong with each approach?\n");
        thoughts.push("- Success metrics - how will we know if the solution works?\n");
        thoughts.push("- Implementation roadmap - what's the step-by-step plan?\n");
        thoughts.push("- Contingency planning - what backup options exist?\n");
        if (promptAnalysis.urgency > 0.5) {
          thoughts.push("- This seems urgent, so I should prioritize:\n");
          thoughts.push("  * Quick wins and immediate solutions\n");
          thoughts.push("  * Minimal viable solutions that can be improved later\n");
        }
        thoughts.push("\n");
        break;
        
      case 'creative':
        thoughts.push("Creative exploration and ideation:\n");
        thoughts.push("- Divergent thinking - what unconventional approaches could work?\n");
        thoughts.push("- Cross-domain inspiration - what can we learn from other fields?\n");
        thoughts.push("- Constraint reframing - how might limitations become opportunities?\n");
        thoughts.push("- Audience consideration - who will engage with this and how?\n");
        thoughts.push("- Iterative refinement - how can initial ideas be developed further?\n");
        thoughts.push("- Originality balance - being creative while remaining practical\n");
        thoughts.push("- Emotional resonance - what will make this memorable or impactful?\n");
        if (promptAnalysis.hasExamples) {
          thoughts.push("- Building on the examples provided while adding fresh perspectives\n");
        }
        thoughts.push("\n");
        break;
        
      case 'mathematical':
        thoughts.push("Mathematical rigor and verification:\n");
        thoughts.push("- Problem setup - ensuring I understand all given conditions\n");
        thoughts.push("- Method selection - choosing the most appropriate mathematical approach\n");
        thoughts.push("- Step-by-step verification - checking each calculation carefully\n");
        thoughts.push("- Alternative solution paths - are there other ways to solve this?\n");
        thoughts.push("- Result validation - does the answer make intuitive sense?\n");
        thoughts.push("- Generalization potential - what broader principles apply?\n");
        if (promptAnalysis.hasUnits) {
          thoughts.push("- Unit consistency - ensuring all measurements are properly handled\n");
        }
        thoughts.push("- Practical interpretation - what does this result mean in context?\n\n");
        break;
        
      case 'ethical':
        thoughts.push("Ethical reasoning and moral considerations:\n");
        thoughts.push("- Stakeholder analysis - who is affected and how?\n");
        thoughts.push("- Multiple ethical frameworks - utilitarian, deontological, virtue ethics\n");
        thoughts.push("- Cultural and contextual sensitivity - different perspectives to consider\n");
        thoughts.push("- Unintended consequences - what ripple effects might occur?\n");
        thoughts.push("- Precedent consideration - what broader implications does this have?\n");
        thoughts.push("- Value conflicts - where do different principles come into tension?\n");
        thoughts.push("- Practical implementation - how can ethical ideals be realized?\n");
        if (promptAnalysis.hasPersonalElements) {
          thoughts.push("- Personal vs. universal considerations - individual vs. societal perspectives\n");
        }
        thoughts.push("\n");
        break;
        
      default:
        thoughts.push("Comprehensive analysis considerations:\n");
        thoughts.push("- Multiple perspectives - what different viewpoints should I consider?\n");
        thoughts.push("- Assumption validation - what am I taking for granted?\n");
        thoughts.push("- Context sensitivity - how does the situation affect the approach?\n");
        thoughts.push("- Completeness check - what important aspects might I be missing?\n");
        thoughts.push("- Practical applicability - how can this be most useful?\n");
        thoughts.push("- Follow-up potential - what questions might this raise?\n");
        if (promptAnalysis.complexity > 0.6) {
          thoughts.push("- This appears complex, so I should ensure I address all dimensions\n");
        }
        thoughts.push("\n");
    }
    
    // Add dynamic contextual thoughts based on prompt analysis
    if (promptAnalysis.hasTimeConstraints) {
      thoughts.push("Time sensitivity noted - I should prioritize actionable, immediate guidance.\n\n");
    }
    
    if (promptAnalysis.hasMultipleQuestions) {
      thoughts.push("Multiple questions detected - I'll need to address each systematically while showing connections between them.\n\n");
    }
    
    return thoughts.join("");
  }

  /**
   * Analyze prompt complexity and characteristics for enhanced reasoning
   */
  analyzePromptComplexity(prompt) {
    const lowerPrompt = prompt.toLowerCase();
    
    return {
      complexity: this.calculateComplexityScore(prompt),
      hasNumbers: /\d+/.test(prompt),
      hasComparisons: /vs|versus|compared to|better than|worse than/.test(lowerPrompt),
      hasExamples: /example|for instance|such as/.test(lowerPrompt),
      hasTimeConstraints: /urgent|asap|quickly|deadline|soon/.test(lowerPrompt),
      hasMultipleQuestions: (prompt.match(/\?/g) || []).length > 1,
      hasUnits: /\b(kg|lb|meter|foot|second|hour|dollar|percent)\b/.test(lowerPrompt),
      hasPersonalElements: /\bi\b|my|me|personal/.test(lowerPrompt),
      urgency: this.calculateUrgencyScore(lowerPrompt)
    };
  }

  /**
   * Calculate complexity score based on various factors
   */
  calculateComplexityScore(prompt) {
    let score = 0;
    
    // Length factor
    score += Math.min(prompt.length / 1000, 0.3);
    
    // Technical terms
    const technicalTerms = ['algorithm', 'architecture', 'framework', 'implementation', 'optimization'];
    score += technicalTerms.filter(term => prompt.toLowerCase().includes(term)).length * 0.1;
    
    // Multiple concepts
    const concepts = prompt.split(/[,;]/).length;
    score += Math.min(concepts / 10, 0.2);
    
    // Question complexity
    const questionWords = ['how', 'why', 'what', 'when', 'where', 'which'];
    score += questionWords.filter(word => prompt.toLowerCase().includes(word)).length * 0.05;
    
    return Math.min(score, 1.0);
  }

  /**
   * Calculate urgency score based on language patterns
   */
  calculateUrgencyScore(lowerPrompt) {
    const urgentWords = ['urgent', 'asap', 'quickly', 'immediate', 'deadline', 'emergency'];
    const urgentPhrases = ['as soon as possible', 'right away', 'time sensitive'];
    
    let score = 0;
    score += urgentWords.filter(word => lowerPrompt.includes(word)).length * 0.2;
    score += urgentPhrases.filter(phrase => lowerPrompt.includes(phrase)).length * 0.3;
    
    return Math.min(score, 1.0);
  }

  /**
   * Split reasoning text into chunks for realistic streaming
   */
  splitIntoChunks(text) {
    const chunks = [];
    let remainingText = text;

    while (remainingText.length > 0) {
      if (remainingText.length <= THINKING_CONTENT_CHUNK_SIZE) {
        chunks.push(remainingText);
        break;
      }

      // Try to find a good break point (space, newline, punctuation)
      let chunkEnd = THINKING_CONTENT_CHUNK_SIZE;
      const searchSpace = remainingText.substring(0, chunkEnd + 10);
      const goodBreaks = [" ", "\n", ".", ",", "!", "?", ";", ":"];

      for (const breakChar of goodBreaks) {
        const lastBreak = searchSpace.lastIndexOf(breakChar);
        if (lastBreak > THINKING_CONTENT_CHUNK_SIZE * 0.7) {
          chunkEnd = lastBreak + 1;
          break;
        }
      }

      chunks.push(remainingText.substring(0, chunkEnd));
      remainingText = remainingText.substring(chunkEnd);
    }

    return chunks;
  }

  /**
   * Stream thinking content with sophisticated pacing and metadata
   */
  async streamThinking(thinkingContent, onChunk, options = {}) {
    if (!thinkingContent) return;

    const model = options.model || 'gemini-2.5-pro';
    const metadata = thinkingContent.metadata || {};
    
    // Calculate adaptive streaming delays based on content complexity
    const baseDelay = this.calculateStreamingDelay(metadata);
    
    if (this.streamThinkingAsContent) {
      // Enhanced R1 style with metadata and adaptive pacing
      
      // Send thinking start with metadata
      onChunk({
        id: `gemini-${Date.now()}`,
        object: 'chat.completion.chunk',
        created: Math.floor(Date.now() / 1000),
        model,
        choices: [{
          index: 0,
          delta: {
            content: '<thinking>\n',
            metadata: {
              reasoning_type: metadata.reasoningType,
              complexity: metadata.complexity,
              thought_depth: metadata.thoughtDepth
            }
          },
          finish_reason: null
        }]
      });

      // Adaptive delay after opening tag based on complexity
      await this.sleep(baseDelay.opening);

      // Stream reasoning content with intelligent pacing
      for (let i = 0; i < thinkingContent.chunks.length; i++) {
        const chunk = thinkingContent.chunks[i];
        const isLastChunk = i === thinkingContent.chunks.length - 1;
        
        // Determine chunk type for adaptive pacing
        const chunkType = this.analyzeChunkType(chunk);
        const chunkDelay = this.getChunkDelay(chunkType, baseDelay);
        
        onChunk({
          id: `gemini-${Date.now()}`,
          object: 'chat.completion.chunk',
          created: Math.floor(Date.now() / 1000),
          model,
          choices: [{
            index: 0,
            delta: {
              content: chunk,
              chunk_metadata: {
                chunk_index: i,
                chunk_type: chunkType,
                is_last_chunk: isLastChunk
              }
            },
            finish_reason: null
          }]
        });

        // Adaptive delay between chunks
        if (!isLastChunk) {
          await this.sleep(chunkDelay);
        }
      }

      // Close thinking tag with completion metadata
      onChunk({
        id: `gemini-${Date.now()}`,
        object: 'chat.completion.chunk',
        created: Math.floor(Date.now() / 1000),
        model,
        choices: [{
          index: 0,
          delta: {
            content: '\n</thinking>\n\n',
            completion_metadata: {
              total_chunks: thinkingContent.chunks.length,
              reasoning_complete: true,
              processing_time_ms: Date.now() - (metadata.processingTime || Date.now())
            }
          },
          finish_reason: null
        }]
      });

    } else {
      // Enhanced original mode with character-by-character streaming and metadata
      const chars = Array.from(thinkingContent.fullText);
      
      for (let i = 0; i < chars.length; i++) {
        const char = chars[i];
        const isLastChar = i === chars.length - 1;
        
        onChunk({
          id: `gemini-${Date.now()}`,
          object: 'chat.completion.chunk',
          created: Math.floor(Date.now() / 1000),
          model,
          choices: [{
            index: 0,
            delta: {
              reasoning: char,
              char_metadata: {
                char_index: i,
                total_chars: chars.length,
                progress: (i + 1) / chars.length
              }
            },
            finish_reason: null
          }]
        });
        
        // Adaptive character delay
        if (!isLastChar) {
          const charDelay = this.getCharacterDelay(char, baseDelay);
          await this.sleep(charDelay);
        }
      }
    }
  }

  /**
   * Calculate adaptive streaming delays based on content metadata
   */
  calculateStreamingDelay(metadata) {
    const complexity = metadata.complexity || 0.5;
    const thoughtDepth = metadata.thoughtDepth || 0.5;
    
    // Base delays in milliseconds
    const baseChunkDelay = REASONING_CHUNK_DELAY;
    const baseCharDelay = 30;
    
    // Adjust delays based on complexity and depth
    const complexityMultiplier = 1 + (complexity * 0.5);
    const depthMultiplier = 1 + (thoughtDepth * 0.3);
    
    return {
      opening: Math.floor(baseChunkDelay * complexityMultiplier),
      chunk: Math.floor(baseChunkDelay * 0.3 * complexityMultiplier),
      character: Math.floor(baseCharDelay * depthMultiplier),
      pause: Math.floor(baseChunkDelay * 0.8 * complexityMultiplier)
    };
  }

  /**
   * Analyze chunk type for adaptive pacing
   */
  analyzeChunkType(chunk) {
    const lowerChunk = chunk.toLowerCase();
    
    if (lowerChunk.includes('let me think') || lowerChunk.includes('i need to consider')) {
      return 'introduction';
    }
    if (lowerChunk.includes(':\n') || lowerChunk.includes('- ')) {
      return 'structured_analysis';
    }
    if (lowerChunk.includes('validation') || lowerChunk.includes('check')) {
      return 'verification';
    }
    if (lowerChunk.includes('conclusion') || lowerChunk.includes('finally')) {
      return 'conclusion';
    }
    if (lowerChunk.includes('?') || lowerChunk.includes('consider')) {
      return 'questioning';
    }
    
    return 'reasoning';
  }

  /**
   * Get appropriate delay for chunk type
   */
  getChunkDelay(chunkType, baseDelay) {
    const delayMultipliers = {
      introduction: 1.2,
      structured_analysis: 0.8,
      verification: 1.0,
      conclusion: 1.3,
      questioning: 1.1,
      reasoning: 1.0
    };
    
    const multiplier = delayMultipliers[chunkType] || 1.0;
    return Math.floor(baseDelay.chunk * multiplier);
  }

  /**
   * Get appropriate delay for character type
   */
  getCharacterDelay(char, baseDelay) {
    // Longer pauses for punctuation that indicates thinking pauses
    if (char === '.' || char === '!' || char === '?') {
      return baseDelay.pause;
    }
    if (char === ',' || char === ';' || char === ':') {
      return Math.floor(baseDelay.character * 1.5);
    }
    if (char === '\n') {
      return Math.floor(baseDelay.character * 2);
    }
    
    return baseDelay.character;
  }

  /**
   * Sleep utility
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Add thinking to completion response
   */
  addThinkingToResponse(response, thinkingContent) {
    if (!thinkingContent || !this.enableFakeThinking) {
      return response;
    }

    if (this.streamThinkingAsContent) {
      // For R1 style, thinking is included in content with <thinking> tags
      const thinkingText = `<thinking>\n${thinkingContent.fullText}\n</thinking>\n\n`;
      
      return {
        ...response,
        choices: response.choices.map(choice => ({
          ...choice,
          message: {
            ...choice.message,
            content: thinkingText + choice.message.content
          }
        }))
      };
    } else {
      // Original mode: add as reasoning field
      return {
        ...response,
        reasoning: thinkingContent.fullText,
        choices: response.choices.map(choice => ({
          ...choice,
          message: {
            ...choice.message,
            reasoning: thinkingContent.fullText
          }
        }))
      };
    }
  }
}

export const thinkingHandler = new ThinkingHandler();
