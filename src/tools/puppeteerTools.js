import { logger } from '../utils/logger.js';

/**
 * Puppeteer Browser Automation Tools
 * Provides general-purpose browser automation capabilities
 */
export class PuppeteerTools {
  constructor() {
    this.activeBrowsers = new Map();
  }

  /**
   * Register Puppeteer tools with the tool registry
   */
  registerTools(toolRegistry) {
    // Browser Launch Tool
    toolRegistry.registerTool('browser_launch', {
      description: 'Launch a new browser instance and navigate to a URL',
      inputSchema: {
        type: 'object',
        properties: {
          url: {
            type: 'string',
            description: 'URL to navigate to'
          },
          headless: {
            type: 'boolean',
            description: 'Run browser in headless mode',
            default: true
          },
          viewport: {
            type: 'object',
            description: 'Browser viewport settings',
            properties: {
              width: { type: 'number', default: 1280 },
              height: { type: 'number', default: 720 }
            }
          },
          session_id: {
            type: 'string',
            description: 'Session ID for browser instance',
            default: 'default'
          }
        },
        required: ['url']
      },
      category: 'browser',
      handler: this.launchBrowser.bind(this)
    });

    // Browser Action Tool
    toolRegistry.registerTool('browser_action', {
      description: 'Perform actions in an active browser session',
      inputSchema: {
        type: 'object',
        properties: {
          action: {
            type: 'string',
            description: 'Action to perform',
            enum: ['click', 'type', 'scroll', 'screenshot', 'navigate', 'wait', 'evaluate']
          },
          selector: {
            type: 'string',
            description: 'CSS selector for the element (required for click, type, wait)'
          },
          value: {
            type: 'string',
            description: 'Value to type or URL to navigate to'
          },
          script: {
            type: 'string',
            description: 'JavaScript code to evaluate (for evaluate action)'
          },
          timeout: {
            type: 'number',
            description: 'Timeout in milliseconds',
            default: 5000
          },
          session_id: {
            type: 'string',
            description: 'Browser session ID',
            default: 'default'
          }
        },
        required: ['action']
      },
      category: 'browser',
      handler: this.browserAction.bind(this)
    });

    // Browser Close Tool
    toolRegistry.registerTool('browser_close', {
      description: 'Close a browser session',
      inputSchema: {
        type: 'object',
        properties: {
          session_id: {
            type: 'string',
            description: 'Browser session ID to close',
            default: 'default'
          }
        }
      },
      category: 'browser',
      handler: this.closeBrowser.bind(this)
    });

    // Browser Info Tool
    toolRegistry.registerTool('browser_info', {
      description: 'Get information about active browser sessions',
      inputSchema: {
        type: 'object',
        properties: {}
      },
      category: 'browser',
      handler: this.getBrowserInfo.bind(this)
    });

    logger.info('Registered Puppeteer browser automation tools');
  }

  async launchBrowser(args) {
    const { 
      url, 
      headless = true, 
      viewport = { width: 1280, height: 720 }, 
      session_id = 'default' 
    } = args;

    try {
      // Import puppeteer dynamically
      const puppeteer = await import('puppeteer');

      // Configure launch options
      const launchOptions = {
        headless: headless,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu',
          '--disable-web-security',
          '--disable-features=VizDisplayCompositor'
        ]
      };

      // Try to use system Chromium on Linux
      if (process.platform === 'linux') {
        const fs = await import('fs');
        const chromiumPaths = [
          '/usr/bin/chromium-browser',
          '/usr/bin/chromium',
          '/usr/bin/google-chrome',
          '/usr/bin/google-chrome-stable'
        ];
        
        for (const chromiumPath of chromiumPaths) {
          if (fs.existsSync(chromiumPath)) {
            launchOptions.executablePath = chromiumPath;
            break;
          }
        }
      }

      // Close existing browser if session exists
      if (this.activeBrowsers.has(session_id)) {
        await this.closeBrowser({ session_id });
      }

      // Launch browser
      const browser = await puppeteer.default.launch(launchOptions);
      const page = await browser.newPage();
      
      // Set viewport
      await page.setViewport(viewport);
      
      // Navigate to URL
      await page.goto(url, { waitUntil: 'networkidle2', timeout: 30000 });

      // Store browser session
      this.activeBrowsers.set(session_id, {
        browser,
        page,
        url,
        created: new Date(),
        viewport
      });

      return {
        success: true,
        session_id,
        url,
        viewport,
        headless,
        title: await page.title(),
        source: 'browser_launch'
      };
    } catch (error) {
      return {
        success: false,
        error: `Failed to launch browser: ${error.message}`,
        troubleshooting: {
          install_puppeteer: 'npm install puppeteer',
          linux_chromium: 'sudo apt-get install chromium-browser',
          permissions: 'Check file permissions and system resources'
        },
        source: 'browser_launch'
      };
    }
  }

  async browserAction(args) {
    const { 
      action, 
      selector, 
      value, 
      script, 
      timeout = 5000, 
      session_id = 'default' 
    } = args;

    const session = this.activeBrowsers.get(session_id);
    if (!session) {
      return {
        success: false,
        error: `No active browser session found: ${session_id}`,
        available_sessions: Array.from(this.activeBrowsers.keys()),
        source: 'browser_action'
      };
    }

    const { page } = session;

    try {
      let result;

      switch (action) {
        case 'click':
          if (!selector) throw new Error('Selector required for click action');
          await page.waitForSelector(selector, { timeout });
          await page.click(selector);
          result = { clicked: selector };
          break;

        case 'type':
          if (!selector || !value) throw new Error('Selector and value required for type action');
          await page.waitForSelector(selector, { timeout });
          await page.focus(selector);
          await page.keyboard.selectAll();
          await page.type(selector, value);
          result = { typed: value, in: selector };
          break;

        case 'scroll':
          const scrollDirection = value || 'down';
          if (scrollDirection === 'down') {
            await page.evaluate(() => window.scrollBy(0, window.innerHeight));
          } else if (scrollDirection === 'up') {
            await page.evaluate(() => window.scrollBy(0, -window.innerHeight));
          } else if (scrollDirection === 'top') {
            await page.evaluate(() => window.scrollTo(0, 0));
          } else if (scrollDirection === 'bottom') {
            await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight));
          }
          result = { scrolled: scrollDirection };
          break;

        case 'screenshot':
          const screenshot = await page.screenshot({
            encoding: 'base64',
            fullPage: value === 'full'
          });
          result = {
            screenshot: `data:image/png;base64,${screenshot}`,
            viewport: session.viewport,
            full_page: value === 'full'
          };
          break;

        case 'navigate':
          if (!value) throw new Error('URL required for navigate action');
          await page.goto(value, { waitUntil: 'networkidle2', timeout: 30000 });
          session.url = value;
          result = { 
            navigated_to: value,
            title: await page.title()
          };
          break;

        case 'wait':
          if (selector) {
            await page.waitForSelector(selector, { timeout });
            result = { waited_for: selector };
          } else {
            const waitTime = parseInt(value) || 1000;
            await page.waitForTimeout(waitTime);
            result = { waited: `${waitTime}ms` };
          }
          break;

        case 'evaluate':
          if (!script) throw new Error('Script required for evaluate action');
          const evalResult = await page.evaluate(script);
          result = { 
            script,
            result: evalResult
          };
          break;

        default:
          throw new Error(`Unknown action: ${action}`);
      }

      // Get current page info
      const currentUrl = page.url();
      const title = await page.title();

      return {
        success: true,
        session_id,
        action,
        result,
        page_info: {
          url: currentUrl,
          title
        },
        source: 'browser_action'
      };
    } catch (error) {
      return {
        success: false,
        error: `Browser action failed: ${error.message}`,
        session_id,
        action,
        selector,
        value,
        source: 'browser_action'
      };
    }
  }

  async closeBrowser(args) {
    const { session_id = 'default' } = args;

    const session = this.activeBrowsers.get(session_id);
    if (!session) {
      return {
        success: false,
        error: `No active browser session found: ${session_id}`,
        available_sessions: Array.from(this.activeBrowsers.keys()),
        source: 'browser_close'
      };
    }

    try {
      await session.browser.close();
      this.activeBrowsers.delete(session_id);

      return {
        success: true,
        session_id,
        closed: true,
        source: 'browser_close'
      };
    } catch (error) {
      return {
        success: false,
        error: `Failed to close browser: ${error.message}`,
        session_id,
        source: 'browser_close'
      };
    }
  }

  async getBrowserInfo(args) {
    const sessions = [];
    
    for (const [sessionId, session] of this.activeBrowsers.entries()) {
      try {
        const title = await session.page.title();
        const url = session.page.url();
        
        sessions.push({
          session_id: sessionId,
          url,
          title,
          created: session.created,
          viewport: session.viewport
        });
      } catch (error) {
        sessions.push({
          session_id: sessionId,
          error: `Session error: ${error.message}`,
          created: session.created
        });
      }
    }

    return {
      success: true,
      active_sessions: sessions.length,
      sessions,
      puppeteer_available: true,
      source: 'browser_info'
    };
  }

  // Cleanup method for graceful shutdown
  async cleanup() {
    for (const [sessionId, session] of this.activeBrowsers.entries()) {
      try {
        await session.browser.close();
        logger.info(`Closed browser session: ${sessionId}`);
      } catch (error) {
        logger.warn(`Failed to close browser session ${sessionId}:`, error.message);
      }
    }
    this.activeBrowsers.clear();
  }
}

export const puppeteerTools = new PuppeteerTools();