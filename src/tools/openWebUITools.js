import { logger } from '../utils/logger.js';
import axios from 'axios';

/**
 * Open WebUI Integration Tools
 * Allows the AI agent to interact with Open WebUI interface and features
 */
export class OpenWebUITools {
  constructor(openWebUIBaseUrl = 'http://localhost:3000') {
    this.baseUrl = openWebUIBaseUrl;
    const headers = {
      'Content-Type': 'application/json',
      'User-Agent': 'Gemini-CLI-Wrapper/1.0'
    };

    if (process.env.OPEN_WEBUI_API_KEY) {
      headers['Authorization'] = `Bearer ${process.env.OPEN_WEBUI_API_KEY}`;
      logger.info(`OpenWebUI Authorization header set with API key: ${process.env.OPEN_WEBUI_API_KEY.substring(0, 10)}...`);
    } else {
      logger.warn('OPEN_WEBUI_API_KEY not found in environment variables');
    }

    this.apiClient = axios.create({
      baseURL: this.baseUrl,
      timeout: 30000,
      headers
    });

    // Log the final headers for debugging
    logger.info(`OpenWebUI axios client created with headers: ${JSON.stringify(headers)}`);
  }

  /**
   * Register Open WebUI tools with the tool registry
   */
  registerTools(toolRegistry) {
    // Web Search Tool (using Open WebUI's web search)
    toolRegistry.registerTool('openwebui_web_search', {
      description: 'Search the web using Open WebUI\'s web search functionality',
      inputSchema: {
        type: 'object',
        properties: {
          query: {
            type: 'string',
            description: 'Search query'
          },
          max_results: {
            type: 'number',
            description: 'Maximum number of results',
            default: 5
          }
        },
        required: ['query']
      },
      category: 'openwebui',
      handler: this.webSearch.bind(this)
    });

    // File Upload Tool
    toolRegistry.registerTool('openwebui_upload_file', {
      description: 'Upload a file to Open WebUI for processing',
      inputSchema: {
        type: 'object',
        properties: {
          file_path: {
            type: 'string',
            description: 'Path to the file to upload'
          },
          file_type: {
            type: 'string',
            description: 'Type of file (image, document, etc.)'
          }
        },
        required: ['file_path']
      },
      category: 'openwebui',
      handler: this.uploadFile.bind(this)
    });

    // Conversation Management
    toolRegistry.registerTool('openwebui_get_conversations', {
      description: 'Get list of conversations from Open WebUI',
      inputSchema: {
        type: 'object',
        properties: {
          limit: {
            type: 'number',
            description: 'Number of conversations to retrieve',
            default: 10
          }
        }
      },
      category: 'openwebui',
      handler: this.getConversations.bind(this)
    });

    // Create New Chat
    toolRegistry.registerTool('openwebui_create_chat', {
      description: 'Create a new chat session in Open WebUI',
      inputSchema: {
        type: 'object',
        properties: {
          title: {
            type: 'string',
            description: 'Title for the new chat'
          },
          model: {
            type: 'string',
            description: 'Model to use for the chat',
            default: 'gemini-2.5-flash'
          }
        }
      },
      category: 'openwebui',
      handler: this.createChat.bind(this)
    });

    // Send Message to Open WebUI (Cross-Session Only)
    toolRegistry.registerTool('openwebui_send_message', {
      description: 'Send a message to a different Open WebUI chat session (NOT current session to avoid loops)',
      inputSchema: {
        type: 'object',
        properties: {
          message: {
            type: 'string',
            description: 'Message to send'
          },
          chat_id: {
            type: 'string',
            description: 'Chat ID to send message to (must be different from current session)'
          },
          model: {
            type: 'string',
            description: 'Model to use',
            default: 'gemini-2.5-flash'
          }
        },
        required: ['message', 'chat_id']
      },
      category: 'openwebui',
      handler: this.sendMessage.bind(this)
    });

    // Get Open WebUI Models
    toolRegistry.registerTool('openwebui_get_models', {
      description: 'Get available models in Open WebUI',
      inputSchema: {
        type: 'object',
        properties: {}
      },
      category: 'openwebui',
      handler: this.getModels.bind(this)
    });

    // Browser Automation for Open WebUI
    toolRegistry.registerTool('openwebui_browser_action', {
      description: 'Perform browser actions on Open WebUI interface',
      inputSchema: {
        type: 'object',
        properties: {
          action: {
            type: 'string',
            description: 'Action to perform',
            enum: ['click', 'type', 'scroll', 'screenshot', 'navigate']
          },
          selector: {
            type: 'string',
            description: 'CSS selector for the element'
          },
          value: {
            type: 'string',
            description: 'Value to type or URL to navigate to'
          }
        },
        required: ['action']
      },
      category: 'openwebui',
      handler: this.browserAction.bind(this)
    });

    logger.info('Registered Open WebUI integration tools');
  }

  // Tool Handlers

  async webSearch(args) {
    const { query, max_results = 5 } = args;
    
    try {
      // Try to use Open WebUI's web search API if available
      const response = await this.apiClient.post('/api/web/search', {
        query,
        limit: max_results
      });

      return {
        success: true,
        query,
        results: response.data.results || response.data,
        source: 'openwebui_web_search'
      };
    } catch (error) {
      // Fallback to direct web search if Open WebUI API not available
      logger.warn('Open WebUI web search not available, using fallback');
      
      return {
        success: false,
        error: `Open WebUI web search failed: ${error.message}`,
        fallback_suggestion: 'Use the regular web_search tool instead',
        source: 'openwebui_web_search'
      };
    }
  }

  async uploadFile(args) {
    const { file_path, file_type } = args;
    
    try {
      const fs = await import('fs/promises');
      const path = await import('path');
      const FormData = (await import('form-data')).default;
      
      const fileBuffer = await fs.readFile(file_path);
      const fileName = path.basename(file_path);
      
      const formData = new FormData();
      formData.append('file', fileBuffer, fileName);
      
      const response = await this.apiClient.post('/api/files/upload', formData, {
        headers: {
          ...formData.getHeaders()
        }
      });

      return {
        success: true,
        file_path,
        file_id: response.data.id || response.data.file_id,
        file_url: response.data.url || response.data.file_url,
        file_type,
        source: 'openwebui_upload_file'
      };
    } catch (error) {
      return {
        success: false,
        error: `File upload failed: ${error.message}`,
        file_path,
        source: 'openwebui_upload_file'
      };
    }
  }

  async getConversations(args) {
    const { limit = 10 } = args;
    
    try {
      const response = await this.apiClient.get(`/api/chats?limit=${limit}`);
      
      return {
        success: true,
        conversations: response.data.chats || response.data,
        count: response.data.chats?.length || 0,
        source: 'openwebui_get_conversations'
      };
    } catch (error) {
      return {
        success: false,
        error: `Failed to get conversations: ${error.message}`,
        source: 'openwebui_get_conversations'
      };
    }
  }

  async createChat(args) {
    const { title, model = 'gemini-2.5-flash' } = args;
    
    const endpointsToTry = [
      { endpoint: '/api/v1/chats', payload: { title: title || `New Chat - ${new Date().toISOString()}`, model } },
      { endpoint: '/api/chats/new', payload: { title: title || `New Chat - ${new Date().toISOString()}`, model } }
    ];

    for (const { endpoint, payload } of endpointsToTry) {
      try {
        const response = await this.apiClient.post(endpoint, payload);
        return {
          success: true,
          chat_id: response.data.id || response.data.chat_id || response.data.uuid,
          title: response.data.title,
          model: response.data.model,
          created_at: response.data.created_at,
          endpoint_used: endpoint,
          source: 'openwebui_create_chat'
        };
      } catch (error) {
        logger.warn(`Failed to create chat with endpoint: ${endpoint}. Error: ${error.message}. Trying next endpoint.`);
      }
    }

    return {
      success: false,
      error: 'Failed to create chat after trying all available endpoints.',
      source: 'openwebui_create_chat'
    };
  }

  async sendMessage(args) {
    const { message, chat_id, model = 'gemini-2.5-flash' } = args;
    
    // Get current session ID to prevent circular loops
    const currentSessionId = process.env.OPENWEBUI_CURRENT_SESSION_ID;
    
    // Enhanced safety checks
    if (currentSessionId && chat_id === currentSessionId) {
      return {
        success: false,
        error: 'CIRCULAR_REFERENCE_PREVENTED',
        message: 'Cannot send message to current session - this would create a circular loop. Use a different chat_id.',
        current_session: currentSessionId,
        attempted_target: chat_id,
        source: 'openwebui_send_message'
      };
    }
    
    if (!chat_id) {
      return {
        success: false,
        error: 'CHAT_ID_REQUIRED',
        message: 'chat_id is required to prevent accidental loops. Specify target chat session.',
        current_session: currentSessionId,
        source: 'openwebui_send_message'
      };
    }
    
    if (currentSessionId && chat_id) {
      console.warn(`⚠️  Cross-session message: ${currentSessionId} → ${chat_id}`);
    }
    
    try {
      const endpoint = `/api/chats/${chat_id}/messages`;
      
      const payload = {
        content: message,
        role: 'user'
      };

      const response = await this.apiClient.post(endpoint, payload);

      return {
        success: true,
        message_id: response.data.id,
        response: response.data.content || response.data.choices?.[0]?.message?.content,
        chat_id: chat_id,
        model,
        session_info: {
          current_session: currentSessionId,
          target_session: chat_id
        },
        source: 'openwebui_send_message'
      };
    } catch (error) {
      return {
        success: false,
        error: `Failed to send message: ${error.message}`,
        message,
        chat_id,
        session_info: {
          current_session: currentSessionId,
          target_session: chat_id
        },
        source: 'openwebui_send_message'
      };
    }
  }

  async getModels(args) {
    try {
      // Debug logging
      logger.info(`OpenWebUI API Key present: ${!!process.env.OPEN_WEBUI_API_KEY}`);
      logger.info(`Base URL: ${this.baseUrl}`);
      logger.info(`Headers: ${JSON.stringify(this.apiClient.defaults.headers)}`);
      
      const response = await this.apiClient.get('/api/models');
      
      return {
        success: true,
        models: response.data.data || response.data,
        count: response.data.data?.length || 0,
        source: 'openwebui_get_models'
      };
    } catch (error) {
      logger.error(`OpenWebUI getModels error: ${error.message}`);
      if (error.response) {
        logger.error(`Response status: ${error.response.status}`);
        logger.error(`Response data: ${JSON.stringify(error.response.data)}`);
      }
      
      return {
        success: false,
        error: `Failed to get models: ${error.message}`,
        debug_info: {
          api_key_present: !!process.env.OPEN_WEBUI_API_KEY,
          base_url: this.baseUrl,
          status: error.response?.status,
          response_data: error.response?.data
        },
        source: 'openwebui_get_models'
      };
    }
  }

  async browserAction(args) {
    // Debug logging
    logger.info(`Browser action called with args: ${JSON.stringify(args)}`);
    
    const { action, selector, value } = args;
    
    // More debug logging
    logger.info(`Extracted action: ${action}, selector: ${selector}, value: ${value}`);
    
    try {
      // Check if browser automation is enabled
      if (process.env.ENABLE_BROWSER_AUTOMATION !== 'true') {
        return {
          success: false,
          error: 'Browser automation is disabled. Set ENABLE_BROWSER_AUTOMATION=true to enable.',
          action,
          source: 'openwebui_browser_action'
        };
      }

      // Try to import puppeteer
      let puppeteer;
      try {
        puppeteer = await import('puppeteer');
      } catch (importError) {
        return {
          success: false,
          error: 'Puppeteer not available. Install with: npm install puppeteer',
          action,
          install_command: 'npm install puppeteer',
          source: 'openwebui_browser_action'
        };
      }

      // Configure browser launch options for ARM64 Linux
      const launchOptions = {
        headless: process.env.PUPPETEER_HEADLESS !== 'false',
        executablePath: '/usr/bin/chromium-browser', // Use system Chromium
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu',
          '--disable-web-security',
          '--disable-features=VizDisplayCompositor'
        ]
      };

      // Launch browser and navigate to Open WebUI
      const browser = await puppeteer.launch(launchOptions);
      const page = await browser.newPage();
      
      // Set viewport for consistent screenshots
      await page.setViewport({ width: 1280, height: 720 });
      
      await page.goto(this.baseUrl, { waitUntil: 'networkidle2' });
      
      let result;
      
      switch (action) {
        case 'click':
          if (!selector) throw new Error('Selector required for click action');
          await page.waitForSelector(selector, { timeout: 5000 });
          await page.click(selector);
          result = { clicked: selector };
          break;
          
        case 'type':
          if (!selector || !value) throw new Error('Selector and value required for type action');
          await page.waitForSelector(selector, { timeout: 5000 });
          await page.type(selector, value);
          result = { typed: value, in: selector };
          break;
          
        case 'scroll':
          await page.evaluate(() => window.scrollBy(0, window.innerHeight));
          result = { scrolled: 'down' };
          break;
          
        case 'screenshot':
          const screenshot = await page.screenshot({
            encoding: 'base64',
            fullPage: false
          });
          result = {
            screenshot: `data:image/png;base64,${screenshot}`,
            viewport: { width: 1280, height: 720 }
          };
          break;
          
        case 'navigate':
          if (!value) throw new Error('URL required for navigate action');
          await page.goto(value, { waitUntil: 'networkidle2' });
          result = { navigated_to: value };
          break;
          
        default:
          throw new Error(`Unknown action: ${action}`);
      }
      
      await browser.close();
      
      return {
        success: true,
        action,
        result,
        browser_config: launchOptions,
        source: 'openwebui_browser_action'
      };
    } catch (error) {
      return {
        success: false,
        error: `Browser action failed: ${error.message}`,
        action,
        selector,
        value,
        troubleshooting: {
          puppeteer_install: 'npm install puppeteer',
          enable_automation: 'Set ENABLE_BROWSER_AUTOMATION=true',
          arm64_note: 'ARM64 Linux may require additional setup'
        },
        source: 'openwebui_browser_action'
      };
    }
  }

  // Utility method to check Open WebUI availability
  async checkAvailability() {
    try {
      const response = await this.apiClient.get('/health');
      return {
        available: true,
        status: response.status,
        version: response.data.version
      };
    } catch (error) {
      return {
        available: false,
        error: error.message
      };
    }
  }
}

export const openWebUITools = new OpenWebUITools();