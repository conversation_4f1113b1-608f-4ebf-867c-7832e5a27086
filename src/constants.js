/**
 * Constants for Gemini CLI Wrapper
 */

// Reasoning effort mapping to thinking budgets (based on GewoonJaap/gemini-cli-openai)
export const REASONING_EFFORT_BUDGETS = {
  none: 0,
  low: 1024,
  medium: {
    flash: 12288,
    default: 16384
  },
  high: {
    flash: 24576,
    default: 32768
  }
};

// Default thinking budget (-1 means dynamic allocation by Gemini)
export const DEFAULT_THINKING_BUDGET = -1;

// Disabled thinking budget
export const DISABLED_THINKING_BUDGET = 0;

// Static reasoning messages for fake thinking
export const REASONING_MESSAGES = [
  '🔍 **Analyzing the request...**\n\n',
  "🤔 Let me think about this step by step... ",
  "💭 I need to consider the context and provide a comprehensive response. ",
  "🎯 Based on my understanding, I should address the key points while being accurate and helpful. ",
  "✨ Let me formulate a clear and structured answer.\n\n"
];

// Default reasoning delay between chunks (in milliseconds)
export const REASONING_CHUNK_DELAY = 100;

// Default chunk size for streaming thinking content (in characters)
export const THINKING_CONTENT_CHUNK_SIZE = 15;

// Reasoning templates for different types of thinking
export const REASONING_TEMPLATES = {
  general: [
    '🔍 **Analyzing the request: "{requestPreview}"**\n\n',
    "🤔 Let me think about this step by step... ",
    "💭 I need to consider the context and provide a comprehensive response. ",
    "🎯 Based on my understanding, I should address the key points while being accurate and helpful. ",
    "✨ Let me formulate a clear and structured answer.\n\n"
  ],
  technical: [
    '🔧 **Technical Analysis: "{requestPreview}"**\n\n',
    "💻 This is a {complexityLevel} technical question that requires careful consideration of:\n",
    "- Architecture and design patterns\n",
    "- Performance implications\n",
    "- Security considerations\n",
    "- Best practices and maintainability\n\n",
    "Let me work through this systematically...\n\n"
  ],
  mathematical: [
    '🧮 **Mathematical Problem: "{requestPreview}"**\n\n',
    "📊 This is a {complexityLevel} mathematical problem. Let me approach this step by step:\n",
    "- Understanding the given conditions\n",
    "- Identifying the appropriate method\n",
    "- Working through the calculations carefully\n",
    "- Verifying the result\n\n"
  ],
  creative: [
    '🎨 **Creative Challenge: "{requestPreview}"**\n\n',
    "✨ This calls for creative thinking! Let me explore different approaches:\n",
    "- Brainstorming unique perspectives\n",
    "- Considering unconventional solutions\n",
    "- Balancing creativity with practicality\n",
    "- Ensuring the result is engaging and original\n\n"
  ],
  problem_solving: [
    '🔍 **Problem Analysis: "{requestPreview}"**\n\n',
    "🛠️ This is a {complexityLevel} problem that needs systematic analysis:\n",
    "- Root cause identification\n",
    "- Constraint evaluation\n",
    "- Solution brainstorming\n",
    "- Risk assessment\n",
    "- Implementation planning\n\n"
  ],
  ethical: [
    '⚖️ **Ethical Consideration: "{requestPreview}"**\n\n',
    "🤝 This involves ethical considerations that require careful thought:\n",
    "- Multiple stakeholder perspectives\n",
    "- Different ethical frameworks\n",
    "- Potential consequences and implications\n",
    "- Cultural and contextual factors\n\n"
  ],
  factual: [
    '📚 **Factual Inquiry: "{requestPreview}"**\n\n',
    "🔍 Let me ensure I provide accurate and comprehensive information:\n",
    "- Verifying key facts and figures\n",
    "- Considering different perspectives\n",
    "- Providing relevant context\n",
    "- Ensuring completeness and accuracy\n\n"
  ]
};

// Keywords for reasoning type detection
export const REASONING_KEYWORDS = {
  technical: [
    'code', 'programming', 'software', 'algorithm', 'database', 'api', 'framework',
    'architecture', 'design pattern', 'performance', 'optimization', 'security',
    'deployment', 'infrastructure', 'scalability', 'debugging', 'testing'
  ],
  mathematical: [
    'calculate', 'equation', 'formula', 'math', 'statistics', 'probability',
    'algebra', 'geometry', 'calculus', 'number', 'solve', 'proof', 'theorem'
  ],
  creative: [
    'creative', 'design', 'art', 'story', 'write', 'brainstorm', 'innovative',
    'unique', 'original', 'imaginative', 'artistic', 'inspiration', 'idea'
  ],
  problem_solving: [
    'problem', 'issue', 'challenge', 'solution', 'fix', 'resolve', 'troubleshoot',
    'debug', 'help', 'stuck', 'broken', 'not working', 'error'
  ],
  ethical: [
    'ethical', 'moral', 'right', 'wrong', 'should', 'ought', 'responsibility',
    'fairness', 'justice', 'values', 'principles', 'dilemma'
  ],
  factual: [
    'what is', 'define', 'explain', 'fact', 'information', 'data', 'research',
    'study', 'evidence', 'statistics', 'history', 'when', 'where', 'who'
  ]
};