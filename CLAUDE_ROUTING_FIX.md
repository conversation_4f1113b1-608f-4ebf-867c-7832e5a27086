# 🔧 Claude Routing Fix - <PERSON><PERSON><PERSON><PERSON> quyết vấn đề fallback về Gemini

## 🚨 Vấn đề đã đượ<PERSON> khắc phục

<PERSON> chọn `claude-4-sonnet` trên Open Web UI, model tự động fallback về Gemini thay vì sử dụng Claude provider như mong muốn.

## 🔍 Nguyên nhân

1. **Logic routing sai** trong `providerManager.js`:
   - Khi model không có trong mapping, luôn fallback về Gemini
   - Không kiểm tra tên model để xác định provider phù hợp

2. **Model mapping chưa đầy đủ**:
   - `claude-4-sonnet` có thể chưa được map đúng vào Claude provider
   - Thiếu logic inference từ tên model

## ✅ Giải pháp đã áp dụng

### 1. Sửa logic routing trong `providerManager.js`

```javascript
getProviderForModel(modelId) {
  const providerName = this.modelToProvider.get(modelId);
  if (!providerName) {
    // <PERSON><PERSON>m tra tên model để xác định provider
    if (modelId.includes('claude')) {
      logger.warn(`Model ${modelId} not in mapping, but name suggests Claude. Routing to Claude provider.`);
      return this.providers.get('claude');
    }
    if (modelId.includes('gemini')) {
      logger.warn(`Model ${modelId} not in mapping, but name suggests Gemini. Routing to Gemini provider.`);
      return this.providers.get('gemini');
    }
    // Throw error để trigger fallback logic
    throw new Error(`Model ${modelId} not found in any provider and could not be inferred.`);
  }
  
  return this.providers.get(providerName);
}
```

### 2. Cải thiện mock response trong `claudeProvider.js`

- Thông báo rõ ràng về trạng thái cấu hình
- Hướng dẫn cách khắc phục vấn đề authentication
- Phân biệt giữa lỗi cấu hình và lỗi CLI

## 🧪 Kiểm tra fix

Chạy script test:

```bash
chmod +x test_claude_routing_fix.sh
./test_claude_routing_fix.sh
```

## 📋 Các bước kiểm tra thủ công

### 1. Kiểm tra model routing
```bash
curl -X POST http://localhost:8010/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer test-key" \
  -d '{
    "model": "claude-4-sonnet",
    "messages": [{"role": "user", "content": "What model are you?"}]
  }'
```

### 2. Kiểm tra available models
```bash
curl -X GET http://localhost:8010/v1/models \
  -H "Authorization: Bearer test-key"
```

### 3. Kiểm tra provider status
```bash
curl -X GET http://localhost:8010/status \
  -H "Authorization: Bearer test-key"
```

## 🔧 Cấu hình Claude API (nếu cần)

Để sử dụng Claude API thực thay vì mock response:

```bash
# Thêm vào .env
ANTHROPIC_AUTH_TOKEN=your_claude_api_key
ANTHROPIC_BASE_URL=https://api.anthropic.com

# Hoặc sử dụng proxy
ANTHROPIC_BASE_URL=https://your-proxy-url.com
```

## 📊 Kết quả mong đợi

Sau khi áp dụng fix:

1. ✅ `claude-4-sonnet` sẽ route đến Claude provider
2. ✅ `gemini-2.5-pro` sẽ route đến Gemini provider  
3. ✅ Model không xác định sẽ được infer từ tên
4. ✅ Fallback logic hoạt động đúng khi có lỗi
5. ✅ Mock response rõ ràng về trạng thái cấu hình

## 🚀 Restart server

Sau khi áp dụng fix, restart server:

```bash
npm run start
# hoặc
pm2 restart ecosystem.config.cjs
```

## 📝 Logs để theo dõi

Kiểm tra logs để xác nhận routing đúng:

```
✅ Routing model claude-4-sonnet to claude provider
⚠️ Model claude-4-sonnet not in mapping, but name suggests Claude. Routing to Claude provider.
```

## 🔄 Fallback behavior

Nếu Claude provider fail:
1. Sẽ fallback về Gemini provider
2. Model sẽ được đổi thành `gemini-2.5-pro`
3. User sẽ nhận được response từ Gemini với thông báo fallback

---

**Fix này đảm bảo Open Web UI có thể chọn đúng Claude models và nhận được response từ Claude provider thay vì bị fallback về Gemini.**