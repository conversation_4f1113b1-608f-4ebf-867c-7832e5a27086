#!/bin/bash

echo "🧪 Testing Claude routing fix..."

# Test 1: Check if claude-4-sonnet routes to <PERSON> provider
echo "📋 Test 1: Testing claude-4-sonnet routing"
curl -X POST http://localhost:8010/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer test-key" \
  -d '{
    "model": "claude-4-sonnet",
    "messages": [
      {
        "role": "user", 
        "content": "Hello, what model are you and which provider are you using?"
      }
    ]
  }' | jq '.'

echo -e "\n" 

# Test 2: Check available models
echo "📋 Test 2: Checking available models"
curl -X GET http://localhost:8010/v1/models \
  -H "Authorization: Bearer test-key" | jq '.data[] | select(.id | contains("claude"))'

echo -e "\n"

# Test 3: Test with explicit claude model
echo "📋 Test 3: Testing claude-4-opus routing"
curl -X POST http://localhost:8010/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer test-key" \
  -d '{
    "model": "claude-4-opus",
    "messages": [
      {
        "role": "user", 
        "content": "Are you Claude or Gemini? Please be specific about your identity."
      }
    ]
  }' | jq '.'

echo -e "\n"

# Test 4: Check provider status
echo "📋 Test 4: Checking provider status"
curl -X GET http://localhost:8010/status \
  -H "Authorization: Bearer test-key" | jq '.providers'

echo "✅ Claude routing fix test completed!"