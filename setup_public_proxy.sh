#!/bin/bash

echo "🌐 Setting up public proxy for Cursor..."
echo "This will expose your local proxy server to a public URL"
echo ""

# Check if ngrok is installed
if ! command -v ngrok &> /dev/null; then
    echo "📦 Installing ngrok..."
    
    # Download and install ngrok
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        curl -s https://ngrok-agent.s3.amazonaws.com/ngrok.asc | sudo tee /etc/apt/trusted.gpg.d/ngrok.asc >/dev/null
        echo "deb https://ngrok-agent.s3.amazonaws.com buster main" | sudo tee /etc/apt/sources.list.d/ngrok.list
        sudo apt update && sudo apt install ngrok
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        if command -v brew &> /dev/null; then
            brew install ngrok/ngrok/ngrok
        else
            echo "Please install Homebrew first or download ngrok manually from https://ngrok.com/download"
            exit 1
        fi
    else
        echo "Please install ngrok manually from https://ngrok.com/download"
        exit 1
    fi
fi

echo "✅ ngrok is available"
echo ""

# Check if proxy server is running
if ! curl -s http://localhost:3001/health > /dev/null; then
    echo "❌ Proxy server is not running on port 3001"
    echo "Please start the proxy server first:"
    echo "  node cursor_proxy_server.cjs"
    exit 1
fi

echo "✅ Proxy server is running on port 3001"
echo ""

echo "🚀 Starting ngrok tunnel..."
echo "This will create a public URL for your proxy server"
echo ""
echo "⚠️  IMPORTANT: Keep this terminal open while using Cursor"
echo "⚠️  The public URL will change each time you restart ngrok"
echo ""

# Start ngrok
ngrok http 3001