#!/bin/bash

echo "🧪 Testing Gemini CLI Wrapper - Unrestricted Mode"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Server URL
SERVER_URL="http://localhost:8010"

echo -e "${YELLOW}Testing in RESTRICTED mode first...${NC}"
echo ""

# Test 1: Try to read a file outside project directory (should fail)
echo "1. Testing file read outside project directory (should FAIL):"
curl -s -X POST "$SERVER_URL/mcp/tools/execute" \
  -H "Content-Type: application/json" \
  -d '{
    "tool_name": "read_file",
    "arguments": {
      "path": "/etc/passwd"
    }
  }' | jq -r '.error // .result.content[0:100]'

echo ""

# Test 2: Try to execute restricted command (should fail)
echo "2. Testing restricted command execution (should FAIL):"
curl -s -X POST "$SERVER_URL/mcp/tools/execute" \
  -H "Content-Type: application/json" \
  -d '{
    "tool_name": "execute_command",
    "arguments": {
      "command": "sudo whoami"
    }
  }' | jq -r '.error // .result.stdout'

echo ""
echo -e "${YELLOW}Now testing what works in RESTRICTED mode...${NC}"
echo ""

# Test 3: Safe command (should work)
echo "3. Testing safe command (should WORK):"
curl -s -X POST "$SERVER_URL/mcp/tools/execute" \
  -H "Content-Type: application/json" \
  -d '{
    "tool_name": "execute_command",
    "arguments": {
      "command": "pwd"
    }
  }' | jq -r '.result.stdout // .error'

echo ""

# Test 4: Read file in project directory (should work)
echo "4. Testing file read in project directory (should WORK):"
curl -s -X POST "$SERVER_URL/mcp/tools/execute" \
  -H "Content-Type: application/json" \
  -d '{
    "tool_name": "read_file",
    "arguments": {
      "path": "package.json"
    }
  }' | jq -r 'if .result then "✅ Success: File read successfully (" + (.result.size | tostring) + " bytes)" else .error end'

echo ""
echo -e "${RED}⚠️  To enable UNRESTRICTED mode:${NC}"
echo "1. Set GEMINI_UNRESTRICTED_MODE=true in your .env file"
echo "2. Restart the server"
echo "3. Run this script again to see the difference"
echo ""
echo -e "${YELLOW}UNRESTRICTED mode allows:${NC}"
echo "- Reading/writing ANY file on the system"
echo "- Executing ANY command (rm, sudo, curl, etc.)"
echo "- Full system access"
echo ""
echo -e "${RED}⚠️  Only use in trusted, isolated environments!${NC}"