# 🧠 Quick Start - R1 Style Thinking

Hướng dẫn nhanh để sử dụng R1 Style Thinking (DeepSeek R1 style) với `<thinking>` tags.

## 🚀 Thiết lập R1 Style

### Bước 1: <PERSON><PERSON><PERSON> hình <PERSON>

```bash
# Copy và chỉnh sửa .env
cp env.example .env
```

<PERSON><PERSON><PERSON> bảo các setting sau trong `.env`:
```bash
# Bật R1 Style Thinking
ENABLE_FAKE_THINKING=true
STREAM_THINKING_AS_CONTENT=true

# Sử dụng model hỗ trợ thinking
DEFAULT_MODEL=gemini-2.5-pro
```

### Bước 2: Khởi động Server

```bash
npm start
# hoặc
./start.sh
```

### Bước 3: Test R1 Style

```bash
# Test với demo script
node examples/r1-style-demo.js

# Hoặc test manual
curl -X POST http://localhost:8010/v1/chat/completions \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gemini-2.5-pro",
    "messages": [
      {"role": "user", "content": "Solve: 2x + 5 = 13"}
    ],
    "stream": true
  }'
```

## 🎯 R1 Style Output

Với R1 style, bạn sẽ thấy output như này:

```
<thinking>
I need to solve the equation 2x + 5 = 13 step by step.

First, I'll isolate the term with x by subtracting 5 from both sides:
2x + 5 - 5 = 13 - 5
2x = 8

Now I'll divide both sides by 2 to solve for x:
2x ÷ 2 = 8 ÷ 2
x = 4

Let me verify: 2(4) + 5 = 8 + 5 = 13 ✓
</thinking>

To solve the equation 2x + 5 = 13, I'll work through this step by step.

Starting with: 2x + 5 = 13

Subtract 5 from both sides:
2x = 13 - 5
2x = 8

Divide both sides by 2:
x = 8 ÷ 2
x = 4

Therefore, x = 4.

To verify: 2(4) + 5 = 8 + 5 = 13 ✓
```

## 💻 Sử dụng với Code

### JavaScript/Node.js

```javascript
import axios from 'axios';

const client = axios.create({
  baseURL: 'http://localhost:8010',
  headers: {
    'Authorization': 'Bearer your-api-key',
    'Content-Type': 'application/json'
  }
});

// R1 Style Streaming
const response = await client.post('/v1/chat/completions', {
  model: 'gemini-2.5-pro',
  messages: [
    { role: 'user', content: 'Explain quantum computing step by step' }
  ],
  stream: true
}, { responseType: 'stream' });

// Process stream để thấy <thinking> tags
response.data.on('data', (chunk) => {
  const lines = chunk.toString().split('\n');
  for (const line of lines) {
    if (line.startsWith('data: ') && line !== 'data: [DONE]') {
      try {
        const data = JSON.parse(line.substring(6));
        const content = data.choices[0]?.delta?.content;
        if (content) {
          process.stdout.write(content);
        }
      } catch (e) {}
    }
  }
});
```

### Python

```python
import requests
import json

url = "http://localhost:8010/v1/chat/completions"
headers = {
    "Authorization": "Bearer your-api-key",
    "Content-Type": "application/json"
}

data = {
    "model": "gemini-2.5-pro",
    "messages": [
        {"role": "user", "content": "Write a Python function to find prime numbers"}
    ],
    "stream": True
}

response = requests.post(url, headers=headers, json=data, stream=True)

# Process R1 style output
for line in response.iter_lines():
    if line and line.startswith(b'data: '):
        try:
            chunk = json.loads(line[6:].decode())
            content = chunk['choices'][0]['delta'].get('content', '')
            if content:
                print(content, end='')
        except json.JSONDecodeError:
            continue
```

### LiteLLM Integration

R1 style hoạt động tuyệt vời với LiteLLM:

```python
import litellm

# Configure LiteLLM
litellm.api_base = "http://localhost:8010/v1"
litellm.api_key = "your-api-key"

# Use R1 style thinking
response = litellm.completion(
    model="gemini-2.5-pro",
    messages=[
        {"role": "user", "content": "Analyze the time complexity of quicksort"}
    ],
    stream=True
)

for chunk in response:
    if chunk.choices[0].delta.content:
        print(chunk.choices[0].delta.content, end="")
```

## 🔧 Advanced Usage

### Complex Problem Solving

```bash
curl -X POST http://localhost:8010/v1/chat/completions \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gemini-2.5-pro",
    "messages": [
      {
        "role": "user", 
        "content": "Design a distributed system for handling 1 million concurrent users. Consider scalability, reliability, and performance."
      }
    ],
    "stream": true
  }'
```

### Math Problem Solving

```bash
curl -X POST http://localhost:8010/v1/thinking/test \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Prove that the square root of 2 is irrational",
    "model": "gemini-2.5-pro",
    "stream": true
  }'
```

### Code Analysis

```bash
curl -X POST http://localhost:8010/v1/chat/completions \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gemini-2.5-flash",
    "messages": [
      {
        "role": "user",
        "content": "Review this code and suggest improvements:\n\ndef fibonacci(n):\n    if n <= 1:\n        return n\n    return fibonacci(n-1) + fibonacci(n-2)"
      }
    ],
    "stream": true
  }'
```

## 🎨 R1 Style Features

### 1. **Transparent Reasoning**
- Hiển thị quá trình suy nghĩ trong `<thinking>` tags
- Step-by-step problem solving
- Logic flow rõ ràng

### 2. **Better Debugging**
- Dễ dàng debug AI reasoning
- Hiểu được cách AI đi đến kết luận
- Identify potential errors in logic

### 3. **Educational Value**
- Học cách approach problems
- Understand problem-solving strategies
- Great for learning and teaching

### 4. **Tool Compatibility**
- Works with LiteLLM
- Compatible with OpenAI tools
- Standard streaming format

## 🔍 Troubleshooting

### R1 Style không hoạt động?

1. **Check environment variables:**
   ```bash
   echo $ENABLE_FAKE_THINKING  # should be 'true'
   echo $STREAM_THINKING_AS_CONTENT  # should be 'true'
   ```

2. **Check model support:**
   ```bash
   curl -H "Authorization: Bearer your-api-key" \
        http://localhost:8010/v1/models | jq '.data[] | select(.capabilities.thinking == true)'
   ```

3. **Test với thinking endpoint:**
   ```bash
   curl -X POST http://localhost:8010/v1/thinking/test \
     -H "Authorization: Bearer your-api-key" \
     -H "Content-Type: application/json" \
     -d '{"message": "Test thinking", "model": "gemini-2.5-pro"}'
   ```

### Không thấy `<thinking>` tags?

- Đảm bảo `STREAM_THINKING_AS_CONTENT=true`
- Sử dụng streaming mode (`"stream": true`)
- Sử dụng thinking-capable models (`gemini-2.5-pro`, `gemini-2.5-flash`)

## 📚 Examples

Chạy các demo examples:

```bash
# R1 Style comprehensive demo
node examples/r1-style-demo.js

# General thinking demo
node examples/thinking-demo.js

# Vision + R1 Style
node examples/vision-demo.js
```

## 🎯 Best Practices

1. **Use appropriate models**: `gemini-2.5-pro` for complex reasoning, `gemini-2.5-flash` for faster responses
2. **Enable streaming**: R1 style works best with streaming enabled
3. **Clear prompts**: Give clear, specific problems for better thinking output
4. **Parse thinking tags**: Process `<thinking>` content separately if needed
5. **Combine with tools**: R1 style works great with function calling and tools

---

**🧠 Happy thinking with R1 Style!**