# 🚀 Gemini CLI Wrapper v2.0 - <PERSON><PERSON><PERSON> năng mới

Phiên bản 2.0 c<PERSON>a Gemini CLI Wrapper đ<PERSON><PERSON><PERSON> nâng cấp với nhiều tính năng mạnh mẽ lấy cảm hứng từ dự án [gemini-cli-openai](https://github.com/GewoonJaap/gemini-cli-openai).

## 🆕 Tính năng mới

### 🖼️ Vision Support (Multimodal)

Hỗ trợ xử lý hình ảnh với các model vision-capable như `gemini-2.5-pro`, `gemini-2.5-flash`, `gemini-1.5-pro`.

#### Tính năng:
- ✅ Base64 encoded images (khuyến nghị)
- ✅ External image URLs
- ✅ Multiple images trong một conversation
- ✅ Supported formats: JPEG, PNG, GIF, WebP
- ✅ Maximum size: 20MB per image

#### Sử dụng:

```javascript
// Vision với base64
const response = await client.post('/v1/chat/completions', {
  model: 'gemini-2.5-pro',
  messages: [{
    role: 'user',
    content: [
      { type: 'text', text: 'What do you see in this image?' },
      { 
        type: 'image_url', 
        image_url: { url: 'data:image/jpeg;base64,/9j/4AAQ...' }
      }
    ]
  }]
});

// Vision với URL
const response = await client.post('/v1/chat/completions', {
  model: 'gemini-2.5-flash',
  messages: [{
    role: 'user',
    content: [
      { type: 'text', text: 'Describe this image' },
      { 
        type: 'image_url', 
        image_url: { url: 'https://example.com/image.jpg' }
      }
    ]
  }]
});
```

#### Test endpoint:
```bash
curl -X POST http://localhost:8010/v1/vision/test \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "image_url": "https://example.com/image.jpg",
    "model": "gemini-2.5-flash"
  }'
```

### 🧠 Thinking Models

Models có khả năng hiển thị quá trình suy nghĩ trước khi đưa ra câu trả lời cuối cùng.

#### Cấu hình:
```bash
# Bật thinking models
ENABLE_FAKE_THINKING=true

# DeepSeek R1 style với <thinking> tags
STREAM_THINKING_AS_CONTENT=true
```

#### Models hỗ trợ:
- `gemini-2.5-pro` ✅
- `gemini-2.5-flash` ✅

#### Sử dụng:

```javascript
// Non-streaming thinking
const response = await client.post('/v1/chat/completions', {
  model: 'gemini-2.5-pro',
  messages: [{
    role: 'user',
    content: 'Solve this step by step: What is 15 * 24?'
  }]
});

console.log('Reasoning:', response.data.reasoning);
console.log('Answer:', response.data.choices[0].message.content);

// Streaming thinking
const response = await client.post('/v1/chat/completions', {
  model: 'gemini-2.5-pro',
  messages: [{
    role: 'user',
    content: 'Analyze the pros and cons of renewable energy'
  }],
  stream: true
});
```

#### DeepSeek R1 Style:
Khi `STREAM_THINKING_AS_CONTENT=true`, thinking được stream như content với `<thinking>` tags:

```
<thinking>
Let me think about this step by step...

First, I need to identify the key elements...
Then, I'll consider the context...
</thinking>

Based on my analysis, here's the answer...
```

### 🔄 Smart Token Caching

Hệ thống quản lý OAuth token thông minh với auto-refresh và persistent storage.

#### Tính năng:
- ✅ Automatic token refresh
- ✅ Persistent disk storage
- ✅ Multiple token support
- ✅ Import từ Gemini CLI
- ✅ Smart expiry handling

#### Cấu hình:
```bash
TOKEN_CACHE_ENABLED=true
TOKEN_REFRESH_BUFFER=300000  # 5 minutes buffer
GOOGLE_CLIENT_ID=your-client-id
GOOGLE_CLIENT_SECRET=your-client-secret
```

#### API endpoints:
```bash
# Check cache status
GET /v1/debug/cache

# Import từ Gemini CLI
POST /v1/token/import

# Clear all tokens
DELETE /v1/token/clear
```

### 📡 Enhanced Streaming

Cải thiện streaming performance với support cho thinking và vision.

#### Tính năng:
- ✅ Thinking content streaming
- ✅ Tool calls trong stream
- ✅ Better error handling
- ✅ Optimized chunk processing

### 🔧 Debug & Monitoring

Các endpoint mới cho debugging và monitoring.

#### Endpoints:
```bash
# Enhanced models với capabilities
GET /v1/models

# Token cache status
GET /v1/debug/cache

# Vision test
POST /v1/vision/test

# Thinking test
POST /v1/thinking/test
```

## 📊 So sánh với dự án gốc

| Tính năng | Dự án hiện tại | gemini-cli-openai |
|-----------|----------------|-------------------|
| **Platform** | Node.js + Express | Cloudflare Workers |
| **Deployment** | Self-hosted | Serverless Edge |
| **Vision Support** | ✅ Base64 + URL | ✅ Base64 + URL |
| **Thinking Models** | ✅ Synthetic | ✅ Synthetic |
| **Token Caching** | ✅ Local disk | ✅ KV Storage |
| **MCP Support** | ✅ Full | ❌ None |
| **Tool Calling** | ✅ Advanced | ❌ Basic |
| **Session Management** | ✅ Full | ❌ Limited |

## 🚀 Cải tiến so với dự án gốc

### 1. **MCP Integration**
- Dự án hiện tại có full MCP support với tool registry
- Dự án gốc không có MCP

### 2. **Advanced Tool Calling**
- Session-aware tool execution
- Tool result caching
- Complex tool workflows

### 3. **Self-hosted Flexibility**
- Không phụ thuộc vào Cloudflare Workers
- Có thể customize và extend dễ dàng
- Full control over data và privacy

### 4. **Enhanced Authentication**
- Hybrid OAuth + API Key
- Multiple authentication methods
- Better security controls

## 📖 Hướng dẫn sử dụng

### 1. **Cài đặt tính năng mới**

```bash
# Update environment variables
cp env.example .env

# Edit .env để enable tính năng mới
ENABLE_FAKE_THINKING=true
STREAM_THINKING_AS_CONTENT=true
ENABLE_VISION=true
TOKEN_CACHE_ENABLED=true
```

### 2. **Test các tính năng**

```bash
# Test tất cả tính năng mới
./test_new_features.sh

# Test vision riêng
node examples/vision-demo.js

# Test thinking riêng
node examples/thinking-demo.js
```

### 3. **Integration với existing code**

Tất cả tính năng mới đều backward compatible. Code cũ sẽ hoạt động bình thường, tính năng mới chỉ activate khi được request.

```javascript
// Existing code vẫn hoạt động
const response = await client.post('/v1/chat/completions', {
  model: 'gemini-1.5-pro',
  messages: [{ role: 'user', content: 'Hello' }]
});

// New features chỉ activate khi cần
const visionResponse = await client.post('/v1/chat/completions', {
  model: 'gemini-2.5-pro',  // Vision-capable model
  messages: [{
    role: 'user',
    content: [
      { type: 'text', text: 'What is this?' },
      { type: 'image_url', image_url: { url: 'data:image/...' }}
    ]
  }]
});
```

## 🔮 Roadmap

### Planned Features:
- [ ] **Audio Support**: Speech-to-text và text-to-speech
- [ ] **File Upload**: Direct file upload endpoint
- [ ] **Batch Processing**: Multiple requests trong một call
- [ ] **Rate Limiting**: Advanced rate limiting với quotas
- [ ] **Analytics**: Usage analytics và monitoring
- [ ] **Plugin System**: Extensible plugin architecture

### Performance Improvements:
- [ ] **Connection Pooling**: Optimize HTTP connections
- [ ] **Response Caching**: Cache frequent responses
- [ ] **Load Balancing**: Multiple Gemini CLI instances
- [ ] **Memory Optimization**: Better memory management

## 🤝 Contributing

Contributions welcome! Đặc biệt quan tâm đến:

1. **Performance optimizations**
2. **New model integrations**
3. **Enhanced debugging tools**
4. **Better error handling**
5. **Documentation improvements**

## 📄 License

ISC License - Same as original project

---

**🎉 Gemini CLI Wrapper v2.0 - Bringing the best of both worlds!**