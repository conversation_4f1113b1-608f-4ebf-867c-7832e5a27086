#!/bin/bash

# M<PERSON>u sắc cho output
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}Khởi động Gemini CLI Wrapper${NC}"

# Kiểm tra Node.js version
NODE_VERSION=$(node -v)
echo -e "Node.js version: ${GREEN}${NODE_VERSION}${NC}"

# Kiểm tra PM2
if ! command -v pm2 &> /dev/null; then
    echo -e "${RED}PM2 chưa được cài đặt. Đang cài đặt...${NC}"
    sudo npm install -g pm2
else
    echo -e "${GREEN}PM2 đã được cài đặt${NC}"
fi

# Kiểm tra port đã được sử dụng chưa
PORT=8010
if lsof -Pi :$PORT -sTCP:LISTEN -t >/dev/null ; then
    echo -e "${RED}Port $PORT đang được sử dụng. Đang dừng tiến trình...${NC}"
    sudo lsof -ti:$PORT | xargs kill -9
    echo -e "${GREEN}Đã dừng tiến trình đang sử dụng port $PORT${NC}"
fi

# Khởi động server với PM2
echo -e "${BLUE}Khởi động server...${NC}"
pm2 delete all 2>/dev/null
pm2 start ecosystem.config.cjs

# Hiển thị IP để truy cập từ bên ngoài
SERVER_IP=$(hostname -I | awk '{print $1}')
echo -e "${GREEN}Server đang chạy tại:${NC}"
echo -e "${BLUE}Local:${NC} http://localhost:$PORT"
echo -e "${BLUE}External:${NC} http://$SERVER_IP:$PORT"

# Hiển thị lệnh kiểm tra
echo -e "\n${GREEN}Để kiểm tra API:${NC}"
echo -e "${BLUE}./test_api.sh${NC}"

# Hiển thị lệnh xem logs
echo -e "\n${GREEN}Để xem logs:${NC}"
echo -e "${BLUE}pm2 logs${NC}"

echo -e "\n${GREEN}Để dừng server:${NC}"
echo -e "${BLUE}pm2 stop all${NC}" 