# 🚀 Cursor Proxy Setup Guide

Hướng dẫn setup proxy server để Cursor có thể sử dụng endpoint OpenRouter của bạn.

## 📋 Tổng quan

Proxy server này sẽ:
- Chạy local trên port 3001
- Convert OpenAI API format sang OpenRouter format
- Route requests từ Cursor đến endpoint `https://kilocode.ai/api/openrouter`
- Hỗ trợ cả streaming và non-streaming responses

## 🔧 Cài đặt và Chạy

### 1. Cài đặt dependencies
```bash
npm install express cors node-fetch
```

### 2. Chạy proxy server
```bash
chmod +x start_cursor_proxy.sh
./start_cursor_proxy.sh
```

Hoặc chạy trực tiếp:
```bash
node cursor_proxy_server.js
```

### 3. Kiểm tra server đang chạy
```bash
curl http://localhost:3001/health
```

## ⚙️ Cấu hình Cursor

### 1. Mở Cursor Settings
- Nhấn `Cmd/Ctrl + ,` để mở Settings
- Tìm "AI" hoặc "Language Model" settings

### 2. Cấu hình Custom API
```
API Base URL: http://localhost:3001/v1
API Key: any-key-works (có thể để bất kỳ gì)
Model: anthropic/claude-sonnet-4
```

### 3. Hoặc cấu hình qua JSON settings
```json
{
  "cursor.ai.apiBaseUrl": "http://localhost:3001/v1",
  "cursor.ai.apiKey": "any-key-works",
  "cursor.ai.model": "anthropic/claude-sonnet-4"
}
```

## 📡 Endpoints Available

### Health Check
```
GET http://localhost:3001/health
```

### List Models (OpenAI compatible)
```
GET http://localhost:3001/v1/models
```

### Chat Completions (OpenAI compatible)
```
POST http://localhost:3001/v1/chat/completions
```

### Legacy Completions (OpenAI compatible)
```
POST http://localhost:3001/v1/completions
```

## 🧪 Test Proxy Server

### Test với curl:
```bash
# Test models
curl http://localhost:3001/v1/models

# Test chat completion
curl -X POST http://localhost:3001/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer any-key" \
  -d '{
    "model": "anthropic/claude-sonnet-4",
    "messages": [{"role": "user", "content": "Hello!"}],
    "max_tokens": 100
  }'
```

## 🔍 Troubleshooting

### Server không start được:
- Kiểm tra port 3001 có bị chiếm không: `lsof -i :3001`
- Cài đặt lại dependencies: `npm install express cors node-fetch`

### Cursor không connect được:
- Đảm bảo proxy server đang chạy: `curl http://localhost:3001/health`
- Kiểm tra API Base URL trong Cursor settings
- Restart Cursor sau khi thay đổi settings

### Model không available:
- Kiểm tra danh sách models: `curl http://localhost:3001/v1/models`
- Đảm bảo model name chính xác: `anthropic/claude-sonnet-4`

## 📊 Logs và Monitoring

Server sẽ log tất cả requests:
```
2025-01-07T08:53:00.000Z - GET /v1/models
📋 Fetching models from OpenRouter...
✅ Returned 24 models

2025-01-07T08:53:10.000Z - POST /v1/chat/completions
💬 Processing chat completion...
🤖 Model: anthropic/claude-sonnet-4
📝 Messages: 1 messages
✅ Chat completion successful
```

## 🚀 Production Notes

Để sử dụng trong production:
1. Thay đổi PORT trong environment variables
2. Add proper error handling và rate limiting
3. Add authentication nếu cần
4. Use process manager như PM2

```bash
# Chạy với PM2
npm install -g pm2
pm2 start cursor_proxy_server.js --name cursor-proxy
pm2 logs cursor-proxy
```

## 🎯 Model Support

Proxy hỗ trợ tất cả models từ OpenRouter endpoint:
- `anthropic/claude-sonnet-4` (200K context)
- `anthropic/claude-3.7-sonnet` (200K context)
- `anthropic/claude-opus-4` (200K context)
- Và nhiều models khác...

Để xem danh sách đầy đủ:
```bash
curl http://localhost:3001/v1/models | jq '.data[].id'