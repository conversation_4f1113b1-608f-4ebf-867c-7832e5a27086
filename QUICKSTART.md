# Quick Start Guide

## 🚀 Thiết lập nhanh trong 5 phút

### Bước 1: <PERSON>ài đặt và cấu hình

```bash
# 1. Clone hoặc tải xuống project
git clone <your-repo-url>
cd gemini-cli-wrapper

# 2. Chạy script setup tự động
./scripts/setup.sh
```

### Bước 2: Cấu hình authentication

Chỉnh sửa file `.env`:

```bash
# API key cho wrapper service (tự tạo)
API_KEY=your-secure-random-key-here

# Chọn MỘT trong hai phương pháp xác thực Gemini:

# Phương pháp 1: API Key (Khuyến nghị)
GEMINI_API_KEY=your-gemini-api-key-from-google-ai-studio

# Phương pháp 2: OAuth
GEMINI_USE_OAUTH=true
```

**Lấy Gemini API Key:**
1. T<PERSON>y cập [Google AI Studio](https://aistudio.google.com/app/apikey)
2. Tạo API key mới
3. Copy và paste vào `.env`

### Bước 3: Khởi động service

```bash
# Development mode
npm run dev

# Production mode
npm start
```

Service sẽ chạy tại: `http://localhost:8010`

### Bước 4: Test API

```bash
# Health check
curl http://localhost:8010/health

# List models
curl -H "Authorization: Bearer your-secure-random-key-here" \
     http://localhost:8010/models

# Chat completion
curl -X POST http://localhost:8010/chat/completions \
     -H "Authorization: Bearer your-secure-random-key-here" \
     -H "Content-Type: application/json" \
     -d '{
       "model": "gemini-1.5-pro",
       "messages": [
         {"role": "user", "content": "Hello!"}
       ]
     }'
```

## 🤖 Sử dụng với code

### JavaScript/Node.js

```javascript
import axios from 'axios';

const client = axios.create({
  baseURL: 'http://localhost:8010',
  headers: {
    'Authorization': 'Bearer your-secure-random-key-here',
    'Content-Type': 'application/json'
  }
});

// Chat completion
const response = await client.post('/chat/completions', {
  model: 'gemini-1.5-pro',
  messages: [
    { role: 'user', content: 'Explain AI in simple terms' }
  ]
});

console.log(response.data.choices[0].message.content);
```

### Python

```python
import requests

url = "http://localhost:8010/chat/completions"
headers = {
    "Authorization": "Bearer your-secure-random-key-here",
    "Content-Type": "application/json"
}

data = {
    "model": "gemini-1.5-pro",
    "messages": [
        {"role": "user", "content": "What is machine learning?"}
    ]
}

response = requests.post(url, headers=headers, json=data)
print(response.json()["choices"][0]["message"]["content"])
```

### Sử dụng Client Demo

```bash
# Chạy demo client (cần set API_KEY trong .env)
node examples/client-demo.js
```

## 🐳 Deploy với Docker

```bash
# Build và chạy
docker-compose up -d

# Hoặc manual
docker build -t gemini-cli-wrapper .
docker run -p 8010:8010 --env-file .env gemini-cli-wrapper
```

## 🔧 Troubleshooting

### Lỗi thường gặp

1. **"Gemini CLI not found"**
   ```bash
   npm install -g @google/gemini-cli
   ```

2. **"Authentication failed"**
   - Kiểm tra `GEMINI_API_KEY` trong `.env`
   - Hoặc thử với `GEMINI_USE_OAUTH=true`

3. **"Port already in use"**
   ```bash
   # Thay đổi port trong .env
   PORT=8011
   ```

4. **Connection timeout**
   - Kiểm tra firewall
   - Thử tăng timeout trong client

### Debug mode

```bash
export LOG_LEVEL=debug
npm run dev
```

## 📚 Tài liệu đầy đủ

Xem [README.md](README.md) để biết thêm chi tiết về:
- API endpoints
- Authentication methods
- Configuration options
- Development setup
- Production deployment

## 🆘 Hỗ trợ

- 📖 [Tài liệu](README.md)
- 🐛 [Report bugs](../../issues)
- 💬 [Discussions](../../discussions)
- 📧 [Contact](mailto:<EMAIL>) 