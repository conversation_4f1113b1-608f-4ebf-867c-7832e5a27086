{"name": "gemini-cli-wrapper", "version": "1.0.0", "type": "module", "description": "A CLI wrapper for Google's Gemini API", "main": "src/index.js", "scripts": {"start": "node src/index.js", "test": "echo \"Error: no test specified\" && exit 1", "test:enhanced-reasoning": "node test_enhanced_reasoning.js"}, "keywords": ["gemini", "ai", "cli", "wrapper"], "author": "", "license": "ISC", "mcpServers": {"puppeteer": {"command": "node", "args": ["puppeteer-mcp-server/dist/index.js"], "debug": true}}, "dependencies": {"@google/gemini-cli": "^0.1.15", "axios": "^1.10.0", "cors": "^2.8.5", "dotenv": "^16.4.1", "express": "^4.21.2", "form-data": "^4.0.3", "node-fetch": "^3.3.2", "puppeteer": "^24.12.0"}}