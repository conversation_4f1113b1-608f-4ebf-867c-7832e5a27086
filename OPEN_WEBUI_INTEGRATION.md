# Open WebUI Integration Guide

## 🎯 Tổng Quan

Tích hợp này cho phép **Gemini CLI Agent** tương tác trực tiếp với **Open WebUI interface** và sử dụng các tính năng của Open WebUI thông qua wrapper.

### Vấn Đề Đã Giải Quyết
- ❌ **Trước**: Agent chỉ hoạt động như API bridge, không thể tương tác với Open WebUI
- ✅ **Sau**: Agent c<PERSON> thể sử dụng web search, upload file, quản lý chat, và tương tác với Open WebUI interface

## 🚀 Tính Năng Mới

### 1. **Web Search qua Open WebUI**
```javascript
// Agent có thể search web thông qua Open WebUI
{
  "tool_name": "openwebui_web_search",
  "arguments": {
    "query": "latest AI developments",
    "max_results": 5
  }
}
```

### 2. **File Upload và Processing**
```javascript
// Upload file để Open WebUI xử lý
{
  "tool_name": "openwebui_upload_file",
  "arguments": {
    "file_path": "/path/to/document.pdf",
    "file_type": "document"
  }
}
```

### 3. **Conversation Management**
```javascript
// Lấy danh sách conversations
{
  "tool_name": "openwebui_get_conversations",
  "arguments": {
    "limit": 10
  }
}
```

### 4. **Chat Creation và Messaging**
```javascript
// Tạo chat mới
{
  "tool_name": "openwebui_create_chat",
  "arguments": {
    "title": "New Discussion",
    "model": "gemini-2.5-pro"
  }
}

// Gửi message
{
  "tool_name": "openwebui_send_message",
  "arguments": {
    "message": "Hello from agent!",
    "chat_id": "chat_123",
    "model": "gemini-2.5-flash"
  }
}
```

### 5. **Browser Automation**
```javascript
// Tương tác trực tiếp với Open WebUI interface
{
  "tool_name": "openwebui_browser_action",
  "arguments": {
    "action": "click",
    "selector": ".new-chat-button"
  }
}
```

### 6. **Model Management**
```javascript
// Lấy danh sách models từ Open WebUI
{
  "tool_name": "openwebui_get_models",
  "arguments": {}
}
```

## ⚙️ Cấu Hình

### 1. Environment Variables
Thêm vào file `.env`:
```bash
# Open WebUI Integration
OPEN_WEBUI_BASE_URL=http://localhost:3000
ENABLE_OPEN_WEBUI_TOOLS=true

# Optional: API authentication
OPEN_WEBUI_API_KEY=your-api-key-if-required

# Browser automation (requires puppeteer)
ENABLE_BROWSER_AUTOMATION=false
PUPPETEER_HEADLESS=true
```

### 2. Dependencies (Optional)
Để sử dụng browser automation:
```bash
npm install puppeteer form-data
```

### 3. Khởi Động Server
```bash
npm start
```

Bạn sẽ thấy log:
```
✅ Open WebUI integration tools registered
Registered X built-in tools
```

## 🧪 Testing

### Chạy Test Script
```bash
chmod +x test_openwebui_integration.sh
./test_openwebui_integration.sh
```

### Manual Testing
```bash
# Test web search
curl -X POST http://localhost:8010/mcp/tools/execute \
  -H "Content-Type: application/json" \
  -d '{
    "tool_name": "openwebui_web_search",
    "arguments": {
      "query": "AI news",
      "max_results": 3
    }
  }'

# Test conversation listing
curl -X POST http://localhost:8010/mcp/tools/execute \
  -H "Content-Type: application/json" \
  -d '{
    "tool_name": "openwebui_get_conversations",
    "arguments": {
      "limit": 5
    }
  }'
```

## 💬 Cách Sử Dụng Trong Chat

### Ví Dụ Conversations

**User**: "Search for latest AI news using Open WebUI"

**Agent**: Sẽ sử dụng `openwebui_web_search` tool để tìm kiếm thông qua Open WebUI interface.

---

**User**: "Create a new chat about machine learning in Open WebUI"

**Agent**: Sẽ sử dụng `openwebui_create_chat` tool để tạo chat mới với title phù hợp.

---

**User**: "Upload this document to Open WebUI for analysis"

**Agent**: Sẽ sử dụng `openwebui_upload_file` tool để upload file và xử lý.

---

**User**: "Show me my recent conversations from Open WebUI"

**Agent**: Sẽ sử dụng `openwebui_get_conversations` tool để lấy danh sách conversations.

## 🔧 API Endpoints Được Sử Dụng

### Open WebUI API Endpoints
```
GET  /api/models              # Lấy danh sách models
GET  /api/chats               # Lấy conversations
POST /api/chats               # Tạo chat mới
POST /api/chats/{id}/messages # Gửi message
POST /api/files/upload        # Upload file
POST /api/web/search          # Web search (nếu có)
GET  /health                  # Health check
```

### Wrapper API Endpoints
```
GET  /mcp/tools               # Xem available tools
POST /mcp/tools/execute       # Execute Open WebUI tools
```

## 🎯 Use Cases

### 1. **Research Assistant**
- Agent search web qua Open WebUI
- Upload documents để phân tích
- Tạo chat riêng cho từng topic
- Quản lý research conversations

### 2. **Content Creation**
- Upload images để generate descriptions
- Search for reference materials
- Create organized chats for different projects
- Manage drafts and revisions

### 3. **Data Analysis**
- Upload CSV/Excel files
- Create analysis chats
- Search for relevant context
- Generate reports in separate conversations

### 4. **Learning Assistant**
- Create study chats for different subjects
- Upload textbooks/papers
- Search for explanations
- Track learning progress

## 🔍 Troubleshooting

### Common Issues

#### 1. "Open WebUI not accessible"
```bash
# Check if Open WebUI is running
curl http://localhost:3000

# Check configuration
echo $OPEN_WEBUI_BASE_URL
```

#### 2. "Tools not registered"
```bash
# Check logs for errors
pm2 logs gemini-cli-wrapper

# Verify tools are available
curl http://localhost:8010/mcp/tools | jq '.tools[] | select(.category == "openwebui")'
```

#### 3. "API endpoints not found"
- Open WebUI API endpoints may vary by version
- Check Open WebUI documentation for correct endpoints
- Some features may require specific Open WebUI configurations

#### 4. "Browser automation fails"
```bash
# Install puppeteer
npm install puppeteer

# Set environment variables
ENABLE_BROWSER_AUTOMATION=true
PUPPETEER_HEADLESS=true
```

### Debug Mode
```bash
# Enable debug logging
LOG_LEVEL=debug npm start

# Check specific tool execution
curl -X POST http://localhost:8010/mcp/tools/execute \
  -H "Content-Type: application/json" \
  -d '{"tool_name": "openwebui_get_models", "arguments": {}}'
```

## 🚀 Advanced Features

### Custom Open WebUI URL
```bash
# For remote Open WebUI instance
OPEN_WEBUI_BASE_URL=http://your-server:3000
```

### API Authentication
```bash
# If Open WebUI requires authentication
OPEN_WEBUI_API_KEY=your-api-key
```

### Browser Automation Examples
```javascript
// Take screenshot of Open WebUI
{
  "tool_name": "openwebui_browser_action",
  "arguments": {
    "action": "screenshot"
  }
}

// Click new chat button
{
  "tool_name": "openwebui_browser_action",
  "arguments": {
    "action": "click",
    "selector": "[data-testid='new-chat-button']"
  }
}

// Type in chat input
{
  "tool_name": "openwebui_browser_action",
  "arguments": {
    "action": "type",
    "selector": "textarea[placeholder='Send a message']",
    "value": "Hello from automation!"
  }
}
```

## 📊 Monitoring

### Health Check
```bash
# Check integration status
curl http://localhost:8010/mcp/tools/execute \
  -H "Content-Type: application/json" \
  -d '{
    "tool_name": "openwebui_get_models",
    "arguments": {}
  }' | jq '.success'
```

### Performance Metrics
- Tool execution time
- Success/failure rates
- Open WebUI response times
- Browser automation performance

## 🎉 Kết Luận

Với tích hợp này, **Gemini CLI Agent** giờ đây có thể:

✅ **Tương tác trực tiếp** với Open WebUI interface  
✅ **Sử dụng các tools** của Open WebUI (web search, file upload)  
✅ **Quản lý conversations** và chats  
✅ **Tự động hóa** các tác vụ trong Open WebUI  
✅ **Mở rộng khả năng** beyond simple API bridge  

Agent không còn bị giới hạn chỉ là một API wrapper mà trở thành một **intelligent assistant** có thể tương tác đầy đủ với Open WebUI ecosystem!