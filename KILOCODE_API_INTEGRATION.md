# KiloCode API Integration for Claude Provider

This document describes the integration of KiloCode API as an alternative Claude provider, giving you access to multiple Claude models through a unified API endpoint.

## 🌟 Features

- **Multiple Claude Models**: Access to Claude 4 Sonnet, Claude 4 Opus, Claude 3.7 Sonnet, and Claude 3.5 Sonnet
- **OpenAI-Compatible API**: Standard OpenAI format for easy integration
- **Streaming Support**: Real-time response streaming
- **Automatic Fallback**: Falls back to CLI mode if API fails
- **Cost Tracking**: Detailed usage and cost information
- **High Availability**: Reliable API endpoint with 200+ models

## 🔄 Multi-Provider Architecture

The Claude provider now implements a **priority-based multi-provider system** for maximum reliability:

### Provider Priority Order

1. **KiloCode API** (Highest Priority)
   - OpenRouter-compatible API
   - Real-time streaming
   - Best performance and reliability

2. **YesScale CLI** (Automatic Fallback)
   - Alternative Claude CLI
   - Simulated streaming
   - Proven stability

3. **Mock Responses** (Last Resort)
   - Development/testing mode
   - Always available
   - Helpful error messages

### Automatic Provider Selection

```mermaid
flowchart TD
    Start[Request] --> CheckKilo{KiloCode API Available?}
    CheckKilo -->|Yes| KiloAPI[Use KiloCode API]
    CheckKilo -->|No| CheckYes{YesScale Available?}
    CheckYes -->|Yes| YesScale[Use YesScale CLI]
    CheckYes -->|No| Mock[Use Mock Response]
    
    KiloAPI --> Success[Return Response]
    YesScale --> Success
    Mock --> Success
```

### Configuration for Multi-Provider

```bash
# Priority 1: KiloCode API (Recommended)
KILOCODE_API_KEY=your-kilocode-api-key-here
KILOCODE_API_ENDPOINT=https://kilocode.ai/api/openrouter

# Priority 2: YesScale CLI (Fallback)
ANTHROPIC_AUTH_TOKEN=your-yescale-api-key-here
ANTHROPIC_BASE_URL=https://yescale.io/api

# Control API usage
CLAUDE_USE_API=true  # Enable API modes (default)
# CLAUDE_USE_API=false  # Force CLI-only mode
```

### Testing Provider Selection

```bash
# Test the provider selection logic
node simple_provider_test.js

# Expected output:
# 🧪 Testing Claude Provider Selection Logic
# 📋 Testing: KiloCode API Priority
#    ✅ PASS: Got expected mode "KiloCode API"
# 📋 Testing: YesScale Fallback
#    ✅ PASS: Got expected mode "YesScale CLI"
# 🎉 All tests passed!
```

## � Quick Setup

### 1. Automatic Setup (Recommended)

```bash
chmod +x setup_kilocode_api.sh
./setup_kilocode_api.sh
```

### 2. Manual Setup

1. **Get your API key** from KiloCode dashboard
2. **Configure environment variables**:

```bash
# Add to your .env file
KILOCODE_API_KEY=your-api-key-here
KILOCODE_API_ENDPOINT=https://kilocode.ai/api/openrouter
CLAUDE_USE_API=true
```

3. **Test the integration**:

```bash
node test_kilocode_integration.js
```

## 📋 Configuration Options

### Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `KILOCODE_API_KEY` | Your KiloCode API key | - | Yes |
| `KILOCODE_API_ENDPOINT` | API endpoint URL | `https://kilocode.ai/api/openrouter` | No |
| `CLAUDE_USE_API` | Enable API mode | `false` | No |

### Provider Modes

1. **API Mode** (`CLAUDE_USE_API=true`)
   - Uses KiloCode API exclusively
   - Best performance and reliability
   - Access to all Claude models

2. **CLI Mode** (`CLAUDE_USE_API=false`)
   - Uses Claude CLI exclusively
   - Requires Claude CLI installation
   - Limited to configured models

3. **Auto Mode** (`CLAUDE_USE_API=auto`)
   - Tries API first, falls back to CLI
   - Best of both worlds
   - Automatic error recovery

## 🤖 Available Models

| Model ID | API Model | Description |
|----------|-----------|-------------|
| `claude-4-sonnet` | `anthropic/claude-sonnet-4` | Most capable Claude model |
| `claude-4-opus` | `anthropic/claude-opus-4` | Premium Claude model |
| `claude-3.7-sonnet` | `anthropic/claude-3.7-sonnet` | Latest Claude 3.7 |
| `claude-3.5-sonnet` | `anthropic/claude-3.5-sonnet` | Balanced performance |

## 🔧 API Usage

### Basic Completion

```javascript
import { ClaudeCLIProvider } from './src/providers/claudeProvider.js';

const provider = new ClaudeCLIProvider();
await provider.initialize();

const response = await provider.createCompletion([
  { role: 'user', content: 'Hello Claude!' }
], {
  model: 'claude-4-sonnet',
  max_tokens: 1000
});

console.log(response.choices[0].message.content);
```

### Streaming

```javascript
await provider.streamCompletion([
  { role: 'user', content: 'Tell me a story' }
], {
  model: 'claude-3.5-sonnet'
}, (chunk) => {
  if (chunk.choices?.[0]?.delta?.content) {
    process.stdout.write(chunk.choices[0].delta.content);
  }
});
```

## 🧪 Testing

### Run Integration Tests

```bash
# Test all functionality
node test_kilocode_integration.js

# Test specific model
CLAUDE_MODEL=claude-4-opus node test_kilocode_integration.js
```

### Manual Testing

```bash
# Test API endpoint directly
curl -X POST "https://kilocode.ai/api/openrouter/chat/completions" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "anthropic/claude-sonnet-4",
    "messages": [{"role": "user", "content": "Hello!"}],
    "max_tokens": 100
  }'
```

## 🔍 Monitoring & Debugging

### Check Provider Status

```javascript
const status = await provider.getStatus();
console.log('Provider Status:', status);
```

### Enable Debug Logging

```bash
# Set log level to debug
LOG_LEVEL=debug npm start
```

### Common Issues

1. **API Key Invalid**
   - Verify your API key is correct
   - Check key permissions and quotas

2. **Network Issues**
   - Verify endpoint URL is accessible
   - Check firewall/proxy settings

3. **Model Not Available**
   - Check available models list
   - Verify model ID spelling

## 💰 Cost Management

The KiloCode API provides detailed cost tracking:

```javascript
const response = await provider.createCompletion(messages);
console.log('Usage:', response.usage);
// Output: { prompt_tokens: 29, completion_tokens: 38, total_tokens: 67, cost: 0.00003285 }
```

## 🔒 Security

- **API Key Protection**: Never commit API keys to version control
- **Environment Variables**: Store sensitive data in `.env` files
- **Rate Limiting**: API includes built-in rate limiting
- **HTTPS Only**: All communications are encrypted

## 📚 Advanced Usage

### Custom Model Configuration

```javascript
// Add custom model
await provider.addCustomModel({
  id: 'claude-custom',
  name: 'Custom Claude Model',
  apiId: 'anthropic/claude-3.5-sonnet',
  maxTokens: 100000
});
```

### Provider Settings

```javascript
// Update provider settings
await provider.updateSettings({
  defaultModel: 'claude-4-sonnet',
  baseUrl: 'https://kilocode.ai/api/openrouter',
  authToken: 'new-api-key'
});
```

## 🆘 Support

- **Documentation**: Check this guide and API documentation
- **Testing**: Use the provided test scripts
- **Logs**: Enable debug logging for detailed information
- **Fallback**: CLI mode available as backup

## 📈 Performance Tips

1. **Use Streaming**: For better user experience with long responses
2. **Model Selection**: Choose appropriate model for your use case
3. **Token Management**: Monitor usage to optimize costs
4. **Caching**: Implement response caching for repeated queries
5. **Error Handling**: Implement proper retry logic

---

**Note**: This integration provides a robust alternative to Claude CLI with enhanced features and reliability. The API mode is recommended for production use.