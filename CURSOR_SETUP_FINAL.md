# 🎯 Cursor Setup - Final Configuration

## ✅ Proxy Server Status
- **Proxy URL**: `https://mali-cents-fall-sbjct.trycloudflare.com`
- **API Key**: `kilo-claude-2025`
- **Status**: ✅ Active and Working

## 🔧 Cursor Configuration

### 1. Open Cursor Settings
- Press `Cmd/Ctrl + ,` to open settings
- Search for "API"

### 2. Configure Custom API
```json
{
  "cursor.cpp.apiProvider": "openai-compatible",
  "cursor.cpp.apiUrl": "https://mali-cents-fall-sbjct.trycloudflare.com/v1",
  "cursor.cpp.apiKey": "kilo-claude-2025"
}
```

### 3. Alternative: Use Settings UI
1. Go to **Cursor Settings** → **Features** → **AI**
2. Set **API Provider**: `OpenAI Compatible`
3. Set **Base URL**: `https://mali-cents-fall-sbjct.trycloudflare.com/v1`
4. Set **API Key**: `kilo-claude-2025`

## 🤖 Available Models

### Primary Models
- `anthropic/claude-sonnet-4` - Latest Claude Sonnet 4
- `anthropic/claude-3.7-sonnet` - Claude 3.7 Sonnet
- `anthropic/claude-opus-4` - Claude Opus 4

### Model Mapping (Automatic)
The proxy automatically maps common model names:
- `claude-4-sonnet` → `anthropic/claude-sonnet-4`
- `claude-sonnet-4` → `anthropic/claude-sonnet-4`
- `claude-sonnet-4-20250514` → `anthropic/claude-sonnet-4`
- `claude-3.7-sonnet` → `anthropic/claude-3.7-sonnet`

## 🧪 Test Configuration

### Test 1: Simple Chat
1. Open Cursor
2. Start a new chat
3. Ask: "What model are you?"
4. Should respond as Claude

### Test 2: Code Generation
1. Ask Cursor to generate some code
2. Verify it uses Claude's capabilities

## 🔍 Troubleshooting

### If Cursor shows "Invalid API Key"
- Double-check the API key: `kilo-claude-2025`
- Ensure the URL ends with `/v1`

### If Models Don't Load
- Check if tunnel is still active
- Restart Cursor
- Try refreshing the model list

### If Chat Fails
- Check proxy logs in terminal
- Verify tunnel URL is accessible
- Try a different model name

## 📊 Proxy Logs
Monitor the proxy terminal for real-time logs:
```
✅ Chat completion successful
🔑 Valid API key: kilo-claud...
🤖 Original Model: claude-sonnet-4
🎯 Using OpenRouter Model: anthropic/claude-sonnet-4
```

## 🚀 Success Indicators
- ✅ API key validation passes
- ✅ Model mapping works correctly
- ✅ Chat completions return Claude responses
- ✅ Cursor can access all available models
- ✅ JWT token authentication working

## 🔧 Starting the Proxy Server

### Method 1: With Token (Recommended)
```bash
./start_cursor_proxy_with_token.sh
```

### Method 2: Manual
```bash
export JWT_TOKEN="your-jwt-token-here"
node cursor_proxy_server.cjs
```

## 🔍 Common Issues & Solutions

### Issue: "OpenRouter API error: 500 Internal Server Error"
**Cause**: JWT token is missing or expired
**Solution**:
1. Set JWT token: `export JWT_TOKEN="your-token"`
2. Restart proxy server
3. Use `./start_cursor_proxy_with_token.sh` for automatic token setup

### Issue: "Invalid API key" in Cursor
**Cause**: Wrong API key in Cursor settings
**Solution**: Use exactly `kilo-claude-2025` as API key

### Issue: Models not loading
**Cause**: Tunnel URL changed or proxy not running
**Solution**:
1. Check if proxy is running
2. Verify tunnel URL is accessible
3. Restart tunnel if needed

## 📝 Notes
- The tunnel URL may change if restarted
- Keep the proxy server running while using Cursor
- The API key `kilo-claude-2025` is specifically for this proxy
- All requests are forwarded to your OpenRouter endpoint with proper JWT authentication
- Use `./start_cursor_proxy_with_token.sh` to ensure JWT token is always set

---

**Status**: 🟢 Ready to use with Cursor!
**Last Updated**: JWT token issue resolved - proxy working correctly