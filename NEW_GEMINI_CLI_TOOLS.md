# New Gemini CLI Tools Integration

## Overview

This document describes the 5 new Gemini CLI tools that have been successfully integrated into the MCP server as of July 31, 2025. These tools were identified from Gemini CLI v0.1.9 and added to provide enhanced file system operations and web content processing capabilities.

## New Tools Added

### 1. `list_directory`
**Description**: Lists files and subdirectories in a given directory with optional glob pattern filtering.

**Parameters**:
- `path` (required): Directory path to list (relative or absolute if unrestricted)
- `ignore_patterns` (optional): Array of glob patterns to ignore (e.g., ["*.log", "node_modules"])

**Example Usage**:
```json
{
  "tool_name": "list_directory",
  "arguments": {
    "path": ".",
    "ignore_patterns": ["node_modules", "*.log"]
  }
}
```

**RBAC Permissions**: Available to `admin`, `user`, and `guest` roles.

### 2. `search_file_content`
**Description**: Searches for regular expression patterns within file contents in a specified directory.

**Parameters**:
- `pattern` (required): Regular expression pattern to search for
- `directory` (optional): Directory to search in (default: ".")
- `file_pattern` (optional): Glob pattern to filter files (default: "*")
- `recursive` (optional): Search recursively in subdirectories (default: true)

**Example Usage**:
```json
{
  "tool_name": "search_file_content",
  "arguments": {
    "pattern": "registerTool",
    "directory": "src",
    "file_pattern": "*.js",
    "recursive": true
  }
}
```

**RBAC Permissions**: Available to `admin` and `user` roles.

### 3. `glob`
**Description**: Efficiently finds files matching specific glob patterns, returning paths sorted by modification time.

**Parameters**:
- `pattern` (required): Glob pattern to match files (e.g., "src/**/*.js", "**/*.md")
- `base_directory` (optional): Base directory to search from (default: ".")
- `sort_by_time` (optional): Sort results by modification time, newest first (default: true)

**Example Usage**:
```json
{
  "tool_name": "glob",
  "arguments": {
    "pattern": "**/*.js",
    "base_directory": "src",
    "sort_by_time": true
  }
}
```

**RBAC Permissions**: Available to `admin` and `user` roles.

### 4. `web_fetch`
**Description**: Processes content from URLs, including local and private network addresses. Can handle up to 20 URLs with specific processing instructions.

**Parameters**:
- `urls` (required): Array of URLs to fetch (up to 20)
- `instructions` (optional): Instructions for processing the content
- `timeout` (optional): Request timeout in milliseconds (default: 10000)

**Example Usage**:
```json
{
  "tool_name": "web_fetch",
  "arguments": {
    "urls": ["http://httpbin.org/json"],
    "instructions": "Extract the JSON data",
    "timeout": 15000
  }
}
```

**RBAC Permissions**: Available to `admin`, `user`, and `guest` roles.

### 5. `read_many_files`
**Description**: Reads content from multiple files specified by paths or glob patterns. Concatenates text files and can process images/PDFs if explicitly requested.

**Parameters**:
- `paths` (required): Array of file paths or glob patterns
- `target_directory` (optional): Target directory for relative paths (default: ".")
- `include_metadata` (optional): Include file metadata like size and modification time (default: true)

**Example Usage**:
```json
{
  "tool_name": "read_many_files",
  "arguments": {
    "paths": ["package.json", "README.md"],
    "target_directory": ".",
    "include_metadata": true
  }
}
```

**RBAC Permissions**: Available to `admin` and `user` roles.

## Implementation Details

### Files Modified
1. **`src/tools/toolRegistry.js`**: Added new tool registrations and handlers
2. **`rbac_policies.json`**: Updated RBAC permissions for new tools
3. **Server restart**: Required to load new tools and RBAC policies

### RBAC Configuration
The new tools have been configured with appropriate RBAC permissions:

- **Admin role**: Full access to all 5 new tools
- **User role**: Access to all 5 new tools (safe operations)
- **Guest role**: Limited access to `list_directory` and `web_fetch` only

### Security Considerations
- All file operations respect the unrestricted mode setting
- File access is limited to project directory unless `GEMINI_UNRESTRICTED_MODE=true`
- Web fetch operations use Gemini CLI for processing, providing built-in safety
- RBAC system prevents unauthorized access based on user roles

## Testing Results

All new tools have been successfully tested and are working correctly:

✅ **list_directory**: Found 107 items in test directory  
✅ **search_file_content**: Found 27 matches for "registerTool" pattern  
✅ **glob**: Found 15 JavaScript files in src directory  
✅ **read_many_files**: Successfully read package.json file  
✅ **web_fetch**: Tool functioning (external URL access depends on network)  

## Total Tools Available

The MCP server now provides **22 total tools** (increased from 17), including:
- 6 Core file/system tools (including 5 new ones)
- 6 Open WebUI integration tools
- 4 Puppeteer browser automation tools
- 6 Other utility tools (web search, memory, code analysis, etc.)

## Usage in Applications

These tools can be used by any application that connects to the MCP server, including:
- VS Code extensions
- AI assistants (Claude, ChatGPT, etc.)
- Open WebUI integrations
- Custom applications via REST API

The tools follow OpenAI function calling format and are automatically available through the `/mcp/tools` and `/mcp/tools/execute` endpoints.

## Future Considerations

- Monitor Gemini CLI updates for additional new tools
- Consider adding more sophisticated file processing capabilities
- Evaluate performance impact of new tools under heavy usage
- Potential for custom tool plugins based on these patterns

---

**Last Updated**: July 31, 2025  
**Gemini CLI Version**: 0.1.9  
**MCP Server Version**: 1.0.0