# Claude Integration Guide

## Tổng Quan

Gemini CLI Wrapper đã được mở rộng để hỗ tr<PERSON> <PERSON> models thông qua Claude CLI. Bây giờ bạn có thể sử dụng cả Gemini và Claude models thông qua cùng một API interface.

## Models Có Sẵn

### Claude Models
- **claude-4-sonnet**: <PERSON> 4 Sonnet - Model có khả năng reasoning và phân tích nâng cao
- **claude-4-opus**: <PERSON> 4 Opus - Model premium với hiệu suất vượt trội cho các tác vụ phức tạp

### Gemini Models (vẫn hoạt động)
- **gemini-2.5-pro**: Gemini 2.5 Pro
- **gemini-2.5-flash**: Gemini 2.5 Flash
- **gemini-1.5-pro**: Gemini 1.5 Pro
- **gemini-1.5-flash**: Gemini 1.5 Flash

## Cấu Hình

### Environment Variables

Thêm các biến môi trường sau vào file `.env`:

```bash
# Claude Authentication - API key từ bên thứ 3
ANTHROPIC_AUTH_TOKEN=your-claude-api-key-here

# Claude Base URL - URL của service bên thứ 3
ANTHROPIC_BASE_URL=https://your-claude-provider-url.com
```

### Cấu Hình Claude CLI

Claude CLI sẽ tự động sử dụng các environment variables:
- `ANTHROPIC_API_KEY` (được set từ `ANTHROPIC_AUTH_TOKEN`)
- `ANTHROPIC_BASE_URL`

## Sử Dụng API

### 1. Lấy Danh Sách Models

```bash
curl -X GET http://localhost:8010/v1/models
```

Response sẽ bao gồm cả Gemini và Claude models:

```json
{
  "data": [
    {
      "id": "claude-4-sonnet",
      "object": "model",
      "owned_by": "gemini-cli-wrapper",
      "capabilities": {
        "vision": true,
        "thinking": false,
        "max_tokens": 200000
      }
    },
    {
      "id": "claude-4-opus",
      "object": "model",
      "owned_by": "gemini-cli-wrapper",
      "capabilities": {
        "vision": true,
        "thinking": false,
        "max_tokens": 200000
      }
    }
  ]
}
```

### 2. Chat Completion với Claude

```bash
curl -X POST http://localhost:8010/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "claude-4-sonnet",
    "messages": [
      {
        "role": "user",
        "content": "Hello! Can you help me with coding?"
      }
    ]
  }'
```

### 3. Streaming với Claude

```bash
curl -X POST http://localhost:8010/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "claude-4-opus",
    "messages": [
      {
        "role": "user",
        "content": "Write a Python function to calculate fibonacci numbers"
      }
    ],
    "stream": true
  }'
```

## Kiến Trúc

### Provider Manager

Hệ thống sử dụng `ProviderManager` để quản lý cả Gemini và Claude providers:

```
Client Request → ProviderManager → Route to appropriate provider → Response
```

### Model Routing

- Models bắt đầu với `claude-*` → Claude Provider
- Models bắt đầu với `gemini-*` → Gemini Provider
- Model không tồn tại → Fallback to default provider

### Fallback Mechanism

1. Nếu Claude provider fail → Fallback to Gemini
2. Nếu Gemini provider fail → Fallback to Claude
3. Nếu cả hai fail → Mock response

## Testing

### Chạy Test Script

```bash
chmod +x test_claude_integration.sh
./test_claude_integration.sh
```

### Manual Testing

1. **Test Claude Models:**
```bash
# Test Claude 4 Sonnet
curl -X POST http://localhost:8010/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{"model": "claude-4-sonnet", "messages": [{"role": "user", "content": "Hello Claude!"}]}'

# Test Claude 4 Opus
curl -X POST http://localhost:8010/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{"model": "claude-4-opus", "messages": [{"role": "user", "content": "Hello Claude Opus!"}]}'
```

2. **Test Fallback:**
```bash
# Test với model không tồn tại
curl -X POST http://localhost:8010/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{"model": "non-existent-model", "messages": [{"role": "user", "content": "Test fallback"}]}'
```

## API Key Management

### Thêm Claude API Key

```bash
curl -X POST http://localhost:8010/api-keys \
  -H "Content-Type: application/json" \
  -d '{
    "key": "your-claude-api-key",
    "name": "Claude API Key",
    "provider": "claude"
  }'
```

### Lấy Danh Sách API Keys

```bash
curl -X GET http://localhost:8010/api-keys
```

Response:
```json
{
  "keys": {
    "gemini": [...],
    "claude": [
      {
        "id": "claude_key_1234567890",
        "name": "Claude API Key",
        "createdAt": "2025-01-07T01:00:00.000Z",
        "key": "sk-a...xyz"
      }
    ]
  }
}
```

## Troubleshooting

### 1. Claude CLI Không Hoạt Động

**Lỗi:** `Credit balance is too low`

**Giải pháp:**
- Kiểm tra `ANTHROPIC_AUTH_TOKEN` có đúng không
- Kiểm tra `ANTHROPIC_BASE_URL` có accessible không
- Hệ thống sẽ tự động fallback to mock response

### 2. Model Không Tìm Thấy

**Lỗi:** `Model claude-4-sonnet not found`

**Giải pháp:**
- Kiểm tra model ID có đúng không
- Restart server để rebuild model mapping
- Kiểm tra Claude provider có được initialize không

### 3. Provider Fallback

Khi một provider fail, hệ thống sẽ:
1. Log warning về failure
2. Attempt fallback to other provider
3. Use compatible model từ fallback provider
4. Return response với provider info

### 4. Environment Variables

Đảm bảo các biến môi trường được set đúng:

```bash
# Check environment variables
echo $ANTHROPIC_AUTH_TOKEN
echo $ANTHROPIC_BASE_URL
```

## Logs

Theo dõi logs để debug:

```bash
# Start server với debug logs
LOG_LEVEL=debug npm start

# Hoặc check logs trong runtime
tail -f logs/app.log
```

## Tính Năng Nâng Cao

### 1. Provider-Specific Settings

```bash
curl -X POST http://localhost:8010/settings \
  -H "Content-Type: application/json" \
  -d '{
    "defaultModel": "claude-4-sonnet",
    "provider": "claude"
  }'
```

### 2. Model Management

```bash
# Add custom Claude model
curl -X POST http://localhost:8010/models \
  -H "Content-Type: application/json" \
  -d '{
    "id": "claude-custom",
    "name": "Custom Claude Model",
    "description": "Custom Claude configuration",
    "maxTokens": 200000,
    "provider": "claude"
  }'
```

### 3. Provider Status

```bash
curl -X GET http://localhost:8010/status
```

Response sẽ show status của cả hai providers:
```json
{
  "providers": {
    "gemini": {
      "initialized": true,
      "defaultModel": "gemini-2.5-pro"
    },
    "claude": {
      "initialized": true,
      "defaultModel": "claude-4-sonnet",
      "baseUrl": "https://your-claude-provider-url.com"
    }
  },
  "totalModels": 6,
  "activeProviders": 2
}
```

## Kết Luận

Tích hợp Claude đã được hoàn thành với:
- ✅ 2 Claude models (claude-4-sonnet, claude-4-opus)
- ✅ Provider Manager quản lý cả Gemini và Claude
- ✅ Automatic routing dựa trên model ID
- ✅ Fallback mechanism giữa providers
- ✅ Environment variables cho custom API endpoint
- ✅ Full API compatibility với OpenAI format
- ✅ Streaming support cho cả hai providers

Bây giờ bạn có thể sử dụng cả Gemini và Claude models thông qua cùng một API interface!