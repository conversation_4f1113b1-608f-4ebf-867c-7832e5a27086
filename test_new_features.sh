#!/bin/bash

# Test script for new features in Gemini CLI Wrapper v2.0
# This script tests Vision, Thinking, and Token Caching features

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
API_BASE_URL="http://localhost:8010"
API_KEY="${API_KEY:-your-api-key-here}"

echo -e "${BLUE}🚀 Gemini CLI Wrapper v2.0 - New Features Test${NC}"
echo -e "${BLUE}=================================================${NC}"
echo ""

# Function to make API calls
make_request() {
    local method=$1
    local endpoint=$2
    local data=$3
    
    if [ -n "$data" ]; then
        curl -s -X "$method" \
             -H "Authorization: Bearer $API_KEY" \
             -H "Content-Type: application/json" \
             -d "$data" \
             "$API_BASE_URL$endpoint"
    else
        curl -s -X "$method" \
             -H "Authorization: Bearer $API_KEY" \
             "$API_BASE_URL$endpoint"
    fi
}

# Test 1: Check enhanced models endpoint
echo -e "${YELLOW}🔍 Test 1: Enhanced Models Endpoint${NC}"
echo "Testing /v1/models with capabilities..."

response=$(make_request "GET" "/v1/models")
echo "$response" | jq '.data[] | {id: .id, vision: .capabilities.vision, thinking: .capabilities.thinking}' 2>/dev/null || echo "$response"
echo ""

# Test 2: Vision capabilities
echo -e "${YELLOW}🖼️  Test 2: Vision Capabilities${NC}"
echo "Testing vision with a simple image URL..."

vision_data='{
  "model": "gemini-2.5-flash",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "What do you see in this image?"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/6/6a/JavaScript-logo.png/256px-JavaScript-logo.png"
          }
        }
      ]
    }
  ]
}'

response=$(make_request "POST" "/v1/chat/completions" "$vision_data")
echo "$response" | jq -r '.choices[0].message.content' 2>/dev/null || echo "$response"
echo ""

# Test 3: Vision test endpoint
echo -e "${YELLOW}🔧 Test 3: Vision Test Endpoint${NC}"
echo "Testing dedicated vision endpoint..."

vision_test_data='{
  "image_url": "https://upload.wikimedia.org/wikipedia/commons/thumb/a/a7/React-icon.svg/256px-React-icon.svg.png",
  "model": "gemini-2.5-flash"
}'

response=$(make_request "POST" "/v1/vision/test" "$vision_test_data")
echo "$response" | jq -r '.choices[0].message.content' 2>/dev/null || echo "$response"
echo ""

# Test 4: Thinking models (if enabled)
echo -e "${YELLOW}🧠 Test 4: Thinking Models${NC}"
echo "Testing thinking capabilities..."

thinking_data='{
  "model": "gemini-2.5-pro",
  "messages": [
    {
      "role": "user",
      "content": "Solve this step by step: What is 15 * 24?"
    }
  ]
}'

response=$(make_request "POST" "/v1/chat/completions" "$thinking_data")
if echo "$response" | jq -e '.reasoning' >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Thinking enabled - showing reasoning:${NC}"
    echo "$response" | jq -r '.reasoning' 2>/dev/null
    echo ""
    echo -e "${GREEN}Final answer:${NC}"
    echo "$response" | jq -r '.choices[0].message.content' 2>/dev/null
else
    echo -e "${YELLOW}⚠️  Thinking not enabled (set ENABLE_FAKE_THINKING=true)${NC}"
    echo "$response" | jq -r '.choices[0].message.content' 2>/dev/null || echo "$response"
fi
echo ""

# Test 5: Thinking test endpoint
echo -e "${YELLOW}🔧 Test 5: Thinking Test Endpoint${NC}"
echo "Testing dedicated thinking endpoint..."

thinking_test_data='{
  "message": "What are the key principles of good software design?",
  "model": "gemini-2.5-pro"
}'

response=$(make_request "POST" "/v1/thinking/test" "$thinking_test_data")
echo "$response" | jq -r '.choices[0].message.content' 2>/dev/null || echo "$response"
echo ""

# Test 6: Token cache status
echo -e "${YELLOW}🔄 Test 6: Token Cache Status${NC}"
echo "Checking token cache status..."

response=$(make_request "GET" "/v1/debug/cache")
echo "$response" | jq '.' 2>/dev/null || echo "$response"
echo ""

# Test 7: Health check with enhanced info
echo -e "${YELLOW}❤️  Test 7: Enhanced Health Check${NC}"
echo "Checking server health and status..."

response=$(make_request "GET" "/health")
echo "$response" | jq '.' 2>/dev/null || echo "$response"
echo ""

response=$(make_request "GET" "/status")
echo "$response" | jq '.features' 2>/dev/null || echo "$response"
echo ""

# Test 8: Streaming with thinking (if enabled)
echo -e "${YELLOW}🌊 Test 8: Enhanced Streaming${NC}"
echo "Testing streaming with potential thinking output..."

streaming_data='{
  "model": "gemini-2.5-flash",
  "messages": [
    {
      "role": "user",
      "content": "Write a short poem about artificial intelligence."
    }
  ],
  "stream": true
}'

echo "Streaming response:"
curl -s -X POST \
     -H "Authorization: Bearer $API_KEY" \
     -H "Content-Type: application/json" \
     -d "$streaming_data" \
     "$API_BASE_URL/v1/chat/completions" | \
while IFS= read -r line; do
    if [[ $line == data:* ]] && [[ $line != "data: [DONE]" ]]; then
        content=$(echo "${line#data: }" | jq -r '.choices[0].delta.content // .choices[0].delta.reasoning // empty' 2>/dev/null)
        if [ -n "$content" ] && [ "$content" != "null" ]; then
            printf "%s" "$content"
        fi
    fi
done
echo ""
echo ""

# Summary
echo -e "${BLUE}📊 Test Summary${NC}"
echo -e "${BLUE}===============${NC}"
echo -e "${GREEN}✅ Enhanced models endpoint with capabilities${NC}"
echo -e "${GREEN}✅ Vision support with image URLs${NC}"
echo -e "${GREEN}✅ Vision test endpoint${NC}"
echo -e "${GREEN}✅ Thinking models (if enabled)${NC}"
echo -e "${GREEN}✅ Thinking test endpoint${NC}"
echo -e "${GREEN}✅ Token cache management${NC}"
echo -e "${GREEN}✅ Enhanced health checks${NC}"
echo -e "${GREEN}✅ Enhanced streaming responses${NC}"
echo ""

echo -e "${BLUE}💡 Tips for full testing:${NC}"
echo "• Set ENABLE_FAKE_THINKING=true for thinking models"
echo "• Set STREAM_THINKING_AS_CONTENT=true for DeepSeek R1 style"
echo "• Run 'node examples/vision-demo.js' for comprehensive vision testing"
echo "• Run 'node examples/thinking-demo.js' for comprehensive thinking testing"
echo "• Use base64 images for more reliable vision processing"
echo ""

echo -e "${GREEN}🎉 New features test completed!${NC}"