#!/bin/bash

# OpenRouter endpoint và token
ENDPOINT="https://kilocode.ai/api/openrouter"
TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJlbnYiOiJwcm9kdWN0aW9uIiwia2lsb1VzZXJJZCI6Im9hdXRoL2dvb2dsZToxMDQ1NDQ0ODE2OTcwNjE3NjYzMjMiLCJ2ZXJzaW9uIjozLCJpYXQiOjE3NTIxMzQ3NjUsImV4cCI6MTkwOTkyMjc2NX0.sqf9x6009QIkTubD4l2tWM1ruCemIa7ZK_vfu3y14a8"
MODEL="anthropic/claude-sonnet-4"

echo "🔍 Testing Claude Sonnet 4 với curl..."
echo "📡 Endpoint: $ENDPOINT"
echo "🤖 Model: $MODEL"
echo "🔑 Token: ${TOKEN:0:20}...${TOKEN: -10}"
echo ""

# Test 1: L<PERSON>y danh sách models để kiểm tra Claude Sonnet 4 có available không
echo "📋 Test 1: Kiểm tra danh sách models..."
echo "curl -X GET \"$ENDPOINT/models\" \\"
echo "  -H \"Authorization: Bearer $TOKEN\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -H \"HTTP-Referer: https://kilocode.ai\" \\"
echo "  -H \"X-Title: Gemini CLI Wrapper\""
echo ""

curl -X GET "$ENDPOINT/models" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -H "HTTP-Referer: https://kilocode.ai" \
  -H "X-Title: Gemini CLI Wrapper" \
  -s | jq '.data[] | select(.id | contains("claude")) | {id: .id, name: .name, context_length: .context_length}'

echo ""
echo "=================================="
echo ""

# Test 2: Test chat completion với Claude Sonnet 4
echo "💬 Test 2: Test chat completion với Claude Sonnet 4..."
echo "curl -X POST \"$ENDPOINT/chat/completions\" \\"
echo "  -H \"Authorization: Bearer $TOKEN\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -H \"HTTP-Referer: https://kilocode.ai\" \\"
echo "  -H \"X-Title: Gemini CLI Wrapper\" \\"
echo "  -d '{...}'"
echo ""

curl -X POST "$ENDPOINT/chat/completions" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -H "HTTP-Referer: https://kilocode.ai" \
  -H "X-Title: Gemini CLI Wrapper" \
  -d '{
    "model": "'$MODEL'",
    "messages": [
      {
        "role": "user",
        "content": "Hello! Please respond with a brief greeting and confirm you are Claude Sonnet 4. Also mention your context window size if you know it."
      }
    ],
    "max_tokens": 150,
    "temperature": 0.7
  }' \
  -s | jq '.'

echo ""
echo "=================================="
echo ""

# Test 3: Test streaming response
echo "🌊 Test 3: Test streaming response..."
echo "curl -X POST \"$ENDPOINT/chat/completions\" \\"
echo "  -H \"Authorization: Bearer $TOKEN\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -H \"HTTP-Referer: https://kilocode.ai\" \\"
echo "  -H \"X-Title: Gemini CLI Wrapper\" \\"
echo "  -d '{...stream: true}'"
echo ""

curl -X POST "$ENDPOINT/chat/completions" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -H "HTTP-Referer: https://kilocode.ai" \
  -H "X-Title: Gemini CLI Wrapper" \
  -d '{
    "model": "'$MODEL'",
    "messages": [
      {
        "role": "user",
        "content": "Count from 1 to 3, explaining each number briefly."
      }
    ],
    "max_tokens": 200,
    "temperature": 0.7,
    "stream": true
  }' \
  -N

echo ""
echo "🏁 Tests completed!"