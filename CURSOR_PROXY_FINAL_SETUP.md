# 🚀 Cursor Proxy Final Setup Guide

## Problem Solved
Cursor cannot access `localhost` directly due to security restrictions ("Access to private networks is forbidden"). We need to expose the proxy server publicly.

## Current Status
✅ Proxy server is running on `localhost:3001`  
✅ Direct API tests with `curl` work perfectly  
❌ Cursor cannot connect to `localhost:3001`  
✅ **Solution**: Public tunnel to expose the proxy

## 🔧 Quick Setup (Choose One Method)

### Method 1: ngrok (Recommended)
```bash
# Run this in a new terminal
./setup_public_proxy.sh
```

### Method 2: Cloudflare Tunnel (Free Alternative)
```bash
# Run this in a new terminal
./setup_cloudflare_tunnel.sh
```

## 📋 Step-by-Step Instructions

### 1. Keep Proxy Server Running
Your proxy server should already be running:
```bash
# Terminal 1 (keep this running)
node cursor_proxy_server.cjs
```

### 2. Start Public Tunnel
Open a **new terminal** and run one of these:

#### Option A: ngrok
```bash
./setup_public_proxy.sh
```
You'll get a URL like: `https://abc123.ngrok.io`

#### Option B: Cloudflare Tunnel
```bash
./setup_cloudflare_tunnel.sh
```
You'll get a URL like: `https://abc-def-ghi.trycloudflare.com`

### 3. Configure Cursor
1. Open Cursor Settings
2. Go to "Models" or "AI Provider"
3. Add Custom OpenAI API:
   - **Base URL**: `https://your-tunnel-url.com/v1` (replace with your actual tunnel URL)
   - **API Key**: Your JWT token (the one you provided)
   - **Model**: `anthropic/claude-sonnet-4`

### 4. Test in Cursor
Try asking Cursor a question - it should now use Claude 3.5 Sonnet via your OpenRouter endpoint!

## 🔍 Troubleshooting

### Check Proxy Health
```bash
curl https://your-tunnel-url.com/health
# Should return: {"status":"ok","timestamp":"..."}
```

### List Available Models
```bash
curl https://your-tunnel-url.com/v1/models \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Test Chat Completion
```bash
curl -X POST https://your-tunnel-url.com/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "model": "anthropic/claude-sonnet-4",
    "messages": [{"role": "user", "content": "Hello!"}],
    "max_tokens": 100
  }'
```

## 📝 Important Notes

1. **Keep Both Terminals Open**: 
   - Terminal 1: `node cursor_proxy_server.cjs`
   - Terminal 2: `./setup_public_proxy.sh` or `./setup_cloudflare_tunnel.sh`

2. **URL Changes**: The public URL changes each time you restart the tunnel

3. **Security**: The tunnel exposes your proxy publicly - only use for development

4. **Model Name**: Use `anthropic/claude-sonnet-4` in Cursor (it will actually use Claude 3.5 Sonnet)

## 🎯 Expected Result

Once configured, Cursor will:
- Send requests to your public tunnel URL
- Proxy will translate OpenAI format → OpenRouter format
- Use your JWT token to authenticate with `https://kilocode.ai/api/openrouter`
- Return Claude 3.5 Sonnet responses to Cursor

## 🆘 Need Help?

If you encounter issues:
1. Check both terminals are running
2. Verify the public URL is accessible
3. Test with `curl` commands above
4. Check Cursor's error messages for details

The proxy server logs all requests, so you can see what's happening in Terminal 1.