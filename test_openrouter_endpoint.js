#!/usr/bin/env node

const https = require('https');
const http = require('http');

// Your endpoint and token
const ENDPOINT = 'https://kilocode.ai/api/openrouter';
const TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJlbnYiOiJwcm9kdWN0aW9uIiwia2lsb1VzZXJJZCI6Im9hdXRoL2dvb2dsZToxMDQ1NDQ0ODE2OTcwNjE3NjYzMjMiLCJ2ZXJzaW9uIjozLCJpYXQiOjE3NTIxMzQ3NjUsImV4cCI6MTkwOTkyMjc2NX0.sqf9x6009QIkTubD4l2tWM1ruCemIa7ZK_vfu3y14a8';

console.log('🔍 Testing OpenRouter endpoint...');
console.log(`📡 Endpoint: ${ENDPOINT}`);
console.log(`🔑 Token: ${TOKEN.substring(0, 20)}...${TOKEN.substring(TOKEN.length - 10)}`);
console.log('');

// Test 1: Get available models
async function testGetModels() {
    console.log('📋 Test 1: Getting available models...');
    
    try {
        const response = await fetch(`${ENDPOINT}/models`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${TOKEN}`,
                'Content-Type': 'application/json',
                'HTTP-Referer': 'https://kilocode.ai',
                'X-Title': 'Gemini CLI Wrapper'
            }
        });

        console.log(`📊 Response Status: ${response.status} ${response.statusText}`);
        
        if (!response.ok) {
            const errorText = await response.text();
            console.log(`❌ Error Response: ${errorText}`);
            return null;
        }

        const data = await response.json();
        console.log(`✅ Models retrieved successfully!`);
        
        // Filter Claude models
        const claudeModels = data.data ? data.data.filter(model => 
            model.id.toLowerCase().includes('claude') || 
            model.name?.toLowerCase().includes('claude')
        ) : [];
        
        console.log(`\n🤖 Found ${claudeModels.length} Claude models:`);
        claudeModels.forEach((model, index) => {
            console.log(`${index + 1}. ${model.id}`);
            console.log(`   Name: ${model.name || 'N/A'}`);
            console.log(`   Context: ${model.context_length || 'N/A'} tokens`);
            console.log(`   Pricing: $${model.pricing?.prompt || 'N/A'} / $${model.pricing?.completion || 'N/A'}`);
            console.log('');
        });
        
        return claudeModels;
    } catch (error) {
        console.log(`❌ Error: ${error.message}`);
        return null;
    }
}

// Test 2: Test chat completion with Claude
async function testChatCompletion(claudeModel = 'anthropic/claude-3.5-sonnet') {
    console.log(`\n💬 Test 2: Testing chat completion with ${claudeModel}...`);
    
    try {
        const requestBody = {
            model: claudeModel,
            messages: [
                {
                    role: 'user',
                    content: 'Hello! Please respond with a brief greeting and confirm you are Claude.'
                }
            ],
            max_tokens: 100,
            temperature: 0.7
        };

        console.log(`📤 Request Body:`, JSON.stringify(requestBody, null, 2));

        const response = await fetch(`${ENDPOINT}/chat/completions`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${TOKEN}`,
                'Content-Type': 'application/json',
                'HTTP-Referer': 'https://kilocode.ai',
                'X-Title': 'Gemini CLI Wrapper'
            },
            body: JSON.stringify(requestBody)
        });

        console.log(`📊 Response Status: ${response.status} ${response.statusText}`);
        
        if (!response.ok) {
            const errorText = await response.text();
            console.log(`❌ Error Response: ${errorText}`);
            return false;
        }

        const data = await response.json();
        console.log(`✅ Chat completion successful!`);
        console.log(`🤖 Claude Response: ${data.choices?.[0]?.message?.content || 'No content'}`);
        console.log(`📊 Usage: ${JSON.stringify(data.usage, null, 2)}`);
        
        return true;
    } catch (error) {
        console.log(`❌ Error: ${error.message}`);
        return false;
    }
}

// Test 3: Test streaming completion
async function testStreamingCompletion(claudeModel = 'anthropic/claude-3.5-sonnet') {
    console.log(`\n🌊 Test 3: Testing streaming completion with ${claudeModel}...`);
    
    try {
        const requestBody = {
            model: claudeModel,
            messages: [
                {
                    role: 'user',
                    content: 'Count from 1 to 5, explaining each number briefly.'
                }
            ],
            max_tokens: 200,
            temperature: 0.7,
            stream: true
        };

        const response = await fetch(`${ENDPOINT}/chat/completions`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${TOKEN}`,
                'Content-Type': 'application/json',
                'HTTP-Referer': 'https://kilocode.ai',
                'X-Title': 'Gemini CLI Wrapper'
            },
            body: JSON.stringify(requestBody)
        });

        console.log(`📊 Response Status: ${response.status} ${response.statusText}`);
        
        if (!response.ok) {
            const errorText = await response.text();
            console.log(`❌ Error Response: ${errorText}`);
            return false;
        }

        console.log(`🌊 Streaming response:`);
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let fullContent = '';

        try {
            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                const chunk = decoder.decode(value, { stream: true });
                const lines = chunk.split('\n');

                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        const data = line.slice(6);
                        if (data === '[DONE]') {
                            console.log('\n✅ Streaming completed!');
                            return true;
                        }

                        try {
                            const parsed = JSON.parse(data);
                            if (parsed.choices?.[0]?.delta?.content) {
                                const content = parsed.choices[0].delta.content;
                                process.stdout.write(content);
                                fullContent += content;
                            }
                        } catch (parseError) {
                            // Ignore parsing errors for streaming data
                        }
                    }
                }
            }
        } finally {
            reader.releaseLock();
        }

        console.log(`\n📝 Full streamed content: ${fullContent}`);
        return true;
    } catch (error) {
        console.log(`❌ Error: ${error.message}`);
        return false;
    }
}

// Main test function
async function runTests() {
    console.log('🚀 Starting OpenRouter endpoint tests...\n');
    
    // Test 1: Get models
    const claudeModels = await testGetModels();
    
    if (!claudeModels || claudeModels.length === 0) {
        console.log('❌ No Claude models found or API error. Cannot proceed with further tests.');
        return;
    }
    
    // Use the first available Claude model for testing
    const testModel = claudeModels[0].id;
    console.log(`\n🎯 Using model for testing: ${testModel}`);
    
    // Test 2: Chat completion
    const chatSuccess = await testChatCompletion(testModel);
    
    // Test 3: Streaming (only if chat completion worked)
    if (chatSuccess) {
        await testStreamingCompletion(testModel);
    }
    
    console.log('\n🏁 Tests completed!');
    
    // Summary
    console.log('\n📋 SUMMARY:');
    console.log('='.repeat(50));
    console.log(`✅ Endpoint: ${ENDPOINT}`);
    console.log(`✅ Authentication: Working`);
    console.log(`✅ Claude Models Available: ${claudeModels.length}`);
    console.log(`✅ Chat Completion: ${chatSuccess ? 'Working' : 'Failed'}`);
    console.log('\n🤖 Available Claude Models:');
    claudeModels.forEach(model => {
        console.log(`   - ${model.id}`);
    });
}

// Add fetch polyfill for Node.js if needed
if (typeof fetch === 'undefined') {
    const { default: fetch } = await import('node-fetch');
    global.fetch = fetch;
}

// Run the tests
runTests().catch(console.error);