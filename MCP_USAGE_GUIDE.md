# 🔧 MCP Server Usage Guide - Hướng dẫn sử dụng MCP Server

## 📋 Tổng quan

Gemini CLI Wrapper có **hỗ trợ đầy đủ MCP (Model Context Protocol)** với khả năng:
- **MCP Client**: Kế<PERSON> nối với external MCP servers
- **MCP Server**: Cung cấp tools cho external clients
- **Tool Registry**: Quản lý tools theo chuẩn MCP
- **Session Management**: Quản lý context giữ<PERSON> cá<PERSON> requests

## 🎯 Cách LLM sử dụng MCP Server

### 1. **Automatic Tool Detection**
LLM có thể tự động sử dụng tools mà không cần khai báo explicit:

```javascript
// LLM tự động detect và sử dụng tools
const response = await fetch('/chat/completions', {
  method: 'POST',
  headers: { 'Authorization': 'Bearer your-key' },
  body: JSON.stringify({
    model: 'gemini-2.5-pro',
    messages: [
      {
        role: 'user', 
        content: 'Search for latest AI news and analyze the package.json file'
      }
    ]
  })
});
```

### 2. **Explicit Tool Calling (OpenAI Style)**
LLM có thể sử dụng tools theo format OpenAI function calling:

```javascript
const response = await fetch('/chat/completions', {
  method: 'POST',
  headers: { 'Authorization': 'Bearer your-key' },
  body: JSON.stringify({
    model: 'gemini-2.5-pro',
    messages: [
      { role: 'user', content: 'Search for TypeScript best practices' }
    ],
    tools: [
      {
        type: 'function',
        function: {
          name: 'web_search',
          description: 'Search the web for current information',
          parameters: {
            type: 'object',
            properties: {
              query: { type: 'string' },
              max_results: { type: 'number', default: 5 }
            },
            required: ['query']
          }
        }
      }
    ]
  })
});
```

### 3. **Direct Tool Execution**
LLM có thể gọi tools trực tiếp qua MCP endpoints:

```javascript
// Execute tool directly
const toolResult = await fetch('/mcp/tools/execute', {
  method: 'POST',
  headers: { 'Authorization': 'Bearer your-key' },
  body: JSON.stringify({
    tool_name: 'web_search',
    arguments: {
      query: 'Node.js performance optimization',
      max_results: 3
    },
    session_id: 'my-session'
  })
});
```

## 🛠️ Available Tools

### Built-in Tools:

1. **web_search** - Tìm kiếm web
   ```json
   {
     "tool_name": "web_search",
     "arguments": {
       "query": "search query",
       "max_results": 5
     }
   }
   ```

2. **read_file** - Đọc file
   ```json
   {
     "tool_name": "read_file", 
     "arguments": {
       "path": "package.json",
       "encoding": "utf8"
     }
   }
   ```

3. **write_file** - Ghi file
   ```json
   {
     "tool_name": "write_file",
     "arguments": {
       "path": "output.txt",
       "content": "file content",
       "encoding": "utf8"
     }
   }
   ```

4. **execute_command** - Thực thi lệnh
   ```json
   {
     "tool_name": "execute_command",
     "arguments": {
       "command": "ls -la",
       "timeout": 30000
     }
   }
   ```

5. **analyze_code** - Phân tích code
   ```json
   {
     "tool_name": "analyze_code",
     "arguments": {
       "path": "src/",
       "analysis_type": "structure"
     }
   }
   ```

6. **store_memory** - Lưu trữ memory
   ```json
   {
     "tool_name": "store_memory",
     "arguments": {
       "key": "important_info",
       "value": "data to remember",
       "ttl": 3600
     }
   }
   ```

## 📡 MCP Endpoints

### Information Endpoints:
- `GET /mcp/info` - Thông tin MCP server
- `GET /mcp/tools` - Danh sách tools có sẵn
- `GET /mcp/sessions` - Danh sách sessions
- `GET /mcp/sessions/{id}` - Chi tiết session

### Execution Endpoints:
- `POST /mcp/tools/execute` - Thực thi tool trực tiếp
- `DELETE /mcp/sessions/{id}` - Xóa session

## 🔄 Session Management

### Tạo và sử dụng session:
```javascript
// Sử dụng session trong chat
const response = await fetch('/chat/completions', {
  method: 'POST',
  body: JSON.stringify({
    model: 'gemini-2.5-pro',
    messages: [{ role: 'user', content: 'Hello' }],
    session_id: 'my-persistent-session'
  })
});

// Tool execution với session
const toolResult = await fetch('/mcp/tools/execute', {
  method: 'POST',
  body: JSON.stringify({
    tool_name: 'store_memory',
    arguments: { key: 'user_pref', value: 'dark_mode' },
    session_id: 'my-persistent-session'
  })
});
```

## 🎯 Practical Examples cho LLM

### Example 1: Research và Analysis
```javascript
// LLM có thể làm như này:
const response = await fetch('/chat/completions', {
  method: 'POST',
  body: JSON.stringify({
    model: 'gemini-2.5-pro',
    messages: [{
      role: 'user',
      content: `
        Please help me research and analyze:
        1. Search for latest React 18 features
        2. Read our package.json to see current React version  
        3. Analyze our src/ directory structure
        4. Store findings in memory for later reference
      `
    }],
    session_id: 'research-session'
  })
});
```

### Example 2: Development Workflow
```javascript
// LLM có thể tự động:
const response = await fetch('/chat/completions', {
  method: 'POST', 
  body: JSON.stringify({
    model: 'gemini-2.5-pro',
    messages: [{
      role: 'user',
      content: `
        I want to optimize this project:
        1. Analyze the current codebase structure
        2. Check package.json for outdated dependencies
        3. Search for best practices for our tech stack
        4. Generate optimization recommendations
      `
    }],
    session_id: 'optimization-session'
  })
});
```

### Example 3: Streaming với Tools
```javascript
// Streaming response với tool usage
const response = await fetch('/chat/completions', {
  method: 'POST',
  body: JSON.stringify({
    model: 'gemini-2.5-pro',
    messages: [{
      role: 'user',
      content: 'Search for Node.js security best practices and create a summary file'
    }],
    stream: true,
    session_id: 'security-audit'
  })
});
```

## 🔒 Security Modes

### Restricted Mode (Default):
- File access limited to project directory
- Safe commands only: `ls`, `pwd`, `cat`, `grep`, etc.
- No system-level operations

### Unrestricted Mode:
```bash
# Enable unrestricted mode
export GEMINI_UNRESTRICTED_MODE=true
```
- Full file system access
- Any command execution
- ⚠️ Use with caution!

## 🧪 Testing MCP Features

### Quick Test:
```bash
# Test basic MCP functionality
node examples/simple-mcp-test.js

# Comprehensive MCP demo
node examples/mcp-demo.js

# Tools demonstration
node examples/tools-demo.js
```

### Manual Testing:
```bash
# Check MCP server info
curl http://localhost:8010/mcp/info

# List available tools
curl http://localhost:8010/mcp/tools

# Execute a tool
curl -X POST http://localhost:8010/mcp/tools/execute \
  -H "Content-Type: application/json" \
  -d '{
    "tool_name": "web_search",
    "arguments": {"query": "AI news"},
    "session_id": "test"
  }'
```

## 💡 Tips cho LLM

### 1. **Natural Language Tool Usage**
LLM có thể sử dụng tools bằng natural language:
- "Search for..." → tự động trigger `web_search`
- "Read file..." → tự động trigger `read_file`  
- "Execute command..." → tự động trigger `execute_command`

### 2. **Tool Chaining**
LLM có thể chain multiple tools:
```
User: "Research React hooks, save findings, then analyze our components"
→ web_search → store_memory → analyze_code
```

### 3. **Session Persistence**
LLM có thể maintain context across requests:
```javascript
// Request 1: Store information
{ session_id: "work-session", content: "Store user preferences" }

// Request 2: Use stored information  
{ session_id: "work-session", content: "Use my stored preferences" }
```

### 4. **Error Handling**
LLM sẽ receive detailed error information:
```json
{
  "success": false,
  "error": "Tool 'invalid_tool' not found",
  "available_tools": ["web_search", "read_file", ...]
}
```

## 🎉 Kết luận

**LLM có thể sử dụng MCP server một cách tự nhiên và mạnh mẽ!**

- ✅ **Automatic tool detection** từ natural language
- ✅ **OpenAI-compatible function calling**
- ✅ **Direct tool execution** qua MCP endpoints
- ✅ **Session management** cho context persistence
- ✅ **Streaming support** với tool integration
- ✅ **Security controls** với restricted/unrestricted modes

**Không cần configuration phức tạp - chỉ cần sử dụng API như bình thường!**