# Gemini CLI Wrapper

Wrapper API cho Google Gemini CLI, cung cấp giao diện RESTful API tương thích với OpenAI cho các ứng dụng client.

## Tính năng

### 🔥 Tính năng cốt lõi
- API endpoints tương thích với OpenAI
- Hỗ trợ nhiều models Gemini (1.5 Pro, 1.5 Flash, 2.5 Pro, 2.0 Flash)
- Streaming responses với real-time output
- <PERSON><PERSON> calling thông qua MCP (Model Context Protocol)
- Session management và context persistence
- Xác thực đa phương thức (OAuth + API Key)
- Tự động khởi động lại khi crash
- Tương thích với Open WebUI và các client OpenAI khác

### 🆕 Tính năng mới (v2.0)
- **🖼️ Vision Support**: Xử lý hình ảnh với base64 và URL
- **🧠 Real Thinking (Native)**: Hỗ trợ `include_reasoning`, `thinking_budget`, `reasoning_effort`
- **🧠 ~~R1 Style Thinking~~**: ~~<PERSON><PERSON><PERSON> thị quá trình suy nghĩ với `<thinking>` tags~~ (fake thinking – không khuyến nghị)
- **🔄 Smart Token Caching**: Quản lý OAuth token thông minh
- **🏷️ DeepSeek R1 Compatible**: Tương thích với LiteLLM và các tool khác
- **📡 Enhanced Streaming**: Streaming responses
- **🔧 Debug Endpoints**: Công cụ debug và monitoring

### 🛠️ Công cụ MCP mới (July 2025)
- **📁 list_directory**: Liệt kê files và thư mục với filtering
- **🔍 search_file_content**: Tìm kiếm nội dung trong files bằng regex
- **🌐 glob**: Tìm files theo glob patterns với sorting
- **📡 web_fetch**: Lấy nội dung từ URLs với processing instructions
- **📚 read_many_files**: Đọc nhiều files cùng lúc với metadata

> **Xem chi tiết**: [NEW_GEMINI_CLI_TOOLS.md](./NEW_GEMINI_CLI_TOOLS.md)

### 🚀 Phase 1 Improvements (July 2025)
- **🧠 Real Thinking Support**: Native Gemini reasoning thay thế fake thinking
- **🛡️ Content Safety Settings**: Configurable moderation controls
- **⚡ Enhanced Token Management**: Improved caching với retry logic
- **🔧 Debug Endpoints**: Monitoring và testing tools

> **Xem chi tiết**: [PHASE1_IMPROVEMENTS.md](./PHASE1_IMPROVEMENTS.md)

## Cài đặt

### Yêu cầu

- Node.js v20+
- PM2 (để quản lý process)

### Bước 1: Clone repository

```bash
git clone <repository-url>
cd gemini-cli-wrapper
```

### Bước 2: Cài đặt dependencies

```bash
npm install
```

### Bước 3: Cài đặt PM2

```bash
sudo npm install -g pm2
```

### Bước 4: Cấu hình

Tạo file `.env` với nội dung:

```
API_KEY=your_secret_api_key
PORT=8010
GEMINI_USE_OAUTH=false
DEFAULT_MODEL=gemini-2.5-pro

# Real Thinking (khuyến nghị)
ENABLE_REAL_THINKING=true
DEFAULT_THINKING_BUDGET=-1

# R1 Style Thinking (fake – không khuyến nghị)
ENABLE_FAKE_THINKING=false
STREAM_THINKING_AS_CONTENT=false
```

### Bước 5: Khởi động server

```bash
pm2 start ecosystem.config.cjs
```

Hoặc sử dụng script khởi động:

```bash
./start.sh
```

## Sử dụng

Xem file [API_USAGE.md](./API_USAGE.md) để biết chi tiết về cách sử dụng API.

### Kiểm tra API

Chạy script test để kiểm tra API:

```bash
./test_api.sh
```

### Quản lý server

- Xem trạng thái: `pm2 status`
- Xem logs: `pm2 logs`
- Khởi động lại: `pm2 restart all`
- Dừng server: `pm2 stop all`
- Xóa khỏi PM2: `pm2 delete all`

## Endpoints tương thích OpenAI

API này cung cấp các endpoints tương thích với OpenAI để dễ dàng tích hợp với các công cụ như Open WebUI:

### 🔄 Core Endpoints
- `GET /v1/models`: Lấy danh sách models với thông tin capabilities
- `POST /v1/chat/completions`: Chat completion với vision và real thinking support

### 🆕 Enhanced Endpoints
- `POST /v1/vision/test`: Test vision capabilities với hình ảnh
- ~~`POST /v1/thinking/test`~~: ~~Test thinking models với reasoning~~ (TẮT)
- `GET /v1/debug/cache`: Kiểm tra token cache status
- `POST /v1/token/import`: Import OAuth credentials từ Gemini CLI
- `DELETE /v1/token/clear`: Xóa tất cả cached tokens

### 🔧 Phase 1 Debug Endpoints
- `GET /v1/debug/real-thinking`: Real thinking handler configuration
- `GET /v1/debug/content-safety`: Content safety settings và thresholds
- `GET /v1/debug/token-cache`: Enhanced token cache statistics
- `POST /v1/debug/test-real-thinking`: Test native Gemini reasoning
- `POST /v1/debug/test-content-safety`: Test content safety filtering

### Model Capabilities
Mỗi model trong `/v1/models` endpoint bao gồm thông tin:
```json
{
  "id": "gemini-2.5-pro",
  "capabilities": {
    "vision": true,
  "thinking": true,
    "max_tokens": 1000000
  }
}
```

## Kết nối với Open WebUI

Để kết nối Gemini CLI Wrapper với Open WebUI:

1. Mở Open WebUI và đăng nhập
2. Vào Settings > Connections
3. Click "Add Connection"
4. Cấu hình như sau:
   - Connection Type: Local
   - URL: `http://YOUR_SERVER_IP:8010`
   - Key: (để trống nếu không có API key, hoặc nhập API key nếu đã cấu hình)
   - Prefix ID: (để trống)
   - Model IDs: (để trống để lấy tất cả models từ endpoint `/v1/models`)
5. Click "Save"

## Cấu trúc thư mục

- `src/index.js`: Entry point của application
- `src/providers/`: Providers cho models và tính năng nâng cao
  - `geminiProvider.js`: Core Gemini integration
  - `visionHandler.js`: 🆕 Vision/multimodal processing
  - `thinkingHandler.js`: ~~🆕 Thinking models support~~ (TẮT)
  - `tokenCache.js`: 🆕 Smart OAuth token management
- `src/middleware/`: Authentication và error handling middleware
- `src/utils/`: Utilities như logging
- `src/mcp/`: Model Context Protocol handler
- `src/tools/`: Tool registry và các tools
- `examples/`: Demo scripts cho các tính năng
  - `vision-demo.js`: 🆕 Vision capabilities demo
  - `thinking-demo.js`: ~~🆕 Thinking models demo~~ (TẮT)
- `memory-bank/`: Documentation và context files

## Troubleshooting

### Port đã được sử dụng

Nếu gặp lỗi "EADDRINUSE", hãy dừng tiến trình đang sử dụng port:

```bash
sudo lsof -i :8010
sudo kill -9 <PID>
```

### Server crash liên tục

Kiểm tra logs để xem lỗi:

```bash
pm2 logs
```

### Lỗi "OpenAI: Network Problem" trong Open WebUI

Nếu gặp lỗi này khi kết nối với Open WebUI, hãy kiểm tra:
- Server Gemini CLI Wrapper đang chạy
- IP và port chính xác
- Không có firewall nào chặn kết nối
- CORS được cấu hình đúng

## Đóng góp

Vui lòng gửi pull requests hoặc mở issues để đóng góp vào dự án.

## Giấy phép

ISC 