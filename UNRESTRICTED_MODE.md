# Unrestricted Mode - Hướng Dẫn Sử Dụng

## ⚠️ CẢNH BÁO BẢO MẬT

**Unrestricted Mode cho phép AI truy cập TOÀN BỘ hệ thống của bạn!**

- 🚨 AI có thể đọc/ghi bất kỳ file nào trên server
- 🚨 AI có thể thực thi bất kỳ lệnh nào (bao gồm `sudo`, `rm -rf /`, etc.)
- 🚨 AI có thể truy cập database, API keys, SSH keys, và các thông tin nhạy cảm khác

**CHỈ SỬ DỤNG TRONG MÔI TRƯỜNG AN TOÀN VÀ TIN CẬY!**

## Tại Sao Cần Unrestricted Mode?

### Giới Hạn Của Restricted Mode (Mặc Định)
- Chỉ có thể đọc/ghi file trong thư mục dự án
- Chỉ có thể chạy một số lệnh an toàn: `ls`, `pwd`, `cat`, `grep`, etc.
- Không thể truy cập các thư mục hệ thống
- Không thể cài đặt phần mềm hoặc thay đổi cấu hình hệ thống

### Kh<PERSON> Năng Của Unrestricted Mode
- ✅ Đọc/ghi file ở bất kỳ đâu: `/etc/`, `/home/<USER>/var/`, etc.
- ✅ Thực thi mọi lệnh: `sudo`, `apt install`, `systemctl`, `docker`, etc.
- ✅ Quản lý hệ thống: cài đặt phần mềm, cấu hình services
- ✅ Truy cập network: `curl`, `wget`, `ssh`, etc.
- ✅ Quản lý processes: `kill`, `ps`, `top`, etc.

## Cách Bật Unrestricted Mode

### Bước 1: Cấu Hình Environment Variable

Thêm vào file `.env`:
```bash
GEMINI_UNRESTRICTED_MODE=true
```

### Bước 2: Khởi Động Lại Server

```bash
npm start
```

Bạn sẽ thấy log cảnh báo:
```
🚨 UNRESTRICTED MODE ENABLED - Full system access allowed
```

### Bước 3: Test Tính Năng

```bash
chmod +x test_unrestricted_mode.sh
./test_unrestricted_mode.sh
```

## Ví Dụ Sử Dụng

### 1. Đọc File Hệ Thống

**Restricted Mode (FAIL):**
```json
{
  "tool_name": "read_file",
  "arguments": {
    "path": "/etc/passwd"
  }
}
```
→ Error: "Access denied: File outside project directory"

**Unrestricted Mode (SUCCESS):**
```json
{
  "tool_name": "read_file",
  "arguments": {
    "path": "/etc/passwd"
  }
}
```
→ Trả về nội dung file `/etc/passwd`

### 2. Thực Thi Lệnh Hệ Thống

**Restricted Mode (FAIL):**
```json
{
  "tool_name": "execute_command",
  "arguments": {
    "command": "sudo apt update"
  }
}
```
→ Error: "Command 'sudo' not allowed for security reasons"

**Unrestricted Mode (SUCCESS):**
```json
{
  "tool_name": "execute_command",
  "arguments": {
    "command": "sudo apt update"
  }
}
```
→ Thực thi lệnh và trả về kết quả

### 3. Quản Lý File Toàn Hệ Thống

```json
{
  "tool_name": "write_file",
  "arguments": {
    "path": "/tmp/test_file.txt",
    "content": "Hello from AI!"
  }
}
```

### 4. Cài Đặt Phần Mềm

```json
{
  "tool_name": "execute_command",
  "arguments": {
    "command": "sudo apt install -y htop",
    "timeout": 60000
  }
}
```

## Các Trường Hợp Sử Dụng Phù Hợp

### ✅ An Toàn
- **Docker Container**: Chạy trong container isolated
- **Virtual Machine**: VM riêng biệt cho testing
- **Development Environment**: Máy dev cá nhân
- **Sandbox Environment**: Môi trường test được cách ly

### ❌ Không An Toàn
- **Production Server**: Server production với data thật
- **Shared Server**: Server có nhiều user khác
- **Server có thông tin nhạy cảm**: Database, API keys, certificates
- **Public Server**: Server có thể truy cập từ internet

## Cách Bảo Vệ Khi Sử Dụng

### 1. Sử Dụng Docker
```dockerfile
FROM node:20-alpine
WORKDIR /app
COPY . .
RUN npm install
# Tạo user non-root
RUN addgroup -g 1001 -S nodejs
RUN adduser -S gemini -u 1001
USER gemini
EXPOSE 8010
CMD ["npm", "start"]
```

### 2. Giới Hạn Network Access
```bash
# Chặn outbound connections
iptables -A OUTPUT -p tcp --dport 80 -j DROP
iptables -A OUTPUT -p tcp --dport 443 -j DROP
```

### 3. Monitoring và Logging
```bash
# Monitor file access
auditctl -w /etc -p wa -k config_changes
auditctl -w /home -p wa -k home_access

# Monitor command execution
export PROMPT_COMMAND='history -a; logger -p local0.info "$(whoami) [$$]: $(history 1)"'
```

### 4. Backup Trước Khi Sử Dụng
```bash
# Backup important files
tar -czf backup_$(date +%Y%m%d).tar.gz /etc /home/<USER>/.ssh
```

## Tắt Unrestricted Mode

### Cách 1: Environment Variable
```bash
GEMINI_UNRESTRICTED_MODE=false
```

### Cách 2: Xóa Hoàn Toàn
Xóa dòng `GEMINI_UNRESTRICTED_MODE` khỏi file `.env`

### Cách 3: Override Tạm Thời
```bash
GEMINI_UNRESTRICTED_MODE=false npm start
```

## Troubleshooting

### Lỗi Permission Denied
```bash
# Kiểm tra quyền của user chạy Node.js
whoami
groups

# Thêm user vào group cần thiết
sudo usermod -aG sudo nodejs_user
```

### Lỗi Command Not Found
```bash
# Kiểm tra PATH
echo $PATH

# Thêm path nếu cần
export PATH=$PATH:/usr/local/bin:/usr/bin:/bin
```

### Log Debugging
```bash
# Bật debug logging
LOG_LEVEL=debug npm start
```

## Kết Luận

Unrestricted Mode là công cụ mạnh mẽ cho phép AI có quyền truy cập toàn bộ hệ thống. Sử dụng cẩn thận và chỉ trong môi trường an toàn!

**Nhớ rằng: "With great power comes great responsibility"** 🕷️