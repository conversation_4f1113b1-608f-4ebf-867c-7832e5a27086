FROM node:18-alpine

# Install system dependencies
RUN apk add --no-cache curl bash

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Install Gemini CLI globally
RUN npm install -g @google/gemini-cli

# Copy source code
COPY src/ ./src/

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S gemini -u 1001

# Change ownership
RUN chown -R gemini:nodejs /app
USER gemini

# Expose port
EXPOSE 8010

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
  CMD curl -f http://localhost:8010/health || exit 1

# Start application
CMD ["node", "src/index.js"] 