# Open WebUI Integration Guide

## Overview

The Gemini CLI Wrapper provides comprehensive integration with Open WebUI through 7 specialized tools that allow the LLM to interact with Open WebUI's API and interface directly.

## 🔧 Configuration Required

### 1. Environment Variables
Set these environment variables for proper authentication:

```bash
# Open WebUI API Key (required for API access)
export OPEN_WEBUI_API_KEY="your-openwebui-api-key"

# Open WebUI Base URL (default: http://localhost:3000)
export OPEN_WEBUI_BASE_URL="http://localhost:3000"

# Enable browser automation for UI interactions
export ENABLE_BROWSER_AUTOMATION=true
```

### 2. Getting Open WebUI API Key
1. Open your Open WebUI instance
2. Go to Settings → Account → API Keys
3. Generate a new API key
4. Copy and set as `OPEN_WEBUI_API_KEY`

## 🛠️ Available Tools

### 1. `openwebui_web_search` - Web Search
Search the web using Open WebUI's built-in web search functionality.

**Parameters:**
- `query` (required): Search query
- `max_results` (optional): Maximum results (default: 5)

**Example:**
```javascript
{
  "tool": "openwebui_web_search",
  "args": {
    "query": "latest AI developments",
    "max_results": 10
  }
}
```

### 2. `openwebui_get_models` - Get Available Models
Retrieve list of available models in Open WebUI.

**Example:**
```javascript
{
  "tool": "openwebui_get_models",
  "args": {}
}
```

### 3. `openwebui_create_chat` - Create New Chat
Create a new chat session in Open WebUI.

**Parameters:**
- `title` (optional): Chat title
- `model` (optional): Model to use (default: gemini-2.5-flash)

**Example:**
```javascript
{
  "tool": "openwebui_create_chat",
  "args": {
    "title": "AI Research Discussion",
    "model": "gemini-2.5-pro"
  }
}
```

### 4. `openwebui_send_message` - Send Message (Cross-Session Only)
Send a message to a different Open WebUI chat session. **Enhanced with circular loop prevention.**

**Parameters:**
- `message` (required): Message content
- `chat_id` (required): Target chat ID (must be different from current session)
- `model` (optional): Model to use (default: gemini-2.5-flash)

**Safety Features:**
- ✅ Prevents circular loops by checking current session ID
- ✅ Requires explicit chat_id to avoid accidental loops
- ✅ Provides clear error messages for safety violations
- ✅ Logs cross-session operations for monitoring

**Example:**
```javascript
{
  "tool": "openwebui_send_message",
  "args": {
    "message": "Explain quantum computing",
    "chat_id": "different-chat-123",
    "model": "gemini-2.5-pro"
  }
}
```

**Error Types:**
- `CIRCULAR_REFERENCE_PREVENTED`: Attempted to send to current session
- `CHAT_ID_REQUIRED`: No chat_id provided (required for safety)
- Standard API errors with session context

### 5. `openwebui_get_conversations` - Get Conversations
Retrieve list of conversations from Open WebUI.

**Parameters:**
- `limit` (optional): Number of conversations (default: 10)

**Example:**
```javascript
{
  "tool": "openwebui_get_conversations",
  "args": {
    "limit": 20
  }
}
```

### 6. `openwebui_upload_file` - Upload File
Upload a file to Open WebUI for processing.

**Parameters:**
- `file_path` (required): Path to file
- `file_type` (optional): File type description

**Example:**
```javascript
{
  "tool": "openwebui_upload_file",
  "args": {
    "file_path": "./document.pdf",
    "file_type": "document"
  }
}
```

### 7. `openwebui_browser_action` - Browser Automation
Perform browser actions on Open WebUI interface using Puppeteer.

**Parameters:**
- `action` (required): Action type (click, type, scroll, screenshot, navigate)
- `selector` (optional): CSS selector
- `value` (optional): Value to type or URL

**Example:**
```javascript
{
  "tool": "openwebui_browser_action",
  "args": {
    "action": "screenshot"
  }
}
```

## 🎯 LLM Usage Patterns

### Automatic Tool Selection
The LLM can automatically choose appropriate Open WebUI tools based on user requests:

- **"Search Open WebUI for..."** → `openwebui_web_search`
- **"Create a new chat in Open WebUI..."** → `openwebui_create_chat`
- **"Send message to Open WebUI..."** → `openwebui_send_message`
- **"Get my Open WebUI conversations..."** → `openwebui_get_conversations`
- **"Upload file to Open WebUI..."** → `openwebui_upload_file`
- **"Take screenshot of Open WebUI..."** → `openwebui_browser_action`

### Tool Chaining Examples

#### 1. Complete Chat Workflow
```
1. openwebui_get_models → Check available models
2. openwebui_create_chat → Create new chat session
3. openwebui_send_message → Send initial message
4. openwebui_get_conversations → Verify chat was created
```

#### 2. Research and Documentation
```
1. openwebui_web_search → Search for information
2. openwebui_create_chat → Create research chat
3. openwebui_send_message → Send research findings
4. openwebui_upload_file → Upload related documents
```

#### 3. UI Interaction and Monitoring
```
1. openwebui_browser_action (screenshot) → Capture current state
2. openwebui_browser_action (click) → Interact with UI
3. openwebui_browser_action (screenshot) → Capture result
```

## 🚨 Troubleshooting

### Common Issues and Solutions

#### 1. "Request failed with status code 403"
**Problem:** Authentication failed
**Solution:** 
- Set `OPEN_WEBUI_API_KEY` environment variable
- Verify API key is valid and has proper permissions
- Check Open WebUI is running and accessible

#### 2. "Failed to get models/conversations"
**Problem:** API endpoint not available
**Solution:**
- Verify Open WebUI version supports API endpoints
- Check `OPEN_WEBUI_BASE_URL` is correct
- Ensure Open WebUI is running on specified port

#### 3. "Browser automation is disabled"
**Problem:** Browser actions not working
**Solution:**
- Set `ENABLE_BROWSER_AUTOMATION=true`
- Install Chromium: `sudo apt-get install chromium-browser`
- Verify Puppeteer is installed: `npm install puppeteer`

#### 4. "Connection refused"
**Problem:** Cannot connect to Open WebUI
**Solution:**
- Start Open WebUI: `docker run -d -p 3000:8080 ghcr.io/open-webui/open-webui:main`
- Check if port 3000 is available
- Verify firewall settings

## 🔒 Security Considerations

### Session Management & Circular Loop Prevention
- **Current Session Tracking**: Set `OPENWEBUI_CURRENT_SESSION_ID` environment variable to track current session
- **Cross-Session Safety**: `openwebui_send_message` requires explicit chat_id and prevents sending to current session
- **Loop Detection**: Automatic detection and prevention of circular message loops
- **Session Monitoring**: All cross-session operations are logged with session context

### API Key Management
- Store API keys securely in environment variables
- Never commit API keys to version control
- Rotate API keys regularly
- Use least-privilege access

### Network Security
- Use HTTPS in production environments
- Implement proper CORS settings
- Consider VPN for remote access
- Monitor API usage and access logs

### File Upload Security
- Validate file types and sizes
- Scan uploaded files for malware
- Implement proper access controls
- Use secure file storage locations

## 📊 Monitoring and Logging

### Available Logs
- Tool execution logs in console
- API request/response logging
- Browser automation activity
- Error tracking and debugging

### Performance Metrics
- API response times
- Tool success/failure rates
- Browser automation performance
- File upload statistics

## 🔄 Integration Workflows

### Development Workflow
```bash
# 1. Start Open WebUI
docker run -d -p 3000:8080 ghcr.io/open-webui/open-webui:main

# 2. Set environment variables
export OPEN_WEBUI_API_KEY="your-api-key"
export ENABLE_BROWSER_AUTOMATION=true

# 3. Start Gemini CLI Wrapper
npm start

# 4. Test integration
curl -X POST http://localhost:8010/mcp/tools/execute \
  -H "Content-Type: application/json" \
  -d '{"tool_name": "openwebui_get_models", "args": {}}'
```

### Production Deployment
1. Configure secure API key management
2. Set up proper monitoring and logging
3. Implement rate limiting and throttling
4. Configure backup and disaster recovery
5. Set up health checks and alerts

## 🎨 Advanced Usage

### Custom Model Integration
```javascript
// Get available models and create chat with specific model
const models = await openwebui_get_models({});
const chat = await openwebui_create_chat({
  title: "Custom Model Chat",
  model: models.models[0].id
});
```

### Batch Operations
```javascript
// Process multiple conversations
const conversations = await openwebui_get_conversations({ limit: 50 });
for (const conv of conversations.conversations) {
  await openwebui_send_message({
    message: "Summary request",
    chat_id: conv.id
  });
}
```

### File Processing Pipeline
```javascript
// Upload and process documents
const upload = await openwebui_upload_file({
  file_path: "./research.pdf"
});

const chat = await openwebui_create_chat({
  title: "Document Analysis"
});

await openwebui_send_message({
  message: `Analyze the uploaded document: ${upload.file_url}`,
  chat_id: chat.chat_id
});
```

This comprehensive integration allows the LLM to seamlessly interact with Open WebUI, providing users with powerful automation capabilities for chat management, web search, file processing, and UI interaction.