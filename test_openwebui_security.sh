#!/bin/bash

# Test OpenAI API Security Configuration
# This script tests the REQUIRE_OPENAI_AUTH functionality

echo "🔒 Testing OpenAI API Security Configuration"
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Server URL
SERVER_URL="http://localhost:8010"

# Function to test endpoint
test_endpoint() {
    local endpoint=$1
    local auth_header=$2
    local expected_status=$3
    local description=$4
    
    echo -n "Testing: $description... "
    
    if [ -n "$auth_header" ]; then
        response=$(curl -s -w "%{http_code}" -H "$auth_header" "$SERVER_URL$endpoint")
    else
        response=$(curl -s -w "%{http_code}" "$SERVER_URL$endpoint")
    fi
    
    status_code="${response: -3}"
    
    if [ "$status_code" = "$expected_status" ]; then
        echo -e "${GREEN}✅ PASS${NC} (Status: $status_code)"
        return 0
    else
        echo -e "${RED}❌ FAIL${NC} (Expected: $expected_status, Got: $status_code)"
        return 1
    fi
}

# Check if server is running
echo "🚀 Checking if server is running..."
if ! curl -s "$SERVER_URL/health" > /dev/null; then
    echo -e "${RED}❌ Server is not running at $SERVER_URL${NC}"
    echo "Please start the server with: npm start"
    exit 1
fi
echo -e "${GREEN}✅ Server is running${NC}"
echo

# Load API keys from environment or use defaults
if [ -f ".env" ]; then
    source .env
fi

ADMIN_KEY=${ADMIN_API_KEY:-"admin-secret-key"}
USER_KEY=${USER_API_KEY:-"user-api-key"}
INVALID_KEY="invalid-key-123"

echo "📋 Test Configuration:"
echo "- REQUIRE_OPENAI_AUTH: ${REQUIRE_OPENAI_AUTH:-'not set (defaults to false)'}"
echo "- Admin API Key: ${ADMIN_KEY:0:8}..."
echo "- User API Key: ${USER_KEY:0:8}..."
echo

# Test 1: Health endpoint (should always work)
echo "🏥 Testing Health Endpoint:"
test_endpoint "/health" "" "200" "Health check without auth"
echo

# Test 2: Check current REQUIRE_OPENAI_AUTH setting
echo "🔍 Checking OpenAI Auth Requirement:"
if [ "$REQUIRE_OPENAI_AUTH" = "true" ]; then
    echo -e "${YELLOW}🔒 OpenAI Auth is ENABLED${NC}"
    
    echo "📝 Testing OpenAI endpoints WITH auth requirement:"
    
    # Should fail without auth
    test_endpoint "/v1/models" "" "401" "GET /v1/models without auth (should fail)"
    test_endpoint "/v1/chat/completions" "" "401" "POST /v1/chat/completions without auth (should fail)"
    
    # Should succeed with valid auth
    test_endpoint "/v1/models" "Authorization: Bearer $USER_KEY" "200" "GET /v1/models with valid auth"
    
    # Test with invalid auth
    test_endpoint "/v1/models" "Authorization: Bearer $INVALID_KEY" "401" "GET /v1/models with invalid auth (should fail)"
    
else
    echo -e "${YELLOW}🔓 OpenAI Auth is DISABLED${NC}"
    
    echo "📝 Testing OpenAI endpoints WITHOUT auth requirement:"
    
    # Should succeed without auth
    test_endpoint "/v1/models" "" "200" "GET /v1/models without auth (should work)"
    
    # Should also succeed with auth
    test_endpoint "/v1/models" "Authorization: Bearer $USER_KEY" "200" "GET /v1/models with valid auth"
fi

echo

# Test 3: Protected endpoints (should always require auth)
echo "🛡️ Testing Protected Endpoints (always require auth):"
test_endpoint "/rbac/status" "" "401" "RBAC status without auth (should fail)"
test_endpoint "/rbac/status" "Authorization: Bearer $USER_KEY" "200" "RBAC status with valid auth"
test_endpoint "/mcp/tools" "" "401" "MCP tools without auth (should fail)"
test_endpoint "/mcp/tools" "Authorization: Bearer $USER_KEY" "200" "MCP tools with valid auth"
echo

# Test 4: Role-based access
echo "👥 Testing Role-based Access:"
test_endpoint "/rbac/policies" "Authorization: Bearer $USER_KEY" "403" "RBAC policies with user role (should fail - admin only)"
test_endpoint "/rbac/policies" "Authorization: Bearer $ADMIN_KEY" "200" "RBAC policies with admin role"
echo

# Summary
echo "📊 Test Summary:"
echo "================"

if [ "$REQUIRE_OPENAI_AUTH" = "true" ]; then
    echo -e "${GREEN}✅ OpenAI API Security is ENABLED${NC}"
    echo "   - Open WebUI will need a valid API key"
    echo "   - All /v1/* endpoints require authentication"
    echo "   - RBAC permissions are enforced"
else
    echo -e "${YELLOW}⚠️  OpenAI API Security is DISABLED${NC}"
    echo "   - Open WebUI can access without API key"
    echo "   - /v1/* endpoints are publicly accessible"
    echo "   - Consider setting REQUIRE_OPENAI_AUTH=true for production"
fi

echo
echo "🔧 To enable OpenAI API security:"
echo "   1. Add REQUIRE_OPENAI_AUTH=true to your .env file"
echo "   2. Restart the server"
echo "   3. Configure Open WebUI with a valid API key"
echo
echo "📚 For detailed setup instructions, see: OPEN_WEBUI_SECURE_SETUP.md"