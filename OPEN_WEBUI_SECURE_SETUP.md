# Open WebUI Secure Setup Guide

## Tổng quan

Hướng dẫn này sẽ gi<PERSON>p bạn thiết lập API key authentication cho Open WebUI để đảm bảo bảo mật khi truy cập Gemini CLI Wrapper.

## Bước 1: Bật Authentication cho OpenAI API Endpoints

### 1.1 Cấu hình Environment Variables

Thêm vào file `.env` của bạn:

```bash
# Bật authentication cho OpenAI API endpoints
REQUIRE_OPENAI_AUTH=true

# API key chính cho authentication
ADMIN_API_KEY=your-secure-admin-key-here
USER_API_KEY=your-secure-user-key-here
```

### 1.2 Tạo API Keys an toàn

Sử dụng script có sẵn để tạo API keys:

```bash
./setup_api_keys.sh
```

Script này sẽ:
- Tạo các API keys ngẫu nhiên và an toàn
- Cập nhật file `.env` 
- Tạo file `api_keys.txt` để tham khảo

## Bước 2: Cấu hình Open WebUI

### 2.1 Truy cập Open WebUI Settings

1. Mở Open WebUI trong browser: `http://localhost:3000`
2. Đi tới **Settings** → **Connections** → **OpenAI API**

### 2.2 Cấu hình API Connection

```
OpenAI API Base URL: http://localhost:8010/v1
OpenAI API Key: your-secure-user-key-here
```

**Lưu ý quan trọng:**
- Sử dụng API key từ file `api_keys.txt` hoặc `.env`
- Đảm bảo URL kết thúc bằng `/v1`
- Port phải khớp với PORT trong `.env` (mặc định 8010)

## Bước 3: Kiểm tra Kết nối

### 3.1 Test Authentication

```bash
# Test với API key hợp lệ
curl -H "Authorization: Bearer your-secure-user-key-here" \
     http://localhost:8010/v1/models

# Test không có API key (sẽ bị từ chối)
curl http://localhost:8010/v1/models
```

### 3.2 Test trong Open WebUI

1. Thử gửi một tin nhắn test
2. Kiểm tra logs của Gemini CLI Wrapper:
   ```bash
   npm start
   ```
3. Tìm log: `✅ API key authentication successful`

## Bước 4: Quản lý Roles và Permissions

### 4.1 Phân quyền theo Role

Bạn có thể gán role khác nhau cho các API keys:

```bash
# Trong .env
API_KEY_ROLE_MAPPINGS={"admin-key":"admin","user-key":"user","guest-key":"guest"}
```

### 4.2 Kiểm tra Permissions

```bash
# Kiểm tra role hiện tại
curl -H "Authorization: Bearer your-api-key" \
     http://localhost:8010/rbac/status
```

## Bước 5: Troubleshooting

### 5.1 Lỗi thường gặp

**401 Unauthorized:**
```json
{
  "error": "Unauthorized",
  "message": "Authentication required"
}
```
**Giải pháp:** Kiểm tra API key trong Open WebUI settings

**403 Access Denied:**
```json
{
  "error": "Access denied",
  "message": "Insufficient permissions"
}
```
**Giải pháp:** Sử dụng API key với role cao hơn (admin thay vì user)

### 5.2 Debug Steps

1. **Kiểm tra server logs:**
   ```bash
   tail -f logs/app.log
   ```

2. **Test API key trực tiếp:**
   ```bash
   ./demo_api_key_usage.sh
   ```

3. **Kiểm tra RBAC status:**
   ```bash
   curl -H "Authorization: Bearer your-api-key" \
        http://localhost:8010/rbac/status
   ```

## Bước 6: Production Deployment

### 6.1 Security Best Practices

1. **Sử dụng HTTPS trong production**
2. **Rotate API keys định kỳ**
3. **Monitor access logs**
4. **Sử dụng strong API keys (32+ characters)**

### 6.2 Environment Configuration

```bash
# Production settings
NODE_ENV=production
REQUIRE_OPENAI_AUTH=true
LOG_LEVEL=warn
```

## Tóm tắt

✅ **Đã hoàn thành:**
- Bật authentication cho OpenAI API endpoints
- Tạo secure API keys
- Cấu hình Open WebUI với API key
- Test kết nối và permissions

🔒 **Bảo mật được đảm bảo:**
- Tất cả requests đến `/v1/*` endpoints yêu cầu API key
- RBAC system kiểm soát permissions
- Timing-safe API key comparison
- Comprehensive access logging

**Kết quả:** Open WebUI giờ đây yêu cầu API key hợp lệ để truy cập Gemini models, đảm bảo bảo mật cho hệ thống của bạn.