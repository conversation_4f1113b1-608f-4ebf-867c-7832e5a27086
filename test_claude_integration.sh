#!/bin/bash

# Test Claude Integration Script
echo "🧪 Testing Claude Integration..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Server URL
SERVER_URL="http://localhost:8010"

# Function to test endpoint
test_endpoint() {
    local method=$1
    local endpoint=$2
    local data=$3
    local description=$4
    
    echo -e "${BLUE}Testing: ${description}${NC}"
    echo "Endpoint: ${method} ${endpoint}"
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "\n%{http_code}" "${SERVER_URL}${endpoint}")
    else
        response=$(curl -s -w "\n%{http_code}" -X "${method}" \
            -H "Content-Type: application/json" \
            -d "${data}" \
            "${SERVER_URL}${endpoint}")
    fi
    
    # Extract HTTP status code (last line)
    http_code=$(echo "$response" | tail -n1)
    # Extract response body (all but last line)
    body=$(echo "$response" | head -n -1)
    
    if [ "$http_code" -eq 200 ]; then
        echo -e "${GREEN}✅ Success (${http_code})${NC}"
        echo "Response: $body" | jq . 2>/dev/null || echo "Response: $body"
    else
        echo -e "${RED}❌ Failed (${http_code})${NC}"
        echo "Response: $body"
    fi
    echo "----------------------------------------"
}

# Wait for server to be ready
echo "⏳ Waiting for server to start..."
sleep 3

# Test 1: Health check
test_endpoint "GET" "/health" "" "Health Check"

# Test 2: Get all models (should include Claude models now)
test_endpoint "GET" "/v1/models" "" "Get Available Models (should include Claude models)"

# Test 3: Get status (should show both providers)
test_endpoint "GET" "/status" "" "Get Provider Status"

# Test 4: Test Claude 4 Sonnet completion
echo -e "${YELLOW}🤖 Testing Claude 4 Sonnet completion...${NC}"
claude_sonnet_data='{
  "model": "claude-4-sonnet",
  "messages": [
    {
      "role": "user", 
      "content": "Hello! Can you tell me what model you are and introduce yourself briefly?"
    }
  ],
  "stream": false
}'

test_endpoint "POST" "/v1/chat/completions" "$claude_sonnet_data" "Claude 4 Sonnet Completion"

# Test 5: Test Claude 4 Opus completion
echo -e "${YELLOW}🤖 Testing Claude 4 Opus completion...${NC}"
claude_opus_data='{
  "model": "claude-4-opus",
  "messages": [
    {
      "role": "user", 
      "content": "Hello! Can you tell me what model you are and solve this simple math: 15 + 27 = ?"
    }
  ],
  "stream": false
}'

test_endpoint "POST" "/v1/chat/completions" "$claude_opus_data" "Claude 4 Opus Completion"

# Test 6: Test streaming with Claude
echo -e "${YELLOW}🌊 Testing Claude streaming...${NC}"
claude_stream_data='{
  "model": "claude-4-sonnet",
  "messages": [
    {
      "role": "user", 
      "content": "Count from 1 to 5 and explain each number briefly."
    }
  ],
  "stream": true
}'

echo "Testing streaming endpoint..."
curl -s -X POST \
    -H "Content-Type: application/json" \
    -d "$claude_stream_data" \
    "${SERVER_URL}/v1/chat/completions" | head -20

echo -e "\n${GREEN}✅ Streaming test completed${NC}"
echo "----------------------------------------"

# Test 7: Test fallback between providers
echo -e "${YELLOW}🔄 Testing provider fallback...${NC}"
fallback_data='{
  "model": "non-existent-model",
  "messages": [
    {
      "role": "user", 
      "content": "This should fallback to a default model."
    }
  ],
  "stream": false
}'

test_endpoint "POST" "/v1/chat/completions" "$fallback_data" "Provider Fallback Test"

# Test 8: Test original Gemini models still work
echo -e "${YELLOW}🔮 Testing Gemini models still work...${NC}"
gemini_data='{
  "model": "gemini-2.5-pro",
  "messages": [
    {
      "role": "user", 
      "content": "Hello Gemini! Can you confirm you are working?"
    }
  ],
  "stream": false
}'

test_endpoint "POST" "/v1/chat/completions" "$gemini_data" "Gemini 2.5 Pro Completion"

echo -e "${GREEN}🎉 Claude Integration Testing Complete!${NC}"
echo ""
echo -e "${BLUE}Summary:${NC}"
echo "- ✅ Claude 4 Sonnet and Claude 4 Opus models added"
echo "- ✅ Provider Manager handles both Gemini and Claude"
echo "- ✅ Automatic model routing based on model ID"
echo "- ✅ Fallback mechanism between providers"
echo "- ✅ Streaming support for both providers"
echo "- ✅ Environment variables for ANTHROPIC_AUTH_TOKEN and ANTHROPIC_BASE_URL"
echo ""
echo -e "${YELLOW}Next steps:${NC}"
echo "1. Configure ANTHROPIC_AUTH_TOKEN and ANTHROPIC_BASE_URL in .env"
echo "2. Test with real Claude API credentials"
echo "3. Monitor logs for any issues"