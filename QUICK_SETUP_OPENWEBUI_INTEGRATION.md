# 🚀 Quick Setup - Open WebUI Integration

## Bước 1: Cấu <PERSON>

Thêm vào file `.env` (tạo mới nếu chưa có):

```bash
# Sao chép từ env.example nếu chưa có .env
cp env.example .env

# Hoặc thêm trực tiếp vào .env:
echo "OPEN_WEBUI_BASE_URL=http://localhost:3000" >> .env
echo "ENABLE_OPEN_WEBUI_TOOLS=true" >> .env
```

## Bước 2: Cài Đặt Dependencies (Tùy Chọn)

Nếu muốn sử dụng browser automation:
```bash
npm install puppeteer form-data
```

## Bước 3: Khởi Động Lại Server

```bash
# Nếu đang chạy bằng npm
npm start

# Nếu đang chạy bằng PM2
pm2 restart gemini-cli-wrapper
```

## Bước 4: <PERSON><PERSON><PERSON> Tra Hoạt Động

```bash
# Test nhanh
chmod +x test_openwebui_integration.sh
./test_openwebui_integration.sh
```

## Bước 5: Sử Dụng Ngay

Bây giờ trong Open WebUI, bạn có thể chat với agent và yêu cầu:

- **"Search for latest AI news using Open WebUI"**
- **"Create a new chat about programming"**
- **"Show me my recent conversations"**
- **"Get available models from Open WebUI"**

## ✅ Kiểm Tra Nhanh

Chạy lệnh này để xem tools đã được đăng ký:
```bash
curl -s http://localhost:8010/mcp/tools | jq '.tools[] | select(.category == "openwebui") | .name'
```

Nếu thấy output như:
```
"openwebui_web_search"
"openwebui_upload_file"
"openwebui_get_conversations"
...
```

Thì đã setup thành công! 🎉

## 🔧 Troubleshooting

Nếu không hoạt động:

1. **Kiểm tra server log:**
```bash
pm2 logs gemini-cli-wrapper
```

2. **Kiểm tra Open WebUI có chạy không:**
```bash
curl http://localhost:3000
```

3. **Kiểm tra .env file:**
```bash
cat .env | grep OPEN_WEBUI
```

## 🎯 Sẵn Sàng Sử Dụng!

Sau khi setup xong, agent sẽ có thể tương tác đầy đủ với Open WebUI!