#!/usr/bin/env node

import axios from 'axios';

const client = axios.create({
  baseURL: process.env.WRAPPER_BASE_URL || 'http://localhost:8010',
  headers: {
    'Content-Type': 'application/json'
  }
});

console.log('🧠 Enhanced Gemini 2.5 Pro Reasoning Test\n');
console.log('==========================================\n');

async function testEnhancedReasoning() {
  const testCases = [
    {
      name: "Technical Architecture Question",
      prompt: "How should I design a scalable microservices architecture for a high-traffic e-commerce platform with real-time inventory management?",
      expectedType: "technical"
    },
    {
      name: "Mathematical Problem",
      prompt: "A company's revenue follows the equation R(t) = 1000 + 50t + 2t², where t is time in months. Calculate the rate of change of revenue at t=6 months and interpret the result.",
      expectedType: "mathematical"
    },
    {
      name: "Ethical Dilemma",
      prompt: "Is it ethical for AI companies to use copyrighted content for training without explicit permission? Consider multiple stakeholder perspectives.",
      expectedType: "ethical"
    },
    {
      name: "Complex Problem Solving",
      prompt: "My startup is running out of funding, our main developer just quit, and we have a critical product launch in 3 weeks. Help me prioritize and create an action plan.",
      expectedType: "problem_solving"
    },
    {
      name: "Creative Challenge",
      prompt: "Design an innovative mobile app concept that combines augmented reality with social networking for Gen Z users. Think outside the box.",
      expectedType: "creative"
    },
    {
      name: "Factual Research",
      prompt: "What are the latest developments in quantum computing as of 2024, and how do they compare to classical computing capabilities?",
      expectedType: "factual"
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n🔍 Testing: ${testCase.name}`);
    console.log(`Expected reasoning type: ${testCase.expectedType}`);
    console.log('─'.repeat(60));
    
    try {
      // Test non-streaming first
      console.log('\n📄 Non-streaming response:');
      const response = await client.post('/v1/chat/completions', {
        model: 'gemini-2.5-pro',
        messages: [
          {
            role: 'user',
            content: testCase.prompt
          }
        ],
        stream: false
      });

      if (response.data.reasoning) {
        console.log('\n🤔 Reasoning Process:');
        console.log(response.data.reasoning.substring(0, 500) + '...');
      }
      
      console.log('\n💡 Final Answer:');
      console.log(response.data.choices[0].message.content.substring(0, 300) + '...');
      
      if (response.data.usage) {
        console.log('\n📊 Usage:', response.data.usage);
      }

      // Test streaming with R1 style
      console.log('\n🌊 Testing R1-style streaming...');
      let streamedReasoning = '';
      let streamedContent = '';
      
      const streamResponse = await client.post('/v1/chat/completions', {
        model: 'gemini-2.5-pro',
        messages: [
          {
            role: 'user',
            content: testCase.prompt
          }
        ],
        stream: true
      }, {
        responseType: 'stream'
      });

      // Process stream (simplified for demo)
      console.log('✅ Streaming initiated successfully');
      
    } catch (error) {
      console.error(`❌ Test failed for ${testCase.name}:`, error.response?.data || error.message);
    }
    
    console.log('\n' + '='.repeat(80));
  }
}

async function testReasoningMetadata() {
  console.log('\n🔬 Testing Reasoning Metadata and Complexity Analysis\n');
  
  const complexPrompt = `
    I'm building a distributed system that needs to handle 1 million concurrent users, 
    process real-time financial transactions with sub-millisecond latency requirements,
    ensure ACID compliance, implement end-to-end encryption, support multiple currencies,
    integrate with legacy banking systems, and maintain 99.99% uptime. The system also
    needs to be compliant with PCI DSS, SOX, and GDPR regulations across multiple
    jurisdictions. What architecture would you recommend, and how would you handle
    the technical, security, and compliance challenges?
  `;

  try {
    const response = await client.post('/v1/chat/completions', {
      model: 'gemini-2.5-pro',
      messages: [
        {
          role: 'user',
          content: complexPrompt
        }
      ],
      stream: false
    });

    console.log('✅ Complex reasoning test completed');
    console.log('Expected: High complexity, technical reasoning type, meta-cognitive reflection');
    
    if (response.data.reasoning) {
      const reasoning = response.data.reasoning;
      console.log('\n📈 Reasoning Analysis:');
      console.log(`- Length: ${reasoning.length} characters`);
      console.log(`- Contains meta-cognitive reflection: ${reasoning.includes('Meta-cognitive') ? 'Yes' : 'No'}`);
      console.log(`- Contains validation: ${reasoning.includes('validation') ? 'Yes' : 'No'}`);
      console.log(`- Technical depth indicators: ${(reasoning.match(/architecture|security|performance|scalability/gi) || []).length}`);
    }
    
  } catch (error) {
    console.error('❌ Metadata test failed:', error.response?.data || error.message);
  }
}

async function testAdaptiveStreaming() {
  console.log('\n⚡ Testing Adaptive Streaming Delays\n');
  
  const testPrompts = [
    {
      name: "Simple Question",
      prompt: "What is the capital of France?",
      expectedComplexity: "low"
    },
    {
      name: "Complex Analysis",
      prompt: "Analyze the geopolitical implications of quantum computing advancement on global cybersecurity, economic systems, and international relations, considering both short-term and long-term scenarios.",
      expectedComplexity: "high"
    }
  ];

  for (const test of testPrompts) {
    console.log(`\n🎯 Testing: ${test.name} (Expected complexity: ${test.expectedComplexity})`);
    
    try {
      const startTime = Date.now();
      
      const response = await client.post('/v1/chat/completions', {
        model: 'gemini-2.5-pro',
        messages: [
          {
            role: 'user',
            content: test.prompt
          }
        ],
        stream: false
      });
      
      const endTime = Date.now();
      console.log(`⏱️  Processing time: ${endTime - startTime}ms`);
      console.log('✅ Adaptive streaming test completed');
      
    } catch (error) {
      console.error(`❌ Adaptive streaming test failed for ${test.name}:`, error.message);
    }
  }
}

async function main() {
  try {
    // Check if server is running
    await client.get('/status');
    console.log('✅ Server is running\n');
    
    // Run all tests
    await testEnhancedReasoning();
    await testReasoningMetadata();
    await testAdaptiveStreaming();
    
    console.log('\n🎉 Enhanced reasoning tests completed!');
    console.log('\n💡 Key Improvements:');
    console.log('  • Sophisticated reasoning type detection with weighted scoring');
    console.log('  • Enhanced templates for 6 reasoning categories (technical, mathematical, ethical, etc.)');
    console.log('  • Meta-cognitive reflection for complex queries');
    console.log('  • High-stakes query validation');
    console.log('  • Adaptive streaming delays based on content complexity');
    console.log('  • Intelligent chunking with reasoning flow awareness');
    console.log('  • Rich metadata for debugging and optimization');
    
  } catch (error) {
    console.error('❌ Test suite failed:', error.message);
    console.log('\n💡 Make sure to:');
    console.log('  • Start the server: npm start');
    console.log('  • Set ENABLE_FAKE_THINKING=true in .env');
    console.log('  • Set STREAM_THINKING_AS_CONTENT=true for R1 style');
  }
}

main();