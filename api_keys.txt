# Gemini CLI Wrapper API Keys
# Generated on Wed Jul  9 06:28:08 UTC 2025
# 
# ⚠️  KEEP THIS FILE SECURE - DO NOT COMMIT TO VERSION CONTROL

## Admin API Key (Full Access)
# Can execute dangerous commands: execute_command, write_file, etc.
ADMIN_API_KEY=be5f6063f1b54ee8cdacc68446df0009b554459a6fe32f2fadc937188a92d3d7

## User API Key (Safe Operations)
# Can read files, search web, analyze code, use OpenWebUI tools
USER_API_KEY=2ae26d0e5603dd813b3c073dff3ec1d77047e90fddbf83b96b2e871f9191fe0c

## Guest API Key (Read-Only)
# Can only search web and get OpenWebUI models
GUEST_API_KEY=d483e4fc0823458a5f66e8ef699939ede0ca93189a7373894bd71e23bb71a7d5

## Usage Examples

### VS Code REST Client
Authorization: Bearer 2ae26d0e5603dd813b3c073dff3ec1d77047e90fddbf83b96b2e871f9191fe0c

### Curl Commands
# Admin command
curl -H "Authorization: Bearer be5f6063f1b54ee8cdacc68446df0009b554459a6fe32f2fadc937188a92d3d7" \
     -H "Content-Type: application/json" \
     -d '{"name":"execute_command","args":{"command":"ls -la"}}' \
     http://localhost:8011/mcp/tools/execute

# User command  
curl -H "Authorization: Bearer 2ae26d0e5603dd813b3c073dff3ec1d77047e90fddbf83b96b2e871f9191fe0c" \
     -H "Content-Type: application/json" \
     -d '{"name":"read_file","args":{"path":"package.json"}}' \
     http://localhost:8011/mcp/tools/execute

# Guest command
curl -H "Authorization: Bearer d483e4fc0823458a5f66e8ef699939ede0ca93189a7373894bd71e23bb71a7d5" \
     -H "Content-Type: application/json" \
     -d '{"name":"web_search","args":{"query":"test"}}' \
     http://localhost:8011/mcp/tools/execute

### Test RBAC Status
curl -H "Authorization: Bearer 2ae26d0e5603dd813b3c073dff3ec1d77047e90fddbf83b96b2e871f9191fe0c" \
     http://localhost:8011/rbac/status
