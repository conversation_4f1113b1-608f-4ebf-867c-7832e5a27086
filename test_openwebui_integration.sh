#!/bin/bash

echo "🧪 Testing Open WebUI Integration with Gemini CLI Wrapper"
echo "========================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Server URL
SERVER_URL="http://localhost:8010"
OPEN_WEBUI_URL="http://localhost:3000"

echo -e "${BLUE}Checking server status...${NC}"
echo ""

# Check if Gemini CLI Wrapper is running
echo "1. Checking Gemini CLI Wrapper status:"
curl -s "$SERVER_URL/health" | jq -r 'if .status == "healthy" then "✅ Gemini CLI Wrapper is running" else "❌ Gemini CLI Wrapper is not responding" end'

echo ""

# Check if Open WebUI is accessible
echo "2. Checking Open WebUI accessibility:"
if curl -s --connect-timeout 5 "$OPEN_WEBUI_URL" > /dev/null 2>&1; then
    echo "✅ Open WebUI is accessible"
else
    echo "❌ Open WebUI is not accessible at $OPEN_WEBUI_URL"
fi

echo ""
echo -e "${YELLOW}Testing Open WebUI Integration Tools...${NC}"
echo ""

# Test 1: Get available tools
echo "3. Testing available tools (should include Open WebUI tools):"
curl -s -X GET "$SERVER_URL/mcp/tools" | jq -r '.tools[] | select(.category == "openwebui") | "✅ " + .name + ": " + .description'

echo ""

# Test 2: Test Open WebUI models endpoint
echo "4. Testing Open WebUI models integration:"
curl -s -X POST "$SERVER_URL/mcp/tools/execute" \
  -H "Content-Type: application/json" \
  -d '{
    "tool_name": "openwebui_get_models",
    "arguments": {}
  }' | jq -r 'if .success then "✅ Successfully retrieved " + (.result.count | tostring) + " models from Open WebUI" else "❌ Failed: " + .error end'

echo ""

# Test 3: Test conversation listing
echo "5. Testing Open WebUI conversations:"
curl -s -X POST "$SERVER_URL/mcp/tools/execute" \
  -H "Content-Type: application/json" \
  -d '{
    "tool_name": "openwebui_get_conversations",
    "arguments": {
      "limit": 5
    }
  }' | jq -r 'if .success then "✅ Successfully retrieved conversations from Open WebUI" else "❌ Failed: " + .error end'

echo ""

# Test 4: Test web search through Open WebUI
echo "6. Testing Open WebUI web search:"
curl -s -X POST "$SERVER_URL/mcp/tools/execute" \
  -H "Content-Type: application/json" \
  -d '{
    "tool_name": "openwebui_web_search",
    "arguments": {
      "query": "latest AI news",
      "max_results": 3
    }
  }' | jq -r 'if .success then "✅ Web search through Open WebUI successful" else "⚠️ Web search failed (may not be available): " + .error end'

echo ""

# Test 5: Test creating a new chat
echo "7. Testing Open WebUI chat creation:"
curl -s -X POST "$SERVER_URL/mcp/tools/execute" \
  -H "Content-Type: application/json" \
  -d '{
    "tool_name": "openwebui_create_chat",
    "arguments": {
      "title": "Test Chat from Gemini CLI Wrapper",
      "model": "gemini-2.5-flash"
    }
  }' | jq -r 'if .success then "✅ Successfully created chat: " + .result.title else "❌ Failed to create chat: " + .error end'

echo ""

# Test 6: Test sending a message through Open WebUI
echo "8. Testing Open WebUI message sending:"
curl -s -X POST "$SERVER_URL/mcp/tools/execute" \
  -H "Content-Type: application/json" \
  -d '{
    "tool_name": "openwebui_send_message",
    "arguments": {
      "message": "Hello from Gemini CLI Wrapper integration test!",
      "model": "gemini-2.5-flash"
    }
  }' | jq -r 'if .success then "✅ Successfully sent message through Open WebUI" else "❌ Failed to send message: " + .error end'

echo ""
echo -e "${YELLOW}Integration Summary:${NC}"
echo ""

# Summary of capabilities
echo -e "${GREEN}✅ Available Open WebUI Integration Features:${NC}"
echo "   • Web search through Open WebUI"
echo "   • File upload to Open WebUI"
echo "   • Conversation management"
echo "   • Chat creation and messaging"
echo "   • Model listing and selection"
echo "   • Browser automation (with puppeteer)"
echo ""

echo -e "${BLUE}📋 How to use in chat:${NC}"
echo "Ask the AI to:"
echo "   • 'Search the web using Open WebUI'"
echo "   • 'Create a new chat in Open WebUI'"
echo "   • 'Upload this file to Open WebUI'"
echo "   • 'Get my recent conversations from Open WebUI'"
echo "   • 'Send a message through Open WebUI'"
echo ""

echo -e "${YELLOW}⚙️ Configuration:${NC}"
echo "Add to your .env file:"
echo "   OPEN_WEBUI_BASE_URL=http://localhost:3000"
echo "   ENABLE_OPEN_WEBUI_TOOLS=true"
echo ""

echo -e "${RED}📝 Note:${NC}"
echo "Some features may require Open WebUI API endpoints to be available."
echo "If tests fail, check that Open WebUI is running and accessible."
echo ""

echo "🎉 Integration test completed!"