#!/usr/bin/env node

/**
 * Test script for new Gemini CLI tools in MCP server
 */

import axios from 'axios';

const BASE_URL = 'http://localhost:8010';
const API_KEY = 'test-key'; // Using default test key

async function testNewTools() {
  console.log('🧪 Testing new Gemini CLI tools...\n');

  // Test 1: List Directory
  console.log('1️⃣ Testing list_directory tool...');
  try {
    const response = await axios.post(`${BASE_URL}/mcp/tools/execute`, {
      tool_name: 'list_directory',
      arguments: {
        path: '.',
        ignore_patterns: ['node_modules', '*.log']
      }
    }, {
      headers: { 'Authorization': `Bearer ${API_KEY}` }
    });
    
    console.log('✅ list_directory works!');
    console.log(`   Found ${response.data.result.count} items`);
    console.log(`   Sample items: ${response.data.result.items.slice(0, 3).map(i => i.name).join(', ')}`);
  } catch (error) {
    console.log('❌ list_directory failed:', error.response?.data?.error || error.message);
  }

  // Test 2: Search File Content
  console.log('\n2️⃣ Testing search_file_content tool...');
  try {
    const response = await axios.post(`${BASE_URL}/mcp/tools/execute`, {
      tool_name: 'search_file_content',
      arguments: {
        pattern: 'registerTool',
        directory: 'src',
        file_pattern: '*.js',
        recursive: true
      }
    }, {
      headers: { 'Authorization': `Bearer ${API_KEY}` }
    });
    
    console.log('✅ search_file_content works!');
    console.log(`   Found ${response.data.result.count} matches`);
    if (response.data.result.matches.length > 0) {
      console.log(`   Sample match: ${response.data.result.matches[0].file}:${response.data.result.matches[0].line}`);
    }
  } catch (error) {
    console.log('❌ search_file_content failed:', error.response?.data?.error || error.message);
  }

  // Test 3: Glob
  console.log('\n3️⃣ Testing glob tool...');
  try {
    const response = await axios.post(`${BASE_URL}/mcp/tools/execute`, {
      tool_name: 'glob',
      arguments: {
        pattern: '**/*.js',
        base_directory: 'src',
        sort_by_time: true
      }
    }, {
      headers: { 'Authorization': `Bearer ${API_KEY}` }
    });
    
    console.log('✅ glob works!');
    console.log(`   Found ${response.data.result.count} JavaScript files`);
    if (response.data.result.matches.length > 0) {
      console.log(`   Sample files: ${response.data.result.matches.slice(0, 3).map(m => m.relativePath).join(', ')}`);
    }
  } catch (error) {
    console.log('❌ glob failed:', error.response?.data?.error || error.message);
  }

  // Test 4: Read Many Files
  console.log('\n4️⃣ Testing read_many_files tool...');
  try {
    const response = await axios.post(`${BASE_URL}/mcp/tools/execute`, {
      tool_name: 'read_many_files',
      arguments: {
        paths: ['package.json', 'README.md'],
        target_directory: '.',
        include_metadata: true
      }
    }, {
      headers: { 'Authorization': `Bearer ${API_KEY}` }
    });
    
    console.log('✅ read_many_files works!');
    console.log(`   Read ${response.data.result.successful_reads} files successfully`);
    console.log(`   Failed to read ${response.data.result.failed_reads} files`);
  } catch (error) {
    console.log('❌ read_many_files failed:', error.response?.data?.error || error.message);
  }

  // Test 5: Web Fetch (test with a simple URL)
  console.log('\n5️⃣ Testing web_fetch tool...');
  try {
    const response = await axios.post(`${BASE_URL}/mcp/tools/execute`, {
      tool_name: 'web_fetch',
      arguments: {
        urls: ['http://httpbin.org/json'],
        instructions: 'Extract the JSON data',
        timeout: 15000
      }
    }, {
      headers: { 'Authorization': `Bearer ${API_KEY}` }
    });
    
    console.log('✅ web_fetch works!');
    console.log(`   Fetched ${response.data.result.successful_fetches} URLs successfully`);
    console.log(`   Failed to fetch ${response.data.result.failed_fetches} URLs`);
  } catch (error) {
    console.log('❌ web_fetch failed:', error.response?.data?.error || error.message);
  }

  // Test 6: Check all available tools
  console.log('\n6️⃣ Checking all available tools...');
  try {
    const response = await axios.get(`${BASE_URL}/mcp/tools`, {
      headers: { 'Authorization': `Bearer ${API_KEY}` }
    });
    
    const newTools = response.data.tools.filter(tool => 
      ['list_directory', 'search_file_content', 'glob', 'web_fetch', 'read_many_files'].includes(tool.name)
    );
    
    console.log('✅ All new tools are registered:');
    newTools.forEach(tool => {
      console.log(`   - ${tool.name}: ${tool.description.substring(0, 60)}...`);
    });
    
    console.log(`\n📊 Total tools available: ${response.data.tools.length}`);
  } catch (error) {
    console.log('❌ Failed to get tools list:', error.response?.data?.error || error.message);
  }

  console.log('\n🎉 Testing completed!');
}

// Run the tests
testNewTools().catch(console.error);