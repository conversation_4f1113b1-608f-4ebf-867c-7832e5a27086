#!/bin/bash

# API Key Setup Script for Gemini CLI Wrapper
# This script generates secure API keys and configures the environment

set -e

echo "🔑 Gemini CLI Wrapper - API Key Setup"
echo "===================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to generate secure API key
generate_api_key() {
    if command -v openssl >/dev/null 2>&1; then
        openssl rand -hex 32
    elif command -v python3 >/dev/null 2>&1; then
        python3 -c "import secrets; print(secrets.token_hex(32))"
    elif command -v node >/dev/null 2>&1; then
        node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
    else
        echo "Error: No suitable random generator found (openssl, python3, or node required)"
        exit 1
    fi
}

# Check if .env already exists
if [ -f ".env" ]; then
    echo -e "${YELLOW}⚠️  .env file already exists${NC}"
    read -p "Do you want to backup and recreate it? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        cp .env .env.backup.$(date +%Y%m%d_%H%M%S)
        echo -e "${GREEN}✅ Backup created${NC}"
    else
        echo -e "${BLUE}ℹ️  Keeping existing .env file${NC}"
        exit 0
    fi
fi

# Generate API keys
echo -e "${BLUE}🔐 Generating secure API keys...${NC}"
ADMIN_KEY=$(generate_api_key)
USER_KEY=$(generate_api_key)
GUEST_KEY=$(generate_api_key)

echo -e "${GREEN}✅ API keys generated successfully${NC}"

# Create .env file
echo -e "${BLUE}📝 Creating .env file...${NC}"

cat > .env << EOF
# Gemini CLI Wrapper Environment Configuration
# Generated on $(date)

# Basic Configuration
PORT=8011
NODE_ENV=development

# API Keys for AI Services
GEMINI_API_KEY=your-gemini-api-key-here
CLAUDE_API_KEY=your-claude-api-key-here

# RBAC API Keys - KEEP THESE SECURE!
ADMIN_API_KEY=${ADMIN_KEY}
USER_API_KEY=${USER_KEY}
GUEST_API_KEY=${GUEST_KEY}

# External Client Default Roles
EXTERNAL_CLIENT_ROLE=user
VSCODE_CLIENT_ROLE=user
CLINE_CLIENT_ROLE=user

# API Key to Role Mappings (JSON format)
API_KEY_ROLE_MAPPINGS={"${ADMIN_KEY}":"admin","${USER_KEY}":"user","${GUEST_KEY}":"guest"}

# Security Settings
GEMINI_UNRESTRICTED_MODE=false

# Open WebUI Integration
OPENWEBUI_BASE_URL=http://localhost:3000
OPENWEBUI_API_KEY=your-openwebui-api-key

# Logging Level
LOG_LEVEL=info
EOF

echo -e "${GREEN}✅ .env file created successfully${NC}"

# Create API key reference file
echo -e "${BLUE}📋 Creating API key reference...${NC}"

cat > api_keys.txt << EOF
# Gemini CLI Wrapper API Keys
# Generated on $(date)
# 
# ⚠️  KEEP THIS FILE SECURE - DO NOT COMMIT TO VERSION CONTROL

## Admin API Key (Full Access)
# Can execute dangerous commands: execute_command, write_file, etc.
ADMIN_API_KEY=${ADMIN_KEY}

## User API Key (Safe Operations)
# Can read files, search web, analyze code, use OpenWebUI tools
USER_API_KEY=${USER_KEY}

## Guest API Key (Read-Only)
# Can only search web and get OpenWebUI models
GUEST_API_KEY=${GUEST_KEY}

## Usage Examples

### VS Code REST Client
Authorization: Bearer ${USER_KEY}

### Curl Commands
# Admin command
curl -H "Authorization: Bearer ${ADMIN_KEY}" \\
     -H "Content-Type: application/json" \\
     -d '{"name":"execute_command","args":{"command":"ls -la"}}' \\
     http://localhost:8011/mcp/tools/execute

# User command  
curl -H "Authorization: Bearer ${USER_KEY}" \\
     -H "Content-Type: application/json" \\
     -d '{"name":"read_file","args":{"path":"package.json"}}' \\
     http://localhost:8011/mcp/tools/execute

# Guest command
curl -H "Authorization: Bearer ${GUEST_KEY}" \\
     -H "Content-Type: application/json" \\
     -d '{"name":"web_search","args":{"query":"test"}}' \\
     http://localhost:8011/mcp/tools/execute

### Test RBAC Status
curl -H "Authorization: Bearer ${USER_KEY}" \\
     http://localhost:8011/rbac/status
EOF

echo -e "${GREEN}✅ API key reference created: api_keys.txt${NC}"

# Set proper permissions
chmod 600 .env api_keys.txt
echo -e "${GREEN}✅ File permissions secured${NC}"

# Display summary
echo
echo -e "${GREEN}🎉 Setup Complete!${NC}"
echo "==================="
echo
echo -e "${BLUE}📁 Files created:${NC}"
echo "  • .env - Environment configuration"
echo "  • api_keys.txt - API key reference (KEEP SECURE!)"
echo
echo -e "${BLUE}🔑 API Keys generated:${NC}"
echo -e "  • ${GREEN}Admin${NC}: ${ADMIN_KEY:0:16}..."
echo -e "  • ${YELLOW}User${NC}:  ${USER_KEY:0:16}..."
echo -e "  • ${RED}Guest${NC}: ${GUEST_KEY:0:16}..."
echo
echo -e "${BLUE}🚀 Next steps:${NC}"
echo "  1. Add your Gemini/Claude API keys to .env"
echo "  2. Start the server: npm start"
echo "  3. Test with: ./test_rbac_security.sh"
echo "  4. Configure your agents with the API keys"
echo
echo -e "${YELLOW}⚠️  Security reminders:${NC}"
echo "  • Keep api_keys.txt secure and private"
echo "  • Never commit API keys to version control"
echo "  • Use different keys for production"
echo "  • Rotate keys regularly"
echo
echo -e "${BLUE}📖 For detailed setup instructions, see:${NC}"
echo "  • API_KEY_SETUP_GUIDE.md"
echo "  • RBAC_SECURITY_GUIDE.md"
echo

# Test if server is running
if curl -s http://localhost:8011/health >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Server is running - you can test immediately!${NC}"
    echo
    echo -e "${BLUE}🧪 Quick test:${NC}"
    echo "curl -H \"Authorization: Bearer ${USER_KEY}\" http://localhost:8011/rbac/status"
else
    echo -e "${YELLOW}ℹ️  Start the server to begin testing:${NC}"
    echo "PORT=8011 node src/index.js"
fi

echo
echo -e "${GREEN}Happy coding! 🚀${NC}"
EOF