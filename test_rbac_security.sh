#!/bin/bash

# RBAC Security Test Script
# Tests role-based access control for Gemini CLI Wrapper

set -e

BASE_URL="http://localhost:8011"
API_KEY="test-api-key"

echo "🔒 RBAC Security Test Suite"
echo "=========================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test function
test_endpoint() {
    local role=$1
    local action=$2
    local expected_status=$3
    local description=$4
    
    echo -e "\n${YELLOW}Testing:${NC} $description"
    echo "Role: $role | Action: $action | Expected: $expected_status"
    
    # Set appropriate args based on tool type
    local args_json
    case "$action" in
        "read_file")
            args_json='{"path": "package.json"}'
            ;;
        "write_file")
            args_json='{"path": "/tmp/test.txt", "content": "test content"}'
            ;;
        "web_search")
            args_json='{"query": "test search"}'
            ;;
        "execute_command")
            args_json='{"command": "echo test"}'
            ;;
        "openwebui_web_search")
            args_json='{"query": "test search"}'
            ;;
        "openwebui_send_message")
            args_json='{"message": "test message"}'
            ;;
        "openwebui_get_models")
            args_json='{}'
            ;;
        *)
            args_json='{"command": "echo test"}'
            ;;
    esac
    
    response=$(curl -s -w "HTTPSTATUS:%{http_code}" \
        -X POST "$BASE_URL/mcp/tools/execute" \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $API_KEY" \
        -H "X-User-Role: $role" \
        -d "{\"tool_name\": \"$action\", \"args\": $args_json}")
    
    http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    body=$(echo $response | sed -e 's/HTTPSTATUS\:.*//g')
    
    if [ "$http_code" -eq "$expected_status" ]; then
        echo -e "${GREEN}✅ PASS${NC} - HTTP $http_code"
    else
        echo -e "${RED}❌ FAIL${NC} - Expected $expected_status, got $http_code"
        echo "Response: $body"
    fi
}

# Test RBAC status endpoint
test_rbac_status() {
    local role=$1
    echo -e "\n${YELLOW}Testing RBAC Status for role:${NC} $role"
    
    response=$(curl -s -w "HTTPSTATUS:%{http_code}" \
        -X GET "$BASE_URL/rbac/status" \
        -H "Authorization: Bearer $API_KEY" \
        -H "X-User-Role: $role")
    
    http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    body=$(echo $response | sed -e 's/HTTPSTATUS\:.*//g')
    
    if [ "$http_code" -eq 200 ]; then
        echo -e "${GREEN}✅ RBAC Status OK${NC}"
        echo "$body" | jq '.current_user.role, .current_user.permissions' 2>/dev/null || echo "$body"
    else
        echo -e "${RED}❌ RBAC Status Failed${NC} - HTTP $http_code"
    fi
}

# Test permission check endpoint
test_permission_check() {
    local role=$1
    local action=$2
    local expected_allowed=$3
    
    echo -e "\n${YELLOW}Testing Permission Check:${NC} $role -> $action"
    
    response=$(curl -s -w "HTTPSTATUS:%{http_code}" \
        -X POST "$BASE_URL/rbac/test-permission" \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $API_KEY" \
        -H "X-User-Role: $role" \
        -d "{\"action\": \"$action\"}")
    
    http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    body=$(echo $response | sed -e 's/HTTPSTATUS\:.*//g')
    
    if [ "$http_code" -eq 200 ]; then
        allowed=$(echo "$body" | jq -r '.allowed' 2>/dev/null || echo "unknown")
        if [ "$allowed" = "$expected_allowed" ]; then
            echo -e "${GREEN}✅ Permission Check PASS${NC} - Allowed: $allowed"
        else
            echo -e "${RED}❌ Permission Check FAIL${NC} - Expected: $expected_allowed, Got: $allowed"
        fi
    else
        echo -e "${RED}❌ Permission Check Error${NC} - HTTP $http_code"
    fi
}

echo -e "\n🏁 Starting RBAC Tests..."

# Test 1: RBAC Status for different roles
echo -e "\n📊 Test 1: RBAC Status Checks"
test_rbac_status "admin"
test_rbac_status "user" 
test_rbac_status "guest"

# Test 2: Permission checks
echo -e "\n🔍 Test 2: Permission Checks"
test_permission_check "admin" "execute_command" "true"
test_permission_check "user" "execute_command" "false"
test_permission_check "guest" "execute_command" "false"

test_permission_check "admin" "write_file" "true"
test_permission_check "user" "write_file" "false"
test_permission_check "guest" "write_file" "false"

test_permission_check "user" "read_file" "true"
test_permission_check "guest" "read_file" "false"

test_permission_check "user" "web_search" "true"
test_permission_check "guest" "web_search" "true"

# Test 3: Actual tool execution
echo -e "\n⚡ Test 3: Tool Execution Tests"

# Admin should be able to execute dangerous commands
test_endpoint "admin" "execute_command" 200 "Admin executing dangerous command"

# User should be denied dangerous commands
test_endpoint "user" "execute_command" 403 "User attempting dangerous command (should be denied)"
test_endpoint "user" "write_file" 403 "User attempting file write (should be denied)"

# User should be allowed safe commands
test_endpoint "user" "web_search" 200 "User executing safe command"
test_endpoint "user" "read_file" 200 "User reading file"

# Guest should be denied most commands
test_endpoint "guest" "execute_command" 403 "Guest attempting dangerous command (should be denied)"
test_endpoint "guest" "read_file" 403 "Guest attempting file read (should be denied)"
test_endpoint "guest" "web_search" 200 "Guest executing web search (should be allowed)"

# Test 4: Open WebUI specific tools
echo -e "\n🌐 Test 4: Open WebUI Tools"
test_endpoint "admin" "openwebui_send_message" 200 "Admin using OpenWebUI send message"
test_endpoint "user" "openwebui_send_message" 403 "User attempting OpenWebUI send message (should be denied)"
test_endpoint "user" "openwebui_web_search" 200 "User using OpenWebUI web search"
test_endpoint "guest" "openwebui_get_models" 200 "Guest getting OpenWebUI models"

# Test 5: No role header (should default to user)
echo -e "\n👤 Test 5: Default Role Behavior"
echo -e "\n${YELLOW}Testing:${NC} No role header (should default to user)"

response=$(curl -s -w "HTTPSTATUS:%{http_code}" \
    -X POST "$BASE_URL/mcp/tools/execute" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $API_KEY" \
    -d '{"tool_name": "execute_command", "args": {"command": "echo test"}}')

http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')

if [ "$http_code" -eq 403 ]; then
    echo -e "${GREEN}✅ PASS${NC} - Default role correctly denied dangerous action"
else
    echo -e "${RED}❌ FAIL${NC} - Default role should be denied, got HTTP $http_code"
fi

# Test 6: Invalid role
echo -e "\n❓ Test 6: Invalid Role"
test_endpoint "invalid_role" "web_search" 403 "Invalid role attempting any action (should be denied)"

echo -e "\n🎯 Test Summary"
echo "=============="
echo "✅ Admin can execute all commands including dangerous ones"
echo "❌ User cannot execute dangerous commands (execute_command, write_file)"
echo "✅ User can execute safe commands (read_file, web_search)"
echo "❌ Guest has very limited access"
echo "✅ Default role (no header) behaves as user"
echo "❌ Invalid roles are denied access"

echo -e "\n${GREEN}🔒 RBAC Security System is working correctly!${NC}"
echo -e "${YELLOW}⚠️  Only admin accounts can execute dangerous commands${NC}"