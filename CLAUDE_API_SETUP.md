# 🔧 Cấu hình Claude API để có Real Response

## ✅ Fix đã hoạt động!

Từ hình ảnh, tôi thấy `claude-4-sonnet` giờ đây đã route đúng đến Claude provider và trả về:

```
"This is a mock response from the Claude CLI integration. The real CLI would provide more sophisticated responses based on the latest Claude models. [Note: This is a mock response from claude-4-sonnet. Configure ANTHROPIC_AUTH_TOKEN and ANTHROPIC_BASE_URL for real Claude API access.]"
```

## 🚀 Để có Real Claude Response

### Bước 1: Cấu hình Environment Variables

Thêm vào file `.env`:

```bash
# Claude API Configuration
ANTHROPIC_AUTH_TOKEN=your_claude_api_key_here
ANTHROPIC_BASE_URL=https://api.anthropic.com

# Hoặc nếu sử dụng proxy/alternative endpoint
ANTHROPIC_BASE_URL=https://your-proxy-url.com
```

### Bước 2: <PERSON><PERSON><PERSON> API Key

1. **Anthropic Official API**:
   - T<PERSON>y cập: https://console.anthropic.com/
   - Tạo API key
   - Copy key vào `ANTHROPIC_AUTH_TOKEN`

2. **Alternative Providers** (nếu cần):
   - YesScale.io, OpenRouter, v.v.
   - Cấu hình `ANTHROPIC_BASE_URL` tương ứng

### Bước 3: Restart Server

```bash
# Restart để load environment variables mới
./apply_claude_fix.sh

# Hoặc manual restart
pm2 restart ecosystem.config.cjs
```

### Bước 4: Test Real API

```bash
# Test với real API
curl -X POST http://localhost:8010/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer test-key" \
  -d '{
    "model": "claude-4-sonnet",
    "messages": [
      {
        "role": "user", 
        "content": "Hello Claude! Please tell me about yourself."
      }
    ]
  }'
```

## 🔍 Kiểm tra trạng thái

```bash
# Kiểm tra environment variables
curl -H "Authorization: Bearer test-key" http://localhost:8010/status | jq '.providers.claude'
```

## 📋 Troubleshooting

### Nếu vẫn nhận mock response:

1. **Kiểm tra environment variables**:
   ```bash
   echo $ANTHROPIC_AUTH_TOKEN
   echo $ANTHROPIC_BASE_URL
   ```

2. **Kiểm tra Claude CLI installation**:
   ```bash
   claude --version
   ```

3. **Kiểm tra logs**:
   ```bash
   tail -f server.log
   ```

### Các lỗi thường gặp:

- **"Credit balance is too low"**: API key hết quota
- **"API key"**: API key không hợp lệ
- **"Network error"**: Vấn đề kết nối

## 🎯 Kết quả mong đợi

Sau khi cấu hình đúng, response sẽ là:

```json
{
  "choices": [{
    "message": {
      "role": "assistant",
      "content": "Hello! I'm Claude, an AI assistant created by Anthropic. I'm designed to be helpful, harmless, and honest..."
    }
  }],
  "model": "claude-4-sonnet",
  "provider": "claude"
}
```

## 📝 Lưu ý

- Mock response chỉ xuất hiện khi chưa cấu hình API key hoặc CLI gặp lỗi
- Fix routing đã hoạt động - model đã route đúng đến Claude provider
- Cần cấu hình thêm để có real API response thay vì mock

---

**Fix routing đã thành công! Giờ chỉ cần cấu hình API key để có real Claude response.**