<!DOCTYPE html>
<html>
<head>
    <title>Test Gemini CLI Wrapper Connection</title>
</head>
<body>
    <h1>Test Gemini CLI Wrapper Connection</h1>
    <button onclick="testConnection()">Test Connection</button>
    <div id="result"></div>

    <script>
        async function testConnection() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing...';
            
            try {
                // Test health endpoint
                const healthResponse = await fetch('http://localhost:8010/health');
                const healthData = await healthResponse.json();
                console.log('Health:', healthData);
                
                // Test models endpoint
                const modelsResponse = await fetch('http://localhost:8010/v1/models');
                const modelsData = await modelsResponse.json();
                console.log('Models:', modelsData);
                
                resultDiv.innerHTML = `
                    <h3>✅ Connection Successful!</h3>
                    <p><strong>Health:</strong> ${healthData.status}</p>
                    <p><strong>Models found:</strong> ${modelsData.data.length}</p>
                    <ul>
                        ${modelsData.data.map(model => `<li>${model.id}</li>`).join('')}
                    </ul>
                `;
            } catch (error) {
                console.error('Error:', error);
                resultDiv.innerHTML = `
                    <h3>❌ Connection Failed</h3>
                    <p><strong>Error:</strong> ${error.message}</p>
                    <p>This might be due to CORS policy or browser security restrictions.</p>
                `;
            }
        }
    </script>
</body>
</html>