# Open Web UI API Key Authentication Setup ✅

## 🔐 **API Key Authentication Enabled**

Đ<PERSON> tăng cườ<PERSON> b<PERSON><PERSON> mậ<PERSON>, server hiện yêu cầu API key cho tất cả các OpenAI endpoints (`/v1/*`).

## 📋 **Configuration**

### **Environment Variables (.env):**
```bash
# OpenAI API Security Configuration
REQUIRE_OPENAI_AUTH=true

# Dedicated API key for Open WebUI access
OPENWEBUI_API_KEY=sk-openwebui-access-2025-secure-key-abc123def456
```

## 🚀 **Open Web UI Setup**

### **Connection Settings:**
1. **Base URL**: `http://localhost:8010` (hoặc IP server của bạn)
2. **API Key**: `sk-openwebui-access-2025-secure-key-abc123def456`

### **Cách cấu hình:**
1. Vào **Settings** → **Connections** trong Open Web UI
2. **OpenAI API Base URL**: `http://localhost:8010`
3. **API Key**: `sk-openwebui-access-2025-secure-key-abc123def456`
4. **Save** và test với model `claude-4-sonnet` hoặc `gemini-2.5-pro`

## 🔒 **Security Features**

### **Authentication Flow:**
1. **No API Key** → `401 Unauthorized: API key required for OpenAI endpoints`
2. **Wrong API Key** → Falls back to regular authentication system
3. **Correct API Key** → `✅ OpenWebUI API key authentication successful`

### **Dedicated Access:**
- API key riêng cho Open Web UI: `OPENWEBUI_API_KEY`
- Không cần cấu hình phức tạp với RBAC system
- User role mặc định: `user` (đủ quyền cho chat completions)

## 🧪 **Testing**

### **Test without API key (should fail):**
```bash
curl -X POST http://localhost:8010/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "claude-4-sonnet",
    "messages": [{"role": "user", "content": "Test"}]
  }'
```
**Expected**: `{"error":"Unauthorized","message":"API key required for OpenAI endpoints"}`

### **Test with correct API key (should work):**
```bash
curl -X POST http://localhost:8010/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer sk-openwebui-access-2025-secure-key-abc123def456" \
  -d '{
    "model": "claude-4-sonnet",
    "messages": [{"role": "user", "content": "Hello!"}]
  }'
```
**Expected**: Claude response with streaming support

## 📊 **Server Logs**

### **Successful Authentication:**
```
✅ OpenWebUI API key authentication successful
✅ Routing model claude-4-sonnet to claude provider
Claude response received in XXXXms
POST /v1/chat/completions - 200
```

### **Failed Authentication:**
```
POST /v1/chat/completions - 401 - 4ms
```

## 🔧 **Advanced Configuration**

### **Disable Authentication (if needed):**
```bash
# In .env file
REQUIRE_OPENAI_AUTH=false
```

### **Change API Key:**
1. Update `OPENWEBUI_API_KEY` in `.env`
2. Restart server: `pm2 restart gemini-cli-wrapper --update-env`
3. Update API key in Open Web UI settings

## ✅ **Available Models**

With proper authentication, you can access:
- ✅ `claude-4-sonnet` (streaming enabled)
- ✅ `gemini-2.5-pro`
- ✅ `gemini-2.5-flash`
- ✅ All other Gemini models

## 🎯 **Benefits**

1. **Security**: Prevents unauthorized access to AI models
2. **Simplicity**: Single API key for Open Web UI
3. **Flexibility**: Can disable auth for development
4. **Compatibility**: Works with existing Open Web UI setup
5. **Monitoring**: Clear logs for authentication events

---

**Status**: ✅ **ACTIVE** - API key authentication enabled and working
**Date**: 2025-07-09
**API Key**: `sk-openwebui-access-2025-secure-key-abc123def456`