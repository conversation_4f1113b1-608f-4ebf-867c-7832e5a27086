# Puppeteer Browser Automation Guide

## Overview

The Gemini CLI Wrapper now includes powerful Puppeteer browser automation capabilities through the MCP (Model Context Protocol) system. This allows the LLM to control web browsers, interact with web pages, take screenshots, and perform complex web automation tasks.

## Available Tools

### 1. `browser_launch` - Launch Browser Session
Launch a new browser instance and navigate to a URL.

**Parameters:**
- `url` (required): URL to navigate to
- `headless` (optional): Run in headless mode (default: true)
- `viewport` (optional): Browser viewport settings
  - `width` (default: 1280)
  - `height` (default: 720)
- `session_id` (optional): Session ID for browser instance (default: "default")

**Example Usage:**
```javascript
// Launch browser and go to Google
{
  "tool": "browser_launch",
  "args": {
    "url": "https://www.google.com",
    "headless": false,
    "viewport": { "width": 1920, "height": 1080 },
    "session_id": "google_session"
  }
}
```

### 2. `browser_action` - Perform Browser Actions
Perform various actions in an active browser session.

**Actions Available:**
- `click`: Click on an element
- `type`: Type text into an input field
- `scroll`: Scroll the page
- `screenshot`: Take a screenshot
- `navigate`: Navigate to a new URL
- `wait`: Wait for element or time
- `evaluate`: Execute JavaScript code

**Parameters:**
- `action` (required): Action to perform
- `selector` (optional): CSS selector for element
- `value` (optional): Value to type or URL to navigate
- `script` (optional): JavaScript code for evaluate action
- `timeout` (optional): Timeout in milliseconds (default: 5000)
- `session_id` (optional): Browser session ID (default: "default")

**Example Usage:**
```javascript
// Click on search button
{
  "tool": "browser_action",
  "args": {
    "action": "click",
    "selector": "input[name='btnK']",
    "session_id": "google_session"
  }
}

// Type in search box
{
  "tool": "browser_action",
  "args": {
    "action": "type",
    "selector": "input[name='q']",
    "value": "Puppeteer automation",
    "session_id": "google_session"
  }
}

// Take screenshot
{
  "tool": "browser_action",
  "args": {
    "action": "screenshot",
    "value": "full",
    "session_id": "google_session"
  }
}

// Execute JavaScript
{
  "tool": "browser_action",
  "args": {
    "action": "evaluate",
    "script": "document.title",
    "session_id": "google_session"
  }
}
```

### 3. `browser_close` - Close Browser Session
Close an active browser session.

**Parameters:**
- `session_id` (optional): Browser session ID to close (default: "default")

### 4. `browser_info` - Get Browser Information
Get information about all active browser sessions.

## LLM Usage Patterns

### Automatic Browser Usage
The LLM can automatically use browser tools based on user requests:

- **"Take a screenshot of google.com"** → `browser_launch` + `browser_action` (screenshot)
- **"Search for something on Google"** → `browser_launch` + `browser_action` (type) + `browser_action` (click)
- **"Check if a website is working"** → `browser_launch` + `browser_action` (screenshot)
- **"Fill out a form on a website"** → `browser_launch` + multiple `browser_action` (type, click)

### Tool Chaining Examples

#### 1. Web Search Automation
```
1. browser_launch → Navigate to Google
2. browser_action (type) → Enter search query
3. browser_action (click) → Click search button
4. browser_action (screenshot) → Capture results
5. browser_close → Clean up
```

#### 2. Website Testing
```
1. browser_launch → Open website
2. browser_action (screenshot) → Initial state
3. browser_action (click) → Test navigation
4. browser_action (screenshot) → After interaction
5. browser_action (evaluate) → Check JavaScript errors
6. browser_close → Clean up
```

#### 3. Form Automation
```
1. browser_launch → Open form page
2. browser_action (type) → Fill name field
3. browser_action (type) → Fill email field
4. browser_action (click) → Submit form
5. browser_action (wait) → Wait for response
6. browser_action (screenshot) → Capture result
7. browser_close → Clean up
```

## Security and Configuration

### Environment Variables
- `PUPPETEER_HEADLESS`: Set to "false" to run browser with GUI (default: true)
- `GEMINI_UNRESTRICTED_MODE`: Enable unrestricted browser access

### System Requirements
- **Linux**: Chromium browser (`sudo apt-get install chromium-browser`)
- **macOS**: Chrome or Chromium
- **Windows**: Chrome or Chromium

### Browser Configuration
The system automatically configures browsers with:
- No sandbox mode for Linux compatibility
- Disabled GPU acceleration for stability
- Optimized for headless operation
- Security settings for automation

## Error Handling

### Common Issues and Solutions

1. **"Puppeteer not available"**
   - Solution: `npm install puppeteer`

2. **"Browser launch failed"**
   - Check if Chromium is installed
   - Verify system permissions
   - Try with `headless: false` for debugging

3. **"Element not found"**
   - Increase timeout value
   - Use more specific CSS selectors
   - Wait for page to load completely

4. **"Session not found"**
   - Check if browser was launched
   - Verify session_id matches
   - Use `browser_info` to see active sessions

## Best Practices for LLM

### 1. Session Management
- Use descriptive session IDs for multiple browsers
- Always close browsers when done
- Check active sessions with `browser_info`

### 2. Element Selection
- Use specific CSS selectors
- Wait for elements to load before interacting
- Handle dynamic content with appropriate timeouts

### 3. Error Recovery
- Take screenshots to debug issues
- Use JavaScript evaluation to check page state
- Implement retry logic for flaky interactions

### 4. Performance
- Use headless mode for faster execution
- Close unused browser sessions
- Limit concurrent browser instances

## Example Workflows

### Website Monitoring
```javascript
// Monitor website availability
const workflow = [
  { tool: "browser_launch", args: { url: "https://example.com" } },
  { tool: "browser_action", args: { action: "screenshot" } },
  { tool: "browser_action", args: { 
    action: "evaluate", 
    script: "document.readyState === 'complete'" 
  }},
  { tool: "browser_close", args: {} }
];
```

### Social Media Automation
```javascript
// Post to social media (example)
const workflow = [
  { tool: "browser_launch", args: { url: "https://twitter.com" } },
  { tool: "browser_action", args: { 
    action: "click", 
    selector: "[data-testid='tweetButtonInline']" 
  }},
  { tool: "browser_action", args: { 
    action: "type", 
    selector: "[data-testid='tweetTextarea_0']",
    value: "Hello from Puppeteer automation!" 
  }},
  { tool: "browser_action", args: { action: "screenshot" } },
  { tool: "browser_close", args: {} }
];
```

### E-commerce Testing
```javascript
// Test product search
const workflow = [
  { tool: "browser_launch", args: { url: "https://amazon.com" } },
  { tool: "browser_action", args: { 
    action: "type", 
    selector: "#twotabsearchtextbox",
    value: "laptop" 
  }},
  { tool: "browser_action", args: { 
    action: "click", 
    selector: "#nav-search-submit-button" 
  }},
  { tool: "browser_action", args: { action: "wait", value: "2000" } },
  { tool: "browser_action", args: { action: "screenshot" } },
  { tool: "browser_close", args: {} }
];
```

## Integration with MCP System

The Puppeteer tools are fully integrated with the MCP system, meaning:

- **Automatic tool selection**: LLM chooses appropriate browser tools based on user requests
- **Session persistence**: Browser sessions persist across multiple tool calls
- **Error handling**: Comprehensive error reporting and recovery
- **Security**: Respects system security settings and restrictions
- **Logging**: Full activity logging for debugging and monitoring

This makes browser automation accessible through natural language requests, allowing users to simply ask for web interactions without needing to know the technical details of Puppeteer or browser automation.