version: '3.8'

services:
  gemini-cli-wrapper:
    container_name: gemini-cli-wrapper
    build: .
    ports:
      - "8010:8010"
    environment:
      - NODE_ENV=production
      - PORT=8010
      # Set these in your .env file or override here
      - API_KEY=${API_KEY}
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - GEMINI_USE_OAUTH=${GEMINI_USE_OAUTH:-false}
    volumes:
      - gemini_auth:/root/.config/gemini-cli
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8010/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - gemini-net

volumes:
  gemini_auth:

networks:
  gemini-net: