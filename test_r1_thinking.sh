#!/bin/bash

echo "🧠 Testing R1 Style Thinking with Gemini 2.5 Pro"
echo "=================================================="

# Test non-streaming thinking
echo "📝 Testing non-streaming R1 thinking..."
curl -X POST http://217.142.186.49:8010/v1/thinking/test \
  -H "Content-Type: application/json" \
  -d '{
    "message": "What is the capital of France and why is it important?",
    "model": "gemini-2.5-pro",
    "stream": false
  }' | jq -r '.choices[0].message.content'

echo -e "\n\n🌊 Testing streaming R1 thinking..."
echo "Note: Streaming will show <thinking> tags with reasoning process"

# Test streaming thinking
curl -X POST http://217.142.186.49:8010/v1/thinking/test \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Solve this math problem: What is 15 * 23?",
    "model": "gemini-2.5-pro", 
    "stream": true
  }'

echo -e "\n\n✅ R1 Style Thinking Test Complete!"