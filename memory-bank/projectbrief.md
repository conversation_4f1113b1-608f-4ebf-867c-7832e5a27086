# Project Brief: Gemini CLI Wrapper

## Overview
Gemini CLI Wrapper là một service wrapper cho Google Gemini CLI, cung cấp REST API tương thích với OpenAI để dễ dàng tích hợp Gemini vào các ứng dụng khác. Dự án này giúp bridge giữa Gemini CLI và các ứng dụng cần giao tiếp qua REST API.

## Core Requirements
1. **API Compatibility**: Cung cấp API endpoints tương thích với OpenAI API
2. **Authentication**: Hỗ trợ cả API key và OAuth authentication
3. **Tool Integration**: Hỗ trợ tool calling và context management
4. **Streaming**: Hỗ trợ streaming responses
5. **Session Management**: Duy trì context và memory giữa các requests
6. **Security**: Đảm bảo an toàn trong authentication và command execution
7. **Extensibility**: Dễ dàng thêm tools và tính năng mới

## Technical Constraints
- Yêu cầu Node.js v20.0.0 trở lên (bắt buộc cho Gemini CLI)
- Sử dụng ES modules
- Phải hoạt động trên các nền tảng khác nhau (Linux, macOS, Windows)

## Success Criteria
- API hoạt động ổn định và phản hồi nhanh
- Tương thích với các ứng dụng đã tích hợp OpenAI API
- Documentation đầy đủ và dễ hiểu
- Dễ dàng cài đặt và cấu hình
- Hỗ trợ tất cả các tính năng chính của Gemini CLI 