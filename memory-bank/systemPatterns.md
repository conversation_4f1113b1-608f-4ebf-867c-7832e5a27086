# System Patterns: Gemini CLI Wrapper

## Architecture Overview

```mermaid
flowchart TD
    Client[Client Applications] --> API[REST API Layer]
    API --> Auth[Authentication Middleware]
    Auth --> Provider[Gemini CLI Provider]
    Provider --> CLI[Gemini CLI]
    Provider --> MCP[MCP Handler]
    MCP --> Tools[Tool Registry]
    Tools --> WebSearch[Web Search]
    Tools --> FileOps[File Operations]
    Tools --> CmdExec[Command Execution]
    Tools --> CodeAnalysis[Code Analysis]
    Tools --> Memory[Memory Store]
```

## Key Components

### REST API Layer
- Express.js server cung cấp các endpoints tương thích với OpenAI API
- X<PERSON> lý requests và responses theo chuẩn OpenAI
- Hỗ trợ streaming responses qua Server-Sent Events (SSE)

### Authentication Middleware
- Hỗ trợ cả API key và OAuth authentication
- Kiểm tra và xác thực mỗi request
- Bypass authentication cho health check endpoints

### Gemini CLI Provider
- Wrapper cho Gemini CLI
- Chuyển đổi giữa API requests và Gemini CLI commands
- Xử lý streaming và non-streaming responses
- Quản lý phiên và context
- Tích hợp với thinking handler (hiện tại bị tắt để tiết kiệm token)

### MCP (Model Context Protocol) Handler
- Quản lý tools và tool calling
- Duy trì context và memory giữa các requests
- Định dạng tools theo chuẩn OpenAI function calling

### Tool Registry
- Đăng ký và quản lý các tools có sẵn
- Cung cấp interface thống nhất để gọi tools
- Xử lý kết quả từ tools và trả về cho provider

## Design Patterns

### Provider Pattern
- GeminiCLIProvider đóng gói logic tương tác với Gemini CLI
- Cung cấp interface thống nhất để tương tác với model

### Middleware Pattern
- Authentication và error handling được triển khai dưới dạng middleware
- Dễ dàng thêm/bớt middleware khi cần

### Observer Pattern
- Sử dụng EventEmitter để xử lý streaming responses
- Cho phép các components khác nhau lắng nghe và phản ứng với events

### Factory Pattern
- Tool Registry sử dụng factory pattern để tạo và quản lý các tools
- Dễ dàng thêm tools mới mà không cần thay đổi code hiện có

### Adapter Pattern
- Chuyển đổi giữa định dạng OpenAI API và Gemini CLI
- Cho phép các ứng dụng hiện có dễ dàng chuyển sang sử dụng Gemini

## Data Flow

```mermaid
sequenceDiagram
    Client->>+API: POST /chat/completions
    API->>+Auth: Validate request
    Auth->>+Provider: Forward request
    Provider->>+MCP: Process tools
    MCP->>+Tools: Execute tools
    Tools->>-MCP: Return results
    MCP->>-Provider: Enhanced messages
    Provider->>+CLI: Execute Gemini command
    CLI->>-Provider: Response
    Provider->>-API: Format response
    API->>-Client: JSON response
```

## Error Handling
- Middleware xử lý lỗi tập trung
- Logging chi tiết với các cấp độ khác nhau
- Fallback mechanisms khi Gemini CLI gặp lỗi

## Scalability Considerations
- Stateless design cho phép horizontal scaling
- Session data có thể được lưu trữ trong external store
- Tool registry có thể mở rộng với plugins 