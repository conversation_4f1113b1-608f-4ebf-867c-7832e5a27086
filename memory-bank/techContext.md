# Technical Context: Gemini CLI Wrapper

## Technologies Used

### Core Technologies
- **Node.js**: v20.0.0 trở lên (b<PERSON><PERSON> buộc cho Gemini CLI)
- **Express.js**: Web framework để xây dựng REST API
- **Gemini CLI**: Command-line interface cho Google Gemini models
- **ES Modules**: Sử dụng ES modules thay vì CommonJS

### Dependencies
- **@google/gemini-cli**: Gemini CLI package
- **express**: Web server framework
- **cors**: Cross-Origin Resource Sharing middleware
- **dotenv**: Environment variable management
- **axios**: HTTP client cho web search và external API calls
- **ws**: WebSockets cho real-time communication

### Development Tools
- **TypeScript**: Type definitions và development
- **Jest**: Testing framework
- **Nodemon**: Auto-reloading trong development

## Development Setup

### Prerequisites
- Node.js v20.0.0 trở lên
- npm hoặc yarn
- Gemini CLI được cài đặt và cấu hình

### Environment Variables
- **PORT**: Port để chạy server (mặc định: 8010)
- **NODE_ENV**: Environment (development/production)
- **API_KEY**: API key cho authentication
- **GEMINI_API_KEY**: API key cho Gemini (nếu không sử dụng OAuth)
- **GEMINI_USE_OAUTH**: Flag để sử dụng OAuth thay vì API key
- **LOG_LEVEL**: Level cho logging (debug/info/warn/error)
- **ENABLE_FAKE_THINKING**: Bật/tắt fake thinking feature (mặc định: false)
- **STREAM_THINKING_AS_CONTENT**: Stream thinking với R1 style tags (mặc định: false)

## Technical Constraints

### Authentication
- Hỗ trợ cả API key và OAuth authentication
- API key được cấu hình trong environment variables
- OAuth sử dụng Google OAuth flow thông qua Gemini CLI

### Security
- CORS được cấu hình để hạn chế cross-origin requests
- Command execution được sandbox để đảm bảo an toàn
- File operations bị giới hạn trong project directory

### Performance
- Streaming responses để giảm latency
- Caching cho các responses phổ biến
- Session cleanup để giảm memory usage

### Compatibility
- API tương thích với OpenAI API format
- Hỗ trợ cả streaming và non-streaming responses
- Tool calling tương thích với OpenAI function calling

## Deployment

### Docker
- Dockerfile và docker-compose.yml được cung cấp
- Multi-stage build để giảm image size
- Volume mounting cho persistent data

### Hosting Options
- Có thể deploy trên cloud platforms (AWS, GCP, Azure)
- Hỗ trợ containerization với Docker
- Có thể chạy trực tiếp trên server với Node.js

## Integration Points

### Client Applications
- REST API endpoints tương thích với OpenAI
- WebSocket connections cho real-time interactions
- Server-Sent Events (SSE) cho streaming responses

### External Services
- Gemini API thông qua Gemini CLI
- Web search APIs cho tool calling
- File system cho local storage và file operations

## Monitoring & Logging
- Structured logging với timestamp và log levels
- Health check endpoint để monitoring
- Error tracking và reporting
- Performance metrics collection 