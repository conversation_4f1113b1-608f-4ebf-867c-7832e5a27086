# Product Context: Gemini CLI Wrapper

## Why This Project Exists
Gemini CLI là một công cụ mạnh mẽ để tương tác với các model Gemini của Google, nhưng nó chỉ cung cấp giao diện dòng lệnh. Nhiều ứng dụng và dịch vụ đã được xây dựng để tương tác với OpenAI API, nhưng không thể dễ dàng chuyển sang sử dụng Gemini. Gemini CLI Wrapper giải quyết vấn đề này bằng cách cung cấp REST API tương thích với OpenAI, cho phép các ứng dụng hiện có chuyển sang sử dụng Gemini mà không cần thay đổi nhiều code.

## Problems It Solves
1. **Kh<PERSON> khăn trong việc tích hợp**: Gemini CLI chỉ có giao diện dòng lệnh, kh<PERSON> tích hợp vào các ứng dụng web/mobile
2. **Không tương thích API**: Các ứng dụng đã tích hợp OpenAI API không thể dễ dàng chuyển sang Gemini
3. **Thiếu session management**: Gemini CLI không có sẵn cơ chế quản lý phiên và context
4. **Hạn chế trong tool calling**: Cần một cơ chế linh hoạt hơn để gọi và quản lý tools
5. **Khó khăn trong authentication**: Cần một cơ chế authentication linh hoạt cho các môi trường khác nhau

## How It Should Work
1. **Đơn giản trong cài đặt**: Người dùng có thể cài đặt và chạy service với vài lệnh đơn giản
2. **Tương thích API**: Cung cấp API endpoints tương thích với OpenAI để dễ dàng chuyển đổi
3. **Linh hoạt trong cấu hình**: Cho phép cấu hình qua environment variables hoặc file cấu hình
4. **Tích hợp tools**: Hỗ trợ các tools phổ biến như web search, file operations, command execution
5. **Quản lý phiên**: Duy trì context và memory giữa các requests
6. **Streaming**: Hỗ trợ streaming responses cho trải nghiệm real-time
7. **Tối ưu token**: Không sử dụng fake thinking để tiết kiệm token và chi phí

## User Experience Goals
1. **Dễ sử dụng**: API dễ hiểu và sử dụng, documentation đầy đủ
2. **Hiệu suất cao**: Phản hồi nhanh, xử lý hiệu quả các requests
3. **Ổn định**: Service hoạt động ổn định, xử lý lỗi tốt
4. **Bảo mật**: Đảm bảo an toàn trong authentication và xử lý dữ liệu
5. **Mở rộng**: Dễ dàng thêm tools và tính năng mới

## Target Users
1. **Developers**: Muốn tích hợp Gemini vào ứng dụng của họ
2. **Data Scientists**: Cần một API để tương tác với Gemini từ notebooks và scripts
3. **Businesses**: Muốn chuyển từ OpenAI sang Gemini mà không cần thay đổi nhiều code
4. **Researchers**: Cần một cách đơn giản để thử nghiệm và so sánh các model Gemini 