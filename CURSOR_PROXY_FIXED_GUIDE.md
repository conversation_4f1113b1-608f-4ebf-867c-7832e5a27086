# 🔧 Cursor Proxy - Fixed Setup Guide

## 🚨 Issues Encountered & Solutions

### Problem 1: ngrok Authentication Required
- **Error**: `authentication failed: Usage of ngrok requires a verified account and authtoken`
- **Solution**: Need free ngrok account or use alternative

### Problem 2: Cloudflare ARM64 Architecture Mismatch  
- **Error**: `package architecture (amd64) does not match system (arm64)`
- **Solution**: Use ARM64-specific cloudflared binary

## 🎯 Working Solutions (Choose One)

### Option 1: Simple Cloudflare Tunnel (Recommended)
```bash
./setup_simple_tunnel.sh
```
This downloads the correct ARM64 cloudflared binary and starts the tunnel.

### Option 2: Manual Cloudflare Setup
```bash
# Download ARM64 version
wget -O cloudflared-arm64 https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-linux-arm64
chmod +x cloudflared-arm64

# Start tunnel
./cloudflared-arm64 tunnel --url http://localhost:3001
```

### Option 3: Setup ngrok with Free Account
1. Sign up at https://dashboard.ngrok.com/signup
2. Get authtoken from https://dashboard.ngrok.com/get-started/your-authtoken
3. Run:
   ```bash
   ngrok config add-authtoken YOUR_TOKEN
   ngrok http 3001
   ```

### Option 4: Use Phone Hotspot (Local Network)
1. Connect computer to phone's hotspot
2. Get your IP: `ip addr show | grep 'inet '`
3. Use `http://YOUR_IP:3001/v1` in Cursor

## 🚀 Quick Start (Recommended)

1. **Keep proxy running** (Terminal 1):
   ```bash
   node cursor_proxy_server.cjs
   ```

2. **Start tunnel** (new terminal):
   ```bash
   ./setup_simple_tunnel.sh
   ```

3. **Copy the tunnel URL** (e.g., `https://abc-def-ghi.trycloudflare.com`)

4. **Configure Cursor**:
   - Base URL: `https://your-tunnel-url.com/v1`
   - API Key: Your JWT token
   - Model: `anthropic/claude-sonnet-4`

## ✅ Test Your Setup

### 1. Test Tunnel Health
```bash
curl https://your-tunnel-url.com/health
# Expected: {"status":"ok","timestamp":"..."}
```

### 2. Test Models Endpoint
```bash
curl https://your-tunnel-url.com/v1/models \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 3. Test Chat Completion
```bash
curl -X POST https://your-tunnel-url.com/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "model": "anthropic/claude-sonnet-4",
    "messages": [{"role": "user", "content": "Hello!"}],
    "max_tokens": 100
  }'
```

## 🔍 Troubleshooting

### If tunnel fails to start:
1. Check proxy is running: `curl http://localhost:3001/health`
2. Try different tunnel method
3. Check firewall settings

### If Cursor still can't connect:
1. Verify tunnel URL is accessible from browser
2. Check Cursor's base URL format: `https://tunnel-url.com/v1` (with `/v1`)
3. Ensure JWT token is correct

### Architecture Issues:
- Your system is ARM64, so use ARM64-specific binaries
- Avoid AMD64 packages that won't work

## 📝 Current Status

✅ Proxy server working  
✅ Direct API tests successful  
✅ ARM64 compatibility fixed  
✅ Multiple tunnel options provided  
🎯 Ready for Cursor configuration

The proxy will translate Cursor's OpenAI API calls to your OpenRouter endpoint seamlessly!