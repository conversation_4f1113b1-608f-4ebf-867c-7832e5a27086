#!/bin/bash

echo "🚀 Simple Tunnel Setup for Cursor Proxy"
echo "Using SSH tunnel as a reliable alternative"
echo ""

# Check if proxy server is running
if ! curl -s http://localhost:3001/health > /dev/null; then
    echo "❌ Proxy server is not running on port 3001"
    echo "Please start the proxy server first:"
    echo "  node cursor_proxy_server.cjs"
    exit 1
fi

echo "✅ Proxy server is running on port 3001"
echo ""

echo "🔧 Alternative Solutions:"
echo ""
echo "1. 📱 Use your phone's hotspot:"
echo "   - Connect your computer to phone hotspot"
echo "   - Get your computer's IP: ip addr show | grep 'inet '"
echo "   - Use http://YOUR_IP:3001/v1 in Cursor"
echo ""
echo "2. 🌐 Manual Cloudflare setup:"
echo "   - Download ARM64 version manually:"
echo "   wget https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-linux-arm64"
echo "   chmod +x cloudflared-linux-arm64"
echo "   ./cloudflared-linux-arm64 tunnel --url http://localhost:3001"
echo ""
echo "3. 🔑 Setup ngrok with auth (free account):"
echo "   - Sign up at https://dashboard.ngrok.com/signup"
echo "   - Get your authtoken from https://dashboard.ngrok.com/get-started/your-authtoken"
echo "   - Run: ngrok config add-authtoken YOUR_TOKEN"
echo "   - Run: ngrok http 3001"
echo ""

echo "🎯 Quick Test - Manual Cloudflare:"
echo "Downloading cloudflared ARM64..."

# Download ARM64 version directly
if [ ! -f "cloudflared-arm64" ]; then
    wget -O cloudflared-arm64 https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-linux-arm64
    chmod +x cloudflared-arm64
fi

echo ""
echo "✅ Downloaded cloudflared-arm64"
echo ""
echo "🚀 Starting tunnel with manual cloudflared..."
echo "⚠️  Keep this terminal open while using Cursor"
echo ""

./cloudflared-arm64 tunnel --url http://localhost:3001