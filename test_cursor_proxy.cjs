#!/usr/bin/env node

const fetch = require('node-fetch');

const PROXY_URL = 'http://localhost:3001';

async function testProxy() {
    console.log('🧪 Testing Cursor Proxy Server...');
    console.log(`📡 Proxy URL: ${PROXY_URL}`);
    console.log('');

    try {
        // Test 1: Health check
        console.log('🏥 Test 1: Health check...');
        const healthResponse = await fetch(`${PROXY_URL}/health`);
        if (healthResponse.ok) {
            const health = await healthResponse.json();
            console.log('✅ Health check passed:', health);
        } else {
            console.log('❌ Health check failed');
            return;
        }

        // Test 2: Models endpoint
        console.log('\n📋 Test 2: Models endpoint...');
        const modelsResponse = await fetch(`${PROXY_URL}/v1/models`);
        if (modelsResponse.ok) {
            const models = await modelsResponse.json();
            console.log(`✅ Models endpoint working: ${models.data.length} models`);
            
            // Find Claude Sonnet 4
            const claudeSonnet4 = models.data.find(m => m.id === 'anthropic/claude-sonnet-4');
            if (claudeSonnet4) {
                console.log('🎯 Found Claude Sonnet 4:', claudeSonnet4.id);
            } else {
                console.log('⚠️  Claude Sonnet 4 not found in models list');
            }
        } else {
            console.log('❌ Models endpoint failed');
            return;
        }

        // Test 3: Chat completion
        console.log('\n💬 Test 3: Chat completion...');
        const chatResponse = await fetch(`${PROXY_URL}/v1/chat/completions`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer test-key'
            },
            body: JSON.stringify({
                model: 'anthropic/claude-sonnet-4',
                messages: [
                    {
                        role: 'user',
                        content: 'Hello! Please respond with just "Proxy working!" to confirm the connection.'
                    }
                ],
                max_tokens: 50,
                temperature: 0.7
            })
        });

        if (chatResponse.ok) {
            const chat = await chatResponse.json();
            console.log('✅ Chat completion working!');
            console.log('🤖 Response:', chat.choices[0].message.content);
            console.log('📊 Usage:', chat.usage);
        } else {
            const error = await chatResponse.text();
            console.log('❌ Chat completion failed:', error);
        }

        console.log('\n🎉 All tests completed!');
        console.log('\n📋 Cursor Configuration:');
        console.log('  API Base URL: http://localhost:3001/v1');
        console.log('  API Key: any-key-works');
        console.log('  Model: anthropic/claude-sonnet-4');

    } catch (error) {
        console.log('❌ Test failed:', error.message);
        console.log('\n💡 Make sure proxy server is running:');
        console.log('  ./start_cursor_proxy.sh');
    }
}

testProxy();