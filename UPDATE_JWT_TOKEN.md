# Cập Nhật JWT Token Mới

## Vấn đề
JWT Token hiện tại đã hết hạn:
- Token hết hạn: `1737634563` (23/01/2025)
- Thời gian hiện tại: `1752206160` (11/07/2025)

## Cách Lấy Token Mới

1. **Truy cập**: https://kilocode.ai/api/openrouter
2. **Đăng nhập** với tài khoản của bạn
3. **Copy JWT token mới** từ trang web

## Cách Cập Nhật Token

### Phương pháp 1: Cập nhật script
```bash
# Sửa file start_cursor_proxy_with_token.sh
nano start_cursor_proxy_with_token.sh

# Thay thế JWT token cũ bằng token mới
export JWT_TOKEN="TOKEN_MỚI_CỦA_BẠN"
```

### Phương pháp 2: Export trực tiếp
```bash
# Kill proxy hiện tại
pkill -f "node cursor_proxy_server.cjs"

# Export token mới và start proxy
export JWT_TOKEN="TOKEN_MỚI_CỦA_BẠN" && node cursor_proxy_server.cjs
```

## Kiểm Tra Token Mới
```bash
# Decode token để xem thời gian hết hạn
echo "TOKEN_MỚI" | cut -d'.' -f2 | base64 -d 2>/dev/null | jq .

# Test endpoint
curl https://mali-cents-fall-sbjct.trycloudflare.com/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer kilo-claude-2025" \
  -d '{
    "model": "cursordev",
    "messages": [{"role": "user", "content": "Test"}],
    "max_tokens": 50
  }'
```

## Lưu Ý
- Token thường có thời hạn khoảng 14 ngày
- Cần cập nhật token định kỳ khi hết hạn
- Model `cursordev` đã được cấu hình với tool support