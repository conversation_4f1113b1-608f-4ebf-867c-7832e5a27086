#!/bin/bash

# Màu sắc cho output
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Lấy IP của server
SERVER_IP=$(hostname -I | awk '{print $1}')
PORT=8010
BASE_URL="http://${SERVER_IP}:${PORT}"

echo -e "${BLUE}Testing Gemini CLI Wrapper API${NC}"
echo -e "${BLUE}Base URL: ${BASE_URL}${NC}"
echo ""

# Test health endpoint
echo -e "${BLUE}Testing health endpoint...${NC}"
HEALTH_RESPONSE=$(curl -s "${BASE_URL}/health")
if [[ $HEALTH_RESPONSE == *"healthy"* ]]; then
  echo -e "${GREEN}✓ Health check passed${NC}"
else
  echo -e "${RED}✗ Health check failed${NC}"
  echo $HEALTH_RESPONSE
fi
echo ""

# Test models endpoint
echo -e "${BLUE}Testing models endpoint...${NC}"
MODELS_RESPONSE=$(curl -s "${BASE_URL}/models")
if [[ $MODELS_RESPONSE == *"gemini"* ]]; then
  echo -e "${GREEN}✓ Models endpoint working${NC}"
  echo "Available models:"
  echo $MODELS_RESPONSE | grep -o '"id":"[^"]*"' | cut -d'"' -f4
else
  echo -e "${RED}✗ Models endpoint failed${NC}"
  echo $MODELS_RESPONSE
fi
echo ""

# Test chat completion
echo -e "${BLUE}Testing chat completion...${NC}"
CHAT_RESPONSE=$(curl -s -X POST "${BASE_URL}/chat/completions" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gemini-1.5-pro",
    "messages": [
      {"role": "user", "content": "Say hello in Vietnamese"}
    ]
  }')

if [[ $CHAT_RESPONSE == *"content"* ]]; then
  echo -e "${GREEN}✓ Chat completion working${NC}"
  echo "Response:"
  echo $CHAT_RESPONSE | grep -o '"content":"[^"]*"' | cut -d'"' -f4
else
  echo -e "${RED}✗ Chat completion failed${NC}"
  echo $CHAT_RESPONSE
fi
echo ""

# Test MCP info
echo -e "${BLUE}Testing MCP info...${NC}"
MCP_RESPONSE=$(curl -s "${BASE_URL}/mcp/info")
if [[ $MCP_RESPONSE == *"name"* ]]; then
  echo -e "${GREEN}✓ MCP info working${NC}"
else
  echo -e "${RED}✗ MCP info failed${NC}"
  echo $MCP_RESPONSE
fi
echo ""

echo -e "${BLUE}API test completed${NC}" 