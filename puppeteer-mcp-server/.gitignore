# Dependencies
node_modules/

# Build output
build/
dist/
*.js
*.js.map

# Memory Bank
cline_docs/

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
.DS_Store

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.*.local

# Test coverage
coverage/

# Temporary files
*.tmp
*.temp
.clinerules*
.roo*
memory-bank/