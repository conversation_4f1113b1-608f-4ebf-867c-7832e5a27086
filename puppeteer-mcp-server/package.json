{"name": "puppeteer-mcp-server", "version": "0.7.2", "description": "Experimental MCP server for browser automation using P<PERSON>peteer (inspired by @modelcontextprotocol/server-puppeteer)", "license": "MIT", "author": "<PERSON><PERSON>", "homepage": "https://github.com/merajmehrabi/puppeteer-mcp-server", "bugs": "https://github.com/merajmehrabi/puppeteer-mcp-server/issues", "repository": {"type": "git", "url": "https://github.com/merajmehrabi/puppeteer-mcp-server.git"}, "type": "module", "bin": {"mcp-server-puppeteer": "dist/index.js"}, "files": ["dist"], "scripts": {"build": "tsc && shx chmod +x dist/*.js", "prepare": "npm run build", "watch": "tsc --watch"}, "dependencies": {"@modelcontextprotocol/sdk": "1.0.1", "puppeteer": "^23.4.0", "winston": "^3.11.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@types/node": "^20.11.17", "@types/winston": "^2.4.4", "shx": "^0.3.4", "typescript": "^5.6.2"}}