# Generated by https://smithery.ai. See: https://smithery.ai/docs/config#dockerfile
# Use a Node.js image for building the TypeScript project
FROM node:18-alpine AS builder

# Set the working directory inside the container
WORKDIR /app

# Copy package.json and package-lock.json to install dependencies
COPY package.json tsconfig.json ./

# Install dependencies without executing any scripts
RUN npm install --ignore-scripts

# Copy the entire source code into the container
COPY . .

# Build the TypeScript project
RUN npm run build

# Use a lightweight Node.js image for the final build
FROM node:18-alpine

# Set the working directory
WORKDIR /app

# Copy the built files from the builder stage
COPY --from=builder /app/dist /app/dist
COPY --from=builder /app/package.json /app/package.json
COPY --from=builder /app/package-lock.json /app/package-lock.json

# Install production dependencies
RUN npm ci --omit=dev

# Expose the port on which the server runs (optional, specify if known)
# EXPOSE 3000

# Set the entrypoint command to run the server
ENTRYPOINT ["node", "dist/index.js"]
