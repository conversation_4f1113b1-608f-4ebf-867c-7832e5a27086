# Contributing to Puppeteer MCP Server

Thank you for your interest in contributing to the Puppeteer MCP Server! This is an experimental implementation inspired by [@modelcontextprotocol/server-puppeteer](https://github.com/modelcontextprotocol/servers). This document provides guidelines and instructions for contributing to the project.

## Development Setup

### Prerequisites
- Node.js (Latest LTS version recommended)
- npm or yarn
- TypeScript knowledge
- Understanding of the Model Context Protocol (MCP)
- Basic familiarity with Puppeteer

### Getting Started
1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```
3. Build the project:
   ```bash
   npm run build
   ```

## Project Structure
```
/
├── src/
│   ├── config/
│   │   ├── logger.ts        # Winston logger configuration
│   │   └── browser.ts       # Browser launch configurations
│   ├── tools/
│   │   ├── definitions.ts   # Tool definitions
│   │   └── handlers.ts      # Tool handler implementations
│   ├── browser/
│   │   └── connection.ts    # Browser connection management
│   ├── types/
│   │   └── global.ts        # Global type declarations
│   ├── resources/
│   │   └── handlers.ts      # Resource request handlers
│   └── server.ts           # Server setup and initialization
├── index.ts                # Entry point
├── package.json           # Project configuration
└── tsconfig.json         # TypeScript configuration
```

## Coding Standards

### General Guidelines
1. Write code in TypeScript
2. Follow existing patterns in the codebase
3. Use async/await for asynchronous operations
4. Include comprehensive error handling
5. Add appropriate TypeScript types/interfaces
6. Document complex logic with comments

### Tool Implementation
- Define tools in `src/tools/definitions.ts`
- Implement handlers in `src/tools/handlers.ts`
- Follow the declarative tool definition pattern
- Include input schema validation
- Provide detailed error messages

### Resource Implementation
- Use URI-based resource identification
- Implement handlers in `src/resources/handlers.ts`
- Follow existing resource patterns (console logs, screenshots)
- Include proper MIME type specifications

### Error Handling
- Use try-catch blocks for browser operations
- Provide structured error responses
- Include user-friendly error messages
- Add troubleshooting steps in error messages
- Implement graceful degradation where appropriate

## Pull Request Process

1. Fork the repository
2. Create a feature branch from `main`
3. Make your changes following the coding standards
4. Update documentation as needed
5. Ensure all tests pass
6. Submit a pull request with:
   - Clear description of changes
   - Link to related issue(s)
   - Screenshots/examples if applicable
   - List of testing steps

### PR Review Checklist
- [ ] Code follows project standards
- [ ] Documentation is updated
- [ ] Tests are included
- [ ] Error handling is implemented
- [ ] No unnecessary dependencies added
- [ ] Commit messages are clear and descriptive

## Testing Guidelines

1. Write tests for new features
2. Include both success and error cases
3. Test browser interactions thoroughly
4. Verify proper error handling
5. Test in both Docker and NPX environments
6. Check console output and screenshots
7. Validate resource cleanup

## Issue Reporting

When reporting issues, please include:

1. Description of the problem
2. Steps to reproduce
3. Expected behavior
4. Actual behavior
5. Environment details:
   - Node.js version
   - Deployment method (Docker/NPX)
   - Browser version
   - Operating system
6. Relevant logs or screenshots

## Development Constraints

### Browser Environment
- Docker: Uses headless mode with security flags
- NPX: Uses GUI mode with enhanced settings
- Active Tab Mode: Requires remote debugging port

### Resource Limitations
- In-memory storage only
- No persistent data between sessions
- Single browser instance per server
- Single page context

### Security Considerations
- No file system access
- Sandboxed browser environment
- Limited network access
- No persistent cookies/storage
- Local-only remote debugging

## Questions or Need Help?

Feel free to open an issue for:
- Feature requests
- Bug reports
- Documentation improvements
- General questions

## License

By contributing to this project, you agree that your contributions will be licensed under the project's license.
