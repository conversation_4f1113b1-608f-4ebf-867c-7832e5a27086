#!/bin/bash

echo "🚀 Setting up Open WebUI Integration for Gemini CLI Wrapper"
echo "=========================================================="

# Check if Open WebUI is running
echo "📡 Checking Open WebUI connection..."
if curl -s http://localhost:3000/health > /dev/null 2>&1; then
    echo "✅ Open WebUI is running on http://localhost:3000"
else
    echo "❌ Open WebUI is not running on http://localhost:3000"
    echo "💡 To start Open WebUI:"
    echo "   docker run -d -p 3000:8080 --name open-webui ghcr.io/open-webui/open-webui:main"
    echo ""
fi

# Check environment variables
echo "🔧 Checking environment configuration..."

if [ -z "$OPEN_WEBUI_API_KEY" ]; then
    echo "⚠️  OPEN_WEBUI_API_KEY is not set"
    echo "💡 To get an API key:"
    echo "   1. Open http://localhost:3000"
    echo "   2. Go to Settings → Account → API Keys"
    echo "   3. Generate a new API key"
    echo "   4. Export it: export OPEN_WEBUI_API_KEY='your-api-key'"
    echo ""
else
    echo "✅ OPEN_WEBUI_API_KEY is configured"
fi

if [ "$ENABLE_BROWSER_AUTOMATION" = "true" ]; then
    echo "✅ ENABLE_BROWSER_AUTOMATION is enabled"
else
    echo "⚠️  ENABLE_BROWSER_AUTOMATION is not enabled"
    echo "💡 To enable: export ENABLE_BROWSER_AUTOMATION=true"
    echo ""
fi

# Check Puppeteer installation
echo "🎭 Checking Puppeteer installation..."
if npm list puppeteer > /dev/null 2>&1; then
    echo "✅ Puppeteer is installed"
else
    echo "❌ Puppeteer is not installed"
    echo "💡 To install: npm install puppeteer"
    echo ""
fi

# Check Chromium installation (Linux)
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    echo "🌐 Checking Chromium installation..."
    if command -v chromium-browser > /dev/null 2>&1; then
        echo "✅ Chromium is installed"
    elif command -v chromium > /dev/null 2>&1; then
        echo "✅ Chromium is installed"
    elif command -v google-chrome > /dev/null 2>&1; then
        echo "✅ Google Chrome is installed"
    else
        echo "❌ No Chromium/Chrome found"
        echo "💡 To install: sudo apt-get install chromium-browser"
        echo ""
    fi
fi

# Test Open WebUI tools
echo "🧪 Testing Open WebUI integration..."
node -e "
import { toolRegistry } from './src/tools/toolRegistry.js';

async function testIntegration() {
  console.log('Testing openwebui_get_models...');
  try {
    const result = await toolRegistry.executeTool('openwebui_get_models', {});
    if (result.success) {
      console.log('✅ API connection successful');
      console.log('📊 Found', result.count, 'models');
    } else {
      console.log('❌ API test failed:', result.error);
      if (result.error.includes('403') || result.error.includes('401')) {
        console.log('💡 This usually means API key is missing or invalid');
      }
    }
  } catch (error) {
    console.log('❌ Test error:', error.message);
  }
}

testIntegration();
" 2>/dev/null

echo ""
echo "📋 Summary:"
echo "==========="
echo "Available Open WebUI tools:"
echo "- openwebui_web_search: Search the web"
echo "- openwebui_get_models: Get available models"
echo "- openwebui_create_chat: Create new chat"
echo "- openwebui_send_message: Send messages"
echo "- openwebui_get_conversations: Get chat history"
echo "- openwebui_upload_file: Upload files"
echo "- openwebui_browser_action: Browser automation"
echo ""
echo "📖 For detailed usage, see: OPEN_WEBUI_INTEGRATION_GUIDE.md"
echo ""
echo "🎯 Quick test command:"
echo "curl -X POST http://localhost:8010/mcp/tools/execute \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -d '{\"tool_name\": \"openwebui_get_models\", \"args\": {}}'"