# Gemini CLI Wrapper Environment Configuration
# Generated on Wed Jul  9 06:15:48 UTC 2025

# Basic Configuration
PORT=8011
NODE_ENV=development

# API Keys for AI Services
GEMINI_API_KEY=your-gemini-api-key-here
CLAUDE_API_KEY=your-claude-api-key-here

# RBAC API Keys - KEEP THESE SECURE!
ADMIN_API_KEY=fa3c4ca5815a7b9e6235514a56d527cdfc049441c485b75c94262f107ad559b6
USER_API_KEY=5f6e775994045b5c476c12545dc387798a46c6a0f91a620d38176451f41ae8a0
GUEST_API_KEY=9807dd41f116de8514ce2653ba601a2638d7cf01be71f3db1db6397e765e1c85

# External Client Default Roles
EXTERNAL_CLIENT_ROLE=user
VSCODE_CLIENT_ROLE=user
CLINE_CLIENT_ROLE=user

# API Key to Role Mappings (JSON format)
API_KEY_ROLE_MAPPINGS={"fa3c4ca5815a7b9e6235514a56d527cdfc049441c485b75c94262f107ad559b6":"admin","5f6e775994045b5c476c12545dc387798a46c6a0f91a620d38176451f41ae8a0":"user","9807dd41f116de8514ce2653ba601a2638d7cf01be71f3db1db6397e765e1c85":"guest"}

# Security Settings
GEMINI_UNRESTRICTED_MODE=false

# Open WebUI Integration
OPENWEBUI_BASE_URL=http://localhost:3000
OPENWEBUI_API_KEY=your-openwebui-api-key

# Logging Level
LOG_LEVEL=info
