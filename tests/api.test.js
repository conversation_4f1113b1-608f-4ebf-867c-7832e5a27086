import request from 'supertest';
import app from '../src/index.js';

describe('Gemini CLI Wrapper API', () => {
  const API_KEY = 'test-api-key';
  
  beforeAll(() => {
    process.env.API_KEY = API_KEY;
    process.env.NODE_ENV = 'test';
  });

  describe('Health Check', () => {
    test('GET /health should return healthy status', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200);

      expect(response.body).toHaveProperty('status', 'healthy');
      expect(response.body).toHaveProperty('timestamp');
    });
  });

  describe('Authentication', () => {
    test('should reject requests without API key', async () => {
      const response = await request(app)
        .get('/models')
        .expect(401);

      expect(response.body).toHaveProperty('error');
    });

    test('should reject requests with invalid API key', async () => {
      const response = await request(app)
        .get('/models')
        .set('Authorization', 'Bearer invalid-key')
        .expect(401);

      expect(response.body).toHaveProperty('error');
    });

    test('should accept requests with valid API key', async () => {
      const response = await request(app)
        .get('/models')
        .set('Authorization', `Bearer ${API_KEY}`)
        .expect(200);

      expect(response.body).toHaveProperty('models');
    });
  });

  describe('Models Endpoint', () => {
    test('GET /models should return available models', async () => {
      const response = await request(app)
        .get('/models')
        .set('Authorization', `Bearer ${API_KEY}`)
        .expect(200);

      expect(response.body).toHaveProperty('models');
      expect(Array.isArray(response.body.models)).toBe(true);
      expect(response.body.models.length).toBeGreaterThan(0);
      
      // Check model structure
      const model = response.body.models[0];
      expect(model).toHaveProperty('id');
      expect(model).toHaveProperty('name');
      expect(model).toHaveProperty('description');
      expect(model).toHaveProperty('maxTokens');
    });
  });

  describe('Chat Completions', () => {
    test('should validate required fields', async () => {
      const response = await request(app)
        .post('/chat/completions')
        .set('Authorization', `Bearer ${API_KEY}`)
        .send({})
        .expect(500);

      expect(response.body).toHaveProperty('error');
    });

    test('should handle basic chat completion request', async () => {
      const requestBody = {
        model: 'gemini-1.5-pro',
        messages: [
          {
            role: 'user',
            content: 'Hello, this is a test message.'
          }
        ]
      };

      const response = await request(app)
        .post('/chat/completions')
        .set('Authorization', `Bearer ${API_KEY}`)
        .send(requestBody);

      // Note: This might fail in test environment without proper Gemini CLI setup
      // In a real test environment, you'd mock the Gemini CLI calls
      if (response.status === 200) {
        expect(response.body).toHaveProperty('id');
        expect(response.body).toHaveProperty('object', 'chat.completion');
        expect(response.body).toHaveProperty('choices');
        expect(Array.isArray(response.body.choices)).toBe(true);
      }
    }, 30000); // Longer timeout for API calls

    test('should handle streaming requests', async () => {
      const requestBody = {
        model: 'gemini-1.5-pro',
        messages: [
          {
            role: 'user',
            content: 'Count from 1 to 5'
          }
        ],
        stream: true
      };

      const response = await request(app)
        .post('/chat/completions')
        .set('Authorization', `Bearer ${API_KEY}`)
        .send(requestBody);

      if (response.status === 200) {
        expect(response.headers['content-type']).toContain('text/event-stream');
      }
    }, 30000);
  });

  describe('Error Handling', () => {
    test('should handle 404 for unknown endpoints', async () => {
      const response = await request(app)
        .get('/unknown-endpoint')
        .set('Authorization', `Bearer ${API_KEY}`)
        .expect(404);
    });

    test('should handle malformed JSON', async () => {
      const response = await request(app)
        .post('/chat/completions')
        .set('Authorization', `Bearer ${API_KEY}`)
        .set('Content-Type', 'application/json')
        .send('{"invalid": json}')
        .expect(400);
    });
  });
}); 