#!/bin/bash

echo "☁️  Setting up Cloudflare tunnel for Cursor proxy (ARM64 fixed)..."
echo "This is a free alternative to ngrok"
echo ""

# Check if cloudflared is installed
if ! command -v cloudflared &> /dev/null; then
    echo "📦 Installing cloudflared for ARM64..."
    
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux ARM64 - detect architecture
        ARCH=$(uname -m)
        if [[ "$ARCH" == "aarch64" || "$ARCH" == "arm64" ]]; then
            echo "Detected ARM64 architecture"
            wget -q https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-linux-arm64.deb
            sudo dpkg -i cloudflared-linux-arm64.deb
            rm cloudflared-linux-arm64.deb
        else
            echo "Detected x86_64 architecture"
            wget -q https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-linux-amd64.deb
            sudo dpkg -i cloudflared-linux-amd64.deb
            rm cloudflared-linux-amd64.deb
        fi
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        if command -v brew &> /dev/null; then
            brew install cloudflared
        else
            echo "Please install Homebrew first or download cloudflared manually"
            exit 1
        fi
    else
        echo "Please install cloudflared manually from https://developers.cloudflare.com/cloudflare-one/connections/connect-apps/install-and-setup/installation/"
        exit 1
    fi
fi

echo "✅ cloudflared is available"
echo ""

# Check if proxy server is running
if ! curl -s http://localhost:3001/health > /dev/null; then
    echo "❌ Proxy server is not running on port 3001"
    echo "Please start the proxy server first:"
    echo "  node cursor_proxy_server.cjs"
    exit 1
fi

echo "✅ Proxy server is running on port 3001"
echo ""

echo "🚀 Starting Cloudflare tunnel..."
echo "This will create a public URL for your proxy server"
echo ""
echo "⚠️  IMPORTANT: Keep this terminal open while using Cursor"
echo "⚠️  The public URL will change each time you restart the tunnel"
echo ""

# Start cloudflare tunnel
cloudflared tunnel --url http://localhost:3001