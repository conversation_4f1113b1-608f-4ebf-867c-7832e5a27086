#!/bin/bash

# Test External Client RBAC Detection
# Tests how different User-Agent headers are handled by the RBAC system

set -e

BASE_URL="http://localhost:8011"
API_KEY="test-api-key"

echo "🔍 External Client RBAC Detection Test"
echo "======================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test function for external clients
test_external_client() {
    local user_agent=$1
    local expected_role=$2
    local description=$3
    
    echo -e "\n${YELLOW}Testing:${NC} $description"
    echo "User-Agent: $user_agent"
    echo "Expected Role: $expected_role"
    
    response=$(curl -s -w "HTTPSTATUS:%{http_code}" \
        -X GET "$BASE_URL/rbac/status" \
        -H "Authorization: Bearer $API_KEY" \
        -H "User-Agent: $user_agent")
    
    http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    body=$(echo $response | sed -e 's/HTTPSTATUS\:.*//g')
    
    if [ "$http_code" -eq 200 ]; then
        actual_role=$(echo "$body" | jq -r '.current_user.role' 2>/dev/null || echo "unknown")
        if [ "$actual_role" = "$expected_role" ]; then
            echo -e "${GREEN}✅ PASS${NC} - Role: $actual_role"
        else
            echo -e "${RED}❌ FAIL${NC} - Expected: $expected_role, Got: $actual_role"
        fi
        
        # Show permissions
        permissions=$(echo "$body" | jq -r '.current_user.permissions[]' 2>/dev/null | head -3 | tr '\n' ', ' | sed 's/,$//')
        echo -e "${BLUE}📋 Permissions:${NC} $permissions..."
    else
        echo -e "${RED}❌ ERROR${NC} - HTTP $http_code"
        echo "Response: $body"
    fi
}

# Test permission for external client
test_external_permission() {
    local user_agent=$1
    local action=$2
    local expected_allowed=$3
    local description=$4
    
    echo -e "\n${YELLOW}Testing Permission:${NC} $description"
    echo "User-Agent: $user_agent | Action: $action | Expected: $expected_allowed"
    
    response=$(curl -s -w "HTTPSTATUS:%{http_code}" \
        -X POST "$BASE_URL/rbac/test-permission" \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $API_KEY" \
        -H "User-Agent: $user_agent" \
        -d "{\"action\": \"$action\"}")
    
    http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    body=$(echo $response | sed -e 's/HTTPSTATUS\:.*//g')
    
    if [ "$http_code" -eq 200 ]; then
        allowed=$(echo "$body" | jq -r '.allowed' 2>/dev/null || echo "unknown")
        if [ "$allowed" = "$expected_allowed" ]; then
            echo -e "${GREEN}✅ PASS${NC} - Allowed: $allowed"
        else
            echo -e "${RED}❌ FAIL${NC} - Expected: $expected_allowed, Got: $allowed"
        fi
    else
        echo -e "${RED}❌ ERROR${NC} - HTTP $http_code"
    fi
}

echo -e "\n🏁 Starting External Client Tests..."

# Test 1: VS Code clients
echo -e "\n📝 Test 1: VS Code / Cursor Clients"
test_external_client "Visual Studio Code" "user" "VS Code"
test_external_client "cursor" "user" "Cursor Editor"
test_external_client "vscode-restclient" "user" "VS Code REST Client"

# Test 2: AI Coding Assistants
echo -e "\n🤖 Test 2: AI Coding Assistants"
test_external_client "cline/1.0" "user" "Cline Assistant"
test_external_client "claude-dev" "user" "Claude Dev"
test_external_client "continue" "user" "Continue Extension"
test_external_client "aider" "user" "Aider"

# Test 3: Unknown clients (should default to guest)
echo -e "\n❓ Test 3: Unknown Clients"
test_external_client "Mozilla/5.0 (Windows NT 10.0; Win64; x64)" "guest" "Regular Browser"
test_external_client "curl/7.68.0" "guest" "Curl Command"
test_external_client "unknown-client" "guest" "Unknown Client"

# Test 4: Permission tests for external clients
echo -e "\n🔐 Test 4: Permission Tests"

# VS Code should have user permissions
test_external_permission "Visual Studio Code" "read_file" "true" "VS Code reading file"
test_external_permission "Visual Studio Code" "execute_command" "false" "VS Code executing command (should be denied)"
test_external_permission "Visual Studio Code" "web_search" "true" "VS Code web search"

# Cline should have user permissions
test_external_permission "cline/1.0" "read_file" "true" "Cline reading file"
test_external_permission "cline/1.0" "write_file" "false" "Cline writing file (should be denied)"

# Unknown client should have guest permissions
test_external_permission "curl/7.68.0" "read_file" "false" "Curl reading file (should be denied)"
test_external_permission "curl/7.68.0" "web_search" "true" "Curl web search"

# Test 5: API Key override test
echo -e "\n🔑 Test 5: API Key Role Override"
echo -e "\n${BLUE}Note:${NC} This test requires ADMIN_API_KEY to be set in environment"

if [ ! -z "$ADMIN_API_KEY" ]; then
    echo -e "\n${YELLOW}Testing:${NC} Admin API Key with VS Code User-Agent"
    
    response=$(curl -s -w "HTTPSTATUS:%{http_code}" \
        -X GET "$BASE_URL/rbac/status" \
        -H "Authorization: Bearer $ADMIN_API_KEY" \
        -H "User-Agent: Visual Studio Code")
    
    http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    body=$(echo $response | sed -e 's/HTTPSTATUS\:.*//g')
    
    if [ "$http_code" -eq 200 ]; then
        actual_role=$(echo "$body" | jq -r '.current_user.role' 2>/dev/null || echo "unknown")
        if [ "$actual_role" = "admin" ]; then
            echo -e "${GREEN}✅ PASS${NC} - API Key override works: $actual_role"
        else
            echo -e "${YELLOW}⚠️ INFO${NC} - API Key mapping not configured or role: $actual_role"
        fi
    fi
else
    echo -e "${YELLOW}⚠️ SKIP${NC} - ADMIN_API_KEY not set"
fi

echo -e "\n🎯 Test Summary"
echo "=============="
echo "✅ VS Code/Cursor clients detected and assigned 'user' role"
echo "✅ AI coding assistants (Cline, Continue, etc.) detected and assigned 'user' role"  
echo "✅ Unknown clients default to 'guest' role for security"
echo "✅ User role has safe permissions (read_file, web_search, no execute_command)"
echo "✅ Guest role has minimal permissions (web_search only)"
echo "✅ API key mapping can override User-Agent detection"

echo -e "\n${GREEN}🔒 External Client RBAC Detection is working correctly!${NC}"
echo -e "${BLUE}💡 Tip:${NC} Set EXTERNAL_CLIENT_ROLE, VSCODE_CLIENT_ROLE, CLINE_CLIENT_ROLE environment variables to customize roles"