# RBAC Security Guide - Gemini CLI Wrapper

## 🔒 Tổng quan

Hệ thống Role-Based Access Control (RBAC) đã được triển khai để bảo vệ Gemini CLI Wrapper khỏi việc thực thi các lệnh nguy hiểm bởi người dùng không có quyền. Chỉ có **admin accounts** trên Open WebUI mới có thể thực hiện các hành động có thể tác động lên VPS.

## 🎯 Mục tiêu bảo mật

- ✅ **Ngăn chặn truy cập trái phép**: Chỉ admin mới có thể thực thi lệnh nguy hiểm
- ✅ **Phân quyền chi tiết**: <PERSON>ác role khác nhau có quyền hạn khác nhau
- ✅ **Audit logging**: Ghi lại tất cả các hoạt động để theo dõi
- ✅ **Fail-safe design**: Mặc định từ chối nếu không có quyền

## 👥 Roles và Permissions

### 🔴 Admin Role
**Quyền hạn**: <PERSON><PERSON><PERSON> quyền truy cập hệ thống
```json
{
  "allowed_actions": [
    "execute_command",     // Thực thi lệnh shell
    "write_file",         // Ghi file
    "read_file",          // Đọc file
    "web_search",         // Tìm kiếm web
    "analyze_code",       // Phân tích code
    "store_memory",       // Lưu trữ memory
    "rbac_manage",        // Quản lý RBAC
    "openwebui_*",        // Tất cả Open WebUI tools
    "browser_*"           // Tất cả browser automation
  ]
}
```

### 🟡 User Role (Mặc định)
**Quyền hạn**: Các hoạt động an toàn
```json
{
  "allowed_actions": [
    "read_file",                    // Đọc file
    "web_search",                   // Tìm kiếm web
    "analyze_code",                 // Phân tích code
    "store_memory",                 // Lưu trữ memory
    "openwebui_web_search",         // Tìm kiếm qua Open WebUI
    "openwebui_get_models",         // Lấy danh sách models
    "openwebui_get_conversations",  // Lấy conversations
    "openwebui_create_chat"         // Tạo chat mới
  ]
}
```

### 🟢 Guest Role
**Quyền hạn**: Chỉ đọc cơ bản
```json
{
  "allowed_actions": [
    "web_search",               // Tìm kiếm web
    "openwebui_web_search",     // Tìm kiếm qua Open WebUI
    "openwebui_get_models"      // Lấy danh sách models
  ]
}
```

## ⚠️ Dangerous Actions

Các hành động sau được coi là **nguy hiểm** và chỉ admin mới được phép:

- `execute_command` - Thực thi lệnh shell
- `write_file` - Ghi file vào hệ thống
- `openwebui_send_message` - Gửi message đến chat khác
- `openwebui_upload_file` - Upload file
- `openwebui_browser_action` - Tương tác với browser
- `browser_*` - Tất cả browser automation

## 🔧 Cấu hình Role cho Open WebUI

### Phương pháp 1: Custom Header (Khuyến nghị)
Open WebUI có thể gửi role qua header:
```http
X-User-Role: admin
X-OpenWebUI-Role: admin
```

### Phương pháp 2: JWT Token
Role được nhúng trong JWT payload:
```json
{
  "role": "admin",
  "openwebui_role": "admin",
  "user_id": "user123",
  "exp": 1234567890
}
```

### Phương pháp 3: Session Data
Role lưu trong session:
```javascript
req.session.user_role = 'admin';
```

### Phương pháp 4: Query Parameter (Chỉ development)
```http
GET /api/endpoint?role=admin
```

## 🚀 Triển khai

### 1. Khởi động Server
```bash
# Server sẽ tự động tạo file rbac_policies.json với cấu hình mặc định
npm start
```

### 2. Kiểm tra RBAC Status
```bash
curl -X GET http://localhost:8010/rbac/status \
  -H "Authorization: Bearer your-api-key" \
  -H "X-User-Role: admin"
```

### 3. Test Permission
```bash
curl -X POST http://localhost:8010/rbac/test-permission \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -H "X-User-Role: user" \
  -d '{"action": "execute_command"}'
```

## 🔍 API Endpoints

### GET `/rbac/status`
Kiểm tra trạng thái RBAC và quyền của user hiện tại
```json
{
  "success": true,
  "current_user": {
    "role": "user",
    "permissions": ["read_file", "web_search", ...],
    "description": "Safe operations only"
  },
  "available_roles": ["admin", "user", "guest"],
  "dangerous_actions": ["execute_command", "write_file", ...],
  "rbac_enabled": true
}
```

### GET `/rbac/policies` (Admin only)
Xem tất cả policies
```json
{
  "success": true,
  "policies": {
    "admin": { "allowed_actions": [...] },
    "user": { "allowed_actions": [...] },
    "guest": { "allowed_actions": [...] }
  }
}
```

### POST `/rbac/test-permission`
Test quyền cho một action cụ thể
```json
{
  "action": "execute_command",
  "role": "user"  // Optional, defaults to current user role
}
```

## 🛡️ Bảo mật

### Authentication Enhancements
- ✅ **Timing-safe comparison**: Chống timing attacks
- ✅ **Production validation**: Bắt buộc API key trong production
- ✅ **Generic error messages**: Không tiết lộ thông tin nhạy cảm
- ✅ **Enhanced logging**: Ghi lại chi tiết các attempt

### Authorization Features
- ✅ **Default deny**: Mặc định từ chối nếu không có quyền
- ✅ **Wildcard support**: Hỗ trợ pattern matching (openwebui_*)
- ✅ **Audit logging**: Ghi lại tất cả access attempts
- ✅ **Dangerous action detection**: Cảnh báo đặc biệt cho hành động nguy hiểm

## 📊 Monitoring và Logging

### Log Levels
- **INFO**: Successful authorizations
- **WARN**: Access denied, dangerous actions
- **ERROR**: System errors, configuration issues
- **DEBUG**: Detailed RBAC operations

### Log Format
```json
{
  "level": "warn",
  "message": "❌ RBAC: user denied access to tool 'execute_command'",
  "user_role": "user",
  "action": "execute_command",
  "allowed": false,
  "ip": "*************",
  "timestamp": "2025-01-09T03:45:00.000Z"
}
```

## 🔧 Troubleshooting

### Vấn đề: User không có quyền thực thi lệnh
**Giải pháp**: 
1. Kiểm tra role của user: `GET /rbac/status`
2. Xác nhận Open WebUI gửi đúng role header
3. Kiểm tra logs để xem chi tiết lỗi

### Vấn đề: Admin không được nhận diện
**Giải pháp**:
1. Đảm bảo Open WebUI gửi header `X-User-Role: admin`
2. Hoặc cấu hình JWT với claim `role: admin`
3. Kiểm tra authentication middleware

### Vấn đề: Tất cả requests bị từ chối
**Giải pháp**:
1. Kiểm tra file `rbac_policies.json` tồn tại
2. Xác nhận RBAC middleware được load
3. Restart server để reload policies

## 📝 Configuration File

File `rbac_policies.json` sẽ được tự động tạo:
```json
{
  "policies": {
    "admin": {
      "allowed_actions": ["execute_command", "write_file", ...],
      "description": "Full system access"
    },
    "user": {
      "allowed_actions": ["read_file", "web_search", ...],
      "description": "Safe operations only"
    },
    "guest": {
      "allowed_actions": ["web_search", ...],
      "description": "Very limited access"
    }
  },
  "metadata": {
    "created": "2025-01-09T03:45:00.000Z",
    "version": "1.0.0",
    "description": "RBAC policies for Gemini CLI Wrapper"
  }
}
```

## 🎯 Best Practices

1. **Luôn sử dụng HTTPS** trong production
2. **Rotate API keys** định kỳ
3. **Monitor logs** để phát hiện hoạt động bất thường
4. **Test permissions** trước khi deploy
5. **Backup RBAC policies** trước khi thay đổi
6. **Sử dụng least privilege principle**

## 🚨 Emergency Procedures

### Tắt RBAC tạm thời (Emergency only)
```bash
# Set environment variable to disable RBAC
export DISABLE_RBAC=true
npm restart
```

### Reset về default policies
```bash
# Delete config file to regenerate defaults
rm rbac_policies.json
npm restart
```

---

**Lưu ý**: Hệ thống RBAC này đảm bảo chỉ admin accounts trên Open WebUI mới có thể thực hiện các lệnh có thể tác động lên VPS, đáp ứng đúng yêu cầu bảo mật của bạn.