# Open WebUI Localhost Issue Fix

## Problem Identified

From the screenshot, I can see that Open WebUI is configured with:
- URL: `http://localhost:8010/v1`
- Open WebUI is running on: `**************:3000`

**The issue**: When Open WebUI runs on an external IP (`**************`) and tries to connect to `localhost:8010`, it's looking for the API on the same machine where the browser is running, not on your server.

## Solution

You need to change the API URL in Open WebUI from:
```
http://localhost:8010/v1
```

To one of these options:

### Option 1: External IP (Recommended)
```
http://**************:8010/v1
```

### Option 2: Internal VPS IP (if both services are on same server)
```
http://**********:8010/v1
```

## Steps to Fix

1. In Open WebUI, go to **Settings** > **Connections**
2. Find the connection with `http://localhost:8010/v1`
3. Click the edit button (⚙️)
4. Change the URL to `http://**************:8010/v1`
5. Click **Save**
6. Test the connection

## Why This Happens

- `localhost` always refers to the current machine
- When Open WebUI runs on `**************`, `localhost` refers to that IP
- Your Gemini CLI Wrapper runs on the same server but needs to be accessed via the server's IP
- The browser needs the full server IP to make the connection

## Verification

After making the change, you should be able to:
1. See the connection status change from error to working
2. Select models like `gemini-2.5-flash` and `gemini-2.5-pro`
3. Start chatting with the AI

The Gemini CLI Wrapper is running correctly and responding to API calls - the issue was just the URL configuration in Open WebUI.