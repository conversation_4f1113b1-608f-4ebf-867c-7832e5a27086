# Gemini CLI Wrapper Environment Configuration
# Generated on [DATE]

# Basic Configuration
PORT=8010
CLAUDE_PORT=8011
NODE_ENV=development

# API Keys for AI Services
GEMINI_API_KEY=your-gemini-api-key-here
CLAUDE_API_KEY=your-claude-api-key-here

# RBAC API Keys - KEEP THESE SECURE!
ADMIN_API_KEY=your-secure-admin-key-here
USER_API_KEY=your-secure-user-key-here
GUEST_API_KEY=your-secure-guest-key-here

# External Client Default Roles
EXTERNAL_CLIENT_ROLE=user
VSCODE_CLIENT_ROLE=user
CLINE_CLIENT_ROLE=user

# API Key to Role Mappings (JSON format)
API_KEY_ROLE_MAPPINGS={"your-secure-admin-key-here":"admin","your-secure-user-key-here":"user","your-secure-guest-key-here":"guest"}

# Security Settings
GEMINI_UNRESTRICTED_MODE=false

# Open WebUI Integration
OPENWEBUI_BASE_URL=http://localhost:3000
OPENWEBUI_API_KEY=your-openwebui-api-key

# OpenAI API Security Configuration
# Set to 'true' to require API key authentication for OpenAI endpoints (/v1/*)
# Set to 'false' to allow Open WebUI to access without authentication (default)
REQUIRE_OPENAI_AUTH=true

# Logging Level
LOG_LEVEL=info

# ===== PHASE 1 IMPROVEMENTS (gemini-cli-openai inspired) =====

# Real Thinking Support (Native Gemini Reasoning)
ENABLE_REAL_THINKING=true
ENABLE_FAKE_THINKING=false
STREAM_THINKING_AS_CONTENT=true
DEFAULT_THINKING_BUDGET=-1

# Content Safety Settings (Gemini Moderation)
GEMINI_ENABLE_SAFETY=true
GEMINI_STRICT_MODE=false
GEMINI_MODERATION_HARASSMENT_THRESHOLD=BLOCK_SOME
GEMINI_MODERATION_HATE_SPEECH_THRESHOLD=BLOCK_SOME
GEMINI_MODERATION_SEXUALLY_EXPLICIT_THRESHOLD=BLOCK_SOME
GEMINI_MODERATION_DANGEROUS_CONTENT_THRESHOLD=BLOCK_ONLY_HIGH
GEMINI_CUSTOM_SAFETY_RULES=[]

# Enhanced Token Management
TOKEN_CACHE_MAX_RETRIES=3
TOKEN_CACHE_RETRY_DELAY=1000
TOKEN_CACHE_EXPIRY_BUFFER=300000
ENABLE_DISTRIBUTED_TOKEN_CACHE=false
REDIS_URL=redis://localhost:6379
TOKEN_CACHE_BACKUP_INTERVAL=3600000

# Google OAuth Configuration (for token refresh)
GOOGLE_CLIENT_ID=77185425430.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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